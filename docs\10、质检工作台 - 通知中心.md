
---

### 页面八：通知中心 (FinalNotificationCenterPage.tsx)

#### 1. 核心定位与目标
该页面是系统内所有**消息和通知的聚合中心**。其核心目标是为所有角色的用户提供一个统一的、可管理的界面，来接收、查看和处理与他们工作相关的各类通知，确保关键信息不会被遗漏，并支持对历史消息的追溯。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "通知中心"
*   **副标题**: "查看和管理系统通知消息"
*   **图标**: `Bell` (铃铛图标)，是通知功能的标准象征。
*   **徽章**: 动态显示**未读消息的总数**，例如“未读 5”，用醒目的红色徽章提醒用户。
*   **快捷操作**:
    *   `全部已读`: 一键将所有未读通知标记为已读。
    *   `清空通知`: 一键删除所有（或当前筛选出的）通知。

**b. 整体布局 (双栏布局)**
页面采用经典的邮箱式左右双栏布局，左侧为分类导航，右侧为通知列表和内容。

*   **左侧 - 通知分类导航**:
    *   **目的**: 帮助用户快速过滤不同类型的通知。
    *   **分类项**:
        *   `全部通知`
        *   `任务提醒`: 如新的复核任务、待处理的申诉。
        *   `结果通知`: 如质检成绩发布、申诉处理结果。
        *   `系统公告`: 如节假日通知、系统维护公告。
    *   **特点**: 每个分类旁边都用数字角标显示该分类下的**未读通知数量**，让用户对各类新消息一目了然。当前选中的分类会有高亮显示。

*   **右侧 - 主内容区**:
    *   **目的**: 展示通知列表，并提供筛选和批量操作功能。

**c. 右侧主内容区详解**

*   **统一搜索筛选器 (`UnifiedSearchFilter`)**:
    *   **功能**: 在当前分类下，对通知进行进一步的精确查找。
    *   **筛选字段**:
        *   `搜索内容`: 对通知的标题和内容进行全文关键字搜索。
        *   `读取状态`: 筛选“全部”、“已读”或“未读”的通知。
        *   `时间范围`: 按通知的接收时间进行筛选。

*   **批量操作栏**:
    *   **位置**: 位于筛选器下方，通知列表上方。
    *   **功能**: 当用户通过复选框选中一条或多条通知后，这里会激活相应的批量操作按钮。
        *   `全选`: 勾选/取消勾选当前页的所有通知。
        *   `标为已读`: 将选中的通知标记为已读。
        *   `标为未读`: 将选中的通知标记为未读。
        *   `删除选中`: 删除所有选中的通知。

*   **通知列表 (`List`)**:
    *   **布局**: 每一条通知都是一个独立的卡片或列表项。
    *   **单条通知包含元素**:
        *   **复选框**: 用于批量选择。
        *   **已读/未读状态点**: 通常是一个小圆点，蓝色代表未读，灰色代表已读，非常直观。
        *   **通知类型徽章**: 用不同颜色的徽章标明通知类型（任务提醒、结果通知等）。
        *   **标题和内容**: 清晰展示通知的核心信息。
        *   **时间戳**: 显示通知的接收时间。
        *   **快捷操作**: 每条通知右侧都有独立的“标记为已读/未读”和“删除”按钮。
    *   **交互**:
        *   点击通知的标题或内容区域，会执行两个动作：1) 将该通知自动标记为已读；2) 如果通知有关联的`actionUrl`，则会**跳转到对应的详情页**（例如，点击“新的复核任务”通知会跳转到“我的复核任务”页面）。
        *   未读通知通常有更醒目的背景色或字体样式（如加粗），以示区别。

*   **分页组件 (`UnifiedPagination`)**:
    *   位于列表底部，用于浏览大量的历史通知。

#### 3. 核心交互与操作
*   **信息聚合与分发**: 将系统中所有需要触达用户的消息统一管理，并通过分类和筛选让用户高效处理。
*   **任务驱动跳转**: 很多通知（特别是任务提醒类）都是一个**工作流的起点**。用户通过点击通知，可以直接跳转到需要他操作的页面，极大提升了工作效率。
*   **状态管理**: 用户可以清晰地管理每条通知的“已读/未读”状态，确保不会遗漏重要信息，同时也能保持收件箱的整洁。
*   **批量处理**: 提供了高效的批量操作能力，方便用户快速清理或标记大量通知。

---