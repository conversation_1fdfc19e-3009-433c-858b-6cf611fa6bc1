import React from 'react';
import { motion } from 'framer-motion';
import { Lightbulb, BookOpen, BarChart2, Cpu } from 'lucide-react';

/**
 * @description 根据开发规范重构核心概念模块
 * 采用四卡片布局，分别展示功能介绍、应用场景、优势特点和检测原理。
 */
const LargeModelCheckConcepts: React.FC = () => {
  const concepts = [
    {
      icon: <BookOpen className="w-8 h-8 text-blue-600" />,
      title: '功能介绍',
      description: '大模型质检规则是基于深度学习的语义理解能力，针对对话语义的质检方案。您只需提供质检场景的检测维度和描述，即可完成复杂的质检需求，无需编写复杂的规则逻辑。',
      bg: 'bg-gradient-to-r from-blue-50 to-indigo-50',
      border: 'border-blue-100',
      iconBg: 'bg-blue-100',
    },
    {
      icon: <BarChart2 className="w-8 h-8 text-green-600" />,
      title: '应用场景',
      description: '适用于需要深度语义理解的复杂场景，如：金融合规性检查（是否揭示风险）、服务质量评估（是否主动关怀）、电商售后处理（是否清晰说明流程）等。',
      bg: 'bg-gradient-to-r from-green-50 to-emerald-50',
      border: 'border-green-100',
      iconBg: 'bg-green-100',
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-purple-600" />,
      title: '优势特点',
      description: '核心优势在于强大的泛化能力和对复杂语义的理解力。能够轻松处理同义词、不同句式、反讽、暗示等传统规则难以覆盖的场景，大幅提升质检的准确率和召回率。',
      bg: 'bg-gradient-to-r from-purple-50 to-pink-50',
      border: 'border-purple-100',
      iconBg: 'bg-purple-100',
    },
    {
      icon: <Cpu className="w-8 h-8 text-orange-600" />,
      title: '检测原理',
      description: '算子不依赖固定的关键词或正则。它通过理解您提供的"场景名称"、"背景知识"和一系列"检测维度"，形成对特定对话场景的认知。检测时，模型会在语义层面判断对话内容是否命中了您定义的维度。',
      bg: 'bg-gradient-to-r from-orange-50 to-red-50',
      border: 'border-orange-100',
      iconBg: 'bg-orange-100',
    },
  ];

  return (
    <div className="p-6 bg-white rounded-lg border border-gray-200">
      <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
        <span className="text-2xl mr-2">💡</span>
        核心概念
      </h2>
      <div className="space-y-4">
        {concepts.map((concept, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`p-4 rounded-lg border ${concept.border} ${concept.bg}`}
          >
            <div className="flex items-start space-x-3">
              <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${concept.iconBg}`}>
                {concept.icon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">{concept.title}</h3>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {concept.description}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default LargeModelCheckConcepts; 