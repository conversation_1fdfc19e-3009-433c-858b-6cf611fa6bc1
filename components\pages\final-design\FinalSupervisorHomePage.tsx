import React, { useState } from 'react';
import { Card, Button, Typography, Space, Badge, Table } from 'antd';
import { Button as CustomButton } from '../../ui/button';
import { 
  Users, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle, 
  CheckCircle,
  Clock,
  Phone,
  Award,
  Target,
  BarChart3,
  Calendar,
  Bell,
  FileText,
  Shield,
  XCircle,
  MessageSquare,
  Brain,
  FileCheck,
  UserCheck,
  ChevronRight,
  HelpCircle
} from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';
import { MetricCard } from './common/MetricCard';
import { MetricDescription } from './common/MetricHelpButton';
import { GlobalStyles } from './common/GlobalStyles';

const { Text } = Typography;

/**
 * 质检主管首页组件
 * 展示质检主管视角的绩效指标、趋势分析、通知公告等信息
 */

// 质检主管页面设计说明 (业务角度)
const designGuideContent = (
  <div className="space-y-4 text-gray-700 text-sm leading-relaxed">
    <h2 className="text-xl font-bold text-gray-800">页面：质检主管视角 (FinalSupervisorHomePage.tsx) - 业务设计说明</h2>

    <h3 className="text-lg font-semibold text-gray-700 mt-4">1. 核心定位与目标</h3>
    <p>
      质检主管首页是为<b>质检主管或拥有最高管理权限的用户</b>量身打造的全局业务仪表盘。其核心目标是提供一个<b>360度的宏观业务视图</b>，使管理者能够全面、高效地监控质检体系的<b>整体健康度、运营效率、服务质量和潜在风险</b>。本页面旨在为质检部门的战略决策、资源优化配置以及流程持续改进提供高级别的数据支持和洞察。
    </p>

    <h3 className="text-lg font-semibold text-gray-700 mt-4">2. 主要功能模块与内容</h3>

    <h4 className="text-md font-semibold text-gray-700 mt-3">a. 页面顶部区域</h4>
    <p>此区域用于提供质检主管的个性化欢迎信息和核心角色标识，确保用户快速进入管理工作状态：</p>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>标题</b>: "质检主管视角"，清晰界定当前页面的管理层级和功能范畴。</li>
      <li><b>副标题</b>: 动态展示主管的姓名、所属部门及当前管理的团队数量，例如："欢迎回来，李主管 | 质检部 | 管理团队：3个"，提供即时、相关的上下文信息。</li>
      <li><b>角色标识</b>: 通过盾牌图标及"主管视角"徽章，直观地象征质检主管在质量保障和风险管控方面的核心职责。</li>
    </ul>

    <h4 className="text-md font-semibold text-gray-700 mt-3">b. 核心绩效指标（整体运营概览）</h4>
    <p>
      该模块以醒目的卡片形式展示了<b>整个质检体系最高维度的宏观关键绩效指标（KPI）</b>。这些数据是对所有团队和坐席表现的逻辑聚合，旨在提供质检运营的整体健康度和效率概览。数据计算和实现方式如下：
    </p>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>第一行：运营规模与效率指标</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>总质检量</b>:
            <p>内容：系统在特定业务周期内（如本周、本月）完成的全部质检任务的通话总数。</p>
            <p>计算/实现：系统自动汇总并统计所有AI质检和人工质检完成的会话记录数量。</p>
          </li>
          <li><b>总复核量</b>:
            <p>内容：在相同业务周期内，所有复核员完成的人工审核或复议任务的总数。</p>
            <p>计算/实现：统计复核员在复核任务队列中标记为"已完成"的复核记录总量。</p>
          </li>
          <li><b>质检平均分</b>:
            <p>内容：特定业务周期内，所有被质检会话的平均得分，是衡量整体服务质量的核心基线。</p>
            <p>计算/实现：<code>所有已完成质检会话的得分总和 / 已完成质检会话的总次数</code>。</p>
          </li>
          <li><b>质检合格率</b>:
            <p>内容：特定业务周期内，质检结果达到"合格"标准的任务占总质检任务的百分比。</p>
            <p>计算/实现：<code>(质检结果为"合格"的会话总数 / 已完成质检会话总数) × 100%</code>。</p>
          </li>
        </ul>
      </li>
      <li><b>第二行：风险与AI效能指标</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>申诉率</b>:
            <p>内容：坐席员对质检结果发起申诉的次数占总质检次数的百分比，反映质检结果的公信力。</p>
            <p>计算/实现：<code>(特定周期内发起的总申诉次数 / 已完成质检会话总数) × 100%</code>。</p>
          </li>
          <li><b>申诉成功率</b>:
            <p>内容：申诉成功（即原质检结果被更改）的次数占总申诉次数的百分比。</p>
            <p>计算/实现：<code>(特定周期内申诉成功的总次数 / 发起的总申诉次数) × 100%</code>。高成功率可能指向AI质检或人工复核标准存在问题，需要深入分析。</p>
          </li>
          <li><b>AI质检准确率</b>:
            <p>内容：衡量AI自动质检结果的可靠性，即AI的判断与人工复核最终结论的一致性。这是评估AI引擎价值的核心指标。</p>
            <p>计算/实现：<code>(AI质检结果与人工复核结果一致的次数 / 所有被人工复核的AI质检总次数) × 100%</code>，直接关系到系统自动化质检的可靠性和投入产出比。</p>
          </li>
        </ul>
      </li>
    </ul>

    <h4 className="text-md font-semibold text-gray-700 mt-3">c. 成绩趋势分析（折线图）</h4>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>图表标题</b>: "成绩趋势分析"。</li>
      <li><b>内容</b>: 通过组合折线图展示近期<b>整体平均分</b>和<b>整体合格率</b>的变化趋势。</li>
      <li><b>业务价值与实现</b>: 质检主管可宏观监控服务质量的波动情况，结合公司层面的政策调整、市场活动或重大事件进行关联分析，以便及时发现并解决潜在问题。数据来源于每日/每周的质检结果汇总，系统自动绘制趋势图。</li>
    </ul>

    <h4 className="text-md font-semibold text-gray-700 mt-3">d. 排名统计（数据表格）</h4>
    <p>此模块通过数据表格形式，用于横向对比不同团队和个人的质检表现，旨在发现标杆团队和需改进的个体。</p>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>左侧卡片 - 团队成绩排名 Top5</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>标题</b>: "团队成绩排名 Top5"。</li>
          <li><b>内容与实现</b>: 清晰展示表现最佳的前5个班组的各项关键指标，包括团队名称、平均分、合格率及成员数量。数据由系统根据各团队成员的质检结果聚合计算并排序。</li>
        </ul>
      </li>
      <li><b>右侧卡片 - 坐席成绩排名 Top5</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>标题</b>: "坐席成绩排名 Top5"。</li>
          <li><b>内容与实现</b>: 展示全公司范围内表现最优秀的5名坐席，并标注其所属团队。数据由系统统计所有坐席的质检得分和合格情况并进行排名。此模块为跨团队的优秀经验分享和坐席激励提供了业务依据。</li>
        </ul>
      </li>
    </ul>

    <h4 className="text-md font-semibold text-gray-700 mt-3">e. 错误分析（双卡片布局）</h4>
    <p>与班组长视角类似，但此模块的数据统计范围是<b>全公司</b>层面，旨在帮助质检主管发现全局性、系统性存在的服务风险点和普遍的业务问题。</p>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>左侧卡片 - 严重错误项 Top5</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>标题</b>: "严重错误项 Top5"。</li>
          <li><b>内容与实现</b>: 统计在所有通话中被AI或人工质检发现，且触犯次数最多的严重违规项。这些是公司层面的核心风险点，需要质检主管重点关注并推动相关部门进行整改和培训。数据由质检规则命中情况汇总统计。</li>
        </ul>
      </li>
      <li><b>右侧卡片 - 高频失分项 Top5</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>标题</b>: "高频失分项 Top5"。</li>
          <li><b>内容与实现</b>: 统计导致所有坐席累计失分最多的规则项。这些规则往往直接指向了公司级培训材料、业务SOP（标准作业程序）或坐席操作规范中需要进一步优化和强化的方向。数据来源于质检扣分项的统计聚合。</li>
        </ul>
      </li>
    </ul>

    <h4 className="text-md font-semibold text-gray-700 mt-3">f. 通知公告与待处理申诉（双卡片布局）</h4>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>左侧卡片 - 通知公告</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>标题</b>: "通知公告"。</li>
          <li><b>内容与实现</b>: 展示最高优先级的系统通知、质检标准重大更新、或需要主管特别关注的任务预警（例如，申诉案件积压过多）。这些信息由系统后台推送或由管理人员发布，旨在确保关键信息能够及时传达给主管。</li>
        </ul>
      </li>
      <li><b>右侧卡片 - 待处理申诉</b>:
        <ul className="list-[circle] pl-6 space-y-1">
          <li><b>标题</b>: "待处理申诉"。</li>
          <li><b>内容</b>: 这是一个为质检主管定制的<b>高优先级待办列表</b>。它展示了最新或最紧急的待处理申诉案件，每条记录包含简要信息，如记录ID、申诉坐席、AI得分、申诉理由等。</li>
          <li><b>交互与实现</b>: 每条记录末尾都提供一个"开始处理"按钮，点击后主管可以直接跳转到对应的申诉处理页面或会话详情页。此功能旨在方便主管快速响应、介入和处理高优先级的业务问题，实现从"发现问题"到"解决问题"的闭环管理。数据来源于申诉管理模块中状态为"待处理"的申诉记录。</li>
        </ul>
      </li>
    </ul>

    <h3 className="text-lg font-semibold text-gray-700 mt-4">3. 核心交互与操作</h3>
    <ul className="list-disc pl-6 space-y-1">
      <li><b>宏观监控</b>: 页面所有数据均为全局聚合数据，为管理者提供"一屏看全貌"的驾驶舱体验，支持高效的业务洞察。</li>
      <li><b>决策支持</b>: 所有模块都旨在回答质检主管最核心的业务问题：我们的服务质量整体表现如何？主要风险点在哪里？哪些团队和坐席表现突出？AI质检的可靠性如何？这些信息直接支持管理层做出基于数据的决策。</li>
      <li><b>管理入口</b>: 页面各模块（如排名、错误项、待处理申诉等）提供无缝跳转到更详细的分析报表或具体处理页面的入口，实现了从"发现业务问题"到"深入分析问题"再到"推动解决问题"的流畅业务管理闭环。</li>
    </ul>
  </div>
);

// 质检主管指标说明
const supervisorMetricDescriptions: Record<string, MetricDescription> = {
  totalQcCount: {
    title: "总质检量",
    description: "在特定时间段内，整个质检体系完成的质检任务总数。",
    calculation: "汇总周期内所有已完成的质检记录数量。"
  },
  totalReviewCount: {
    title: "总复核量",
    description: "在特定时间段内，质检团队（复核员）完成的人工审核或复议任务的总数。",
    calculation: "汇总周期内所有由复核员完成的复核任务数量。"
  },
  averageScore: {
    title: "质检平均分",
    description: "在特定时间段内，所有被质检的会话的平均得分。",
    calculation: "质检平均分 = 周期内所有质检项的得分总和 / 周期内已完成的质检总次数"
  },
  qualificationRate: {
    title: "质检合格率",
    description: `在特定时间段内，质检结果达到"合格"标准的任务占总质检任务的百分比。`,
    calculation: "质检合格率 = ( 周期内质检合格的总次数 / 周期内已完成的质检总次数 ) × 100%"
  },
  appealRate: {
    title: "申诉率",
    description: "坐席员对质检结果发起申诉的次数占总质检次数的百分比。",
    calculation: "申诉率 = ( 周期内发起的总申诉次数 / 周期内已完成的质检总次数 ) × 100%"
  },
  appealSuccessRate: {
    title: "申诉成功率",
    description: "申诉成功（即质检结果被更改）的次数占总申诉次数的百分比。",
    calculation: "申诉成功率 = ( 周期内申诉成功的总次数 / 周期内发起的总申诉次数 ) × 100%"
  },
  aiAccuracyRate: {
    title: "AI质检准确率",
    description: "衡量AI自动质检结果的可靠性，即AI的判断与人工复核最终结论的一致性。",
    calculation: "AI质检准确率 = ( AI质检结果与人工复核结果一致的次数 / 所有被人工复核的AI质检总次数 )×100%"
  }
};

// 模拟数据
const performanceData = {
  totalQcCount: 2450,
  totalReviewCount: 486,
  averageScore: 86.7,
  qualificationRate: 89.3,
  appealRate: 3.2,
  appealSuccessRate: 45.8,
  aiAccuracyRate: 94.2
};

const trendData = [
  { date: '01-15', avgScore: 85.2, qualificationRate: 87.5 },
  { date: '01-16', avgScore: 86.1, qualificationRate: 88.2 },
  { date: '01-17', avgScore: 85.8, qualificationRate: 89.1 },
  { date: '01-18', avgScore: 87.3, qualificationRate: 90.3 },
  { date: '01-19', avgScore: 86.7, qualificationRate: 89.8 },
  { date: '01-20', avgScore: 88.1, qualificationRate: 91.2 },
  { date: '01-21', avgScore: 86.9, qualificationRate: 89.5 }
];

const teamRanking = [
  { rank: 1, team: 'A班组', avgScore: 91.2, qualificationRate: 95.8, memberCount: 15 },
  { rank: 2, team: 'B班组', avgScore: 88.7, qualificationRate: 92.3, memberCount: 12 },
  { rank: 3, team: 'C班组', avgScore: 85.9, qualificationRate: 89.1, memberCount: 18 },
  { rank: 4, team: 'D班组', avgScore: 83.4, qualificationRate: 86.7, memberCount: 14 },
  { rank: 5, team: 'E班组', avgScore: 81.8, qualificationRate: 84.2, memberCount: 16 }
];

const agentRanking = [
  { rank: 1, name: '李明', team: 'A班组', avgScore: 95.2, qualificationRate: 98.5 },
  { rank: 2, name: '王芳', team: 'B班组', avgScore: 94.1, qualificationRate: 97.8 },
  { rank: 3, name: '张伟', team: 'A班组', avgScore: 92.8, qualificationRate: 96.3 },
  { rank: 4, name: '刘洋', team: 'C班组', avgScore: 91.5, qualificationRate: 95.7 },
  { rank: 5, name: '陈静', team: 'B班组', avgScore: 90.9, qualificationRate: 94.2 }
];

const seriousErrors = [
  { name: '未核实客户身份', count: 25, severity: 'critical' },
  { name: '违规承诺退款', count: 18, severity: 'critical' },
  { name: '泄露客户隐私信息', count: 12, severity: 'critical' },
  { name: '未经授权查询', count: 10, severity: 'high' },
  { name: '违规操作系统权限', count: 8, severity: 'high' },
  { name: '恶意修改客户信息', count: 6, severity: 'critical' },
  { name: '未做风险提示', count: 5, severity: 'medium' },
  { name: '违规转接业务', count: 4, severity: 'medium' },
  { name: '擅自离岗', count: 3, severity: 'high' },
  { name: '违规录音', count: 2, severity: 'critical' }
];

const frequentDeductions = [
  { name: '服务态度不佳', count: 145, points: -290 },
  { name: '未主动询问需求', count: 128, points: -256 },
  { name: '话术不规范', count: 98, points: -196 },
  { name: '响应时间过长', count: 87, points: -174 },
  { name: '未做总结确认', count: 76, points: -152 },
  { name: '业务知识错误', count: 65, points: -195 },
  { name: '操作流程违规', count: 54, points: -162 },
  { name: '沟通效率低下', count: 43, points: -86 },
  { name: '未进行回访确认', count: 38, points: -76 },
  { name: '记录信息不完整', count: 32, points: -64 }
];

// 统一通知接口定义
interface Notification {
  id: number;
  type: 'system' | 'task' | 'announcement' | 'result' | 'appeal';
  title: string;
  content: string;
  time: string;
  isRead: boolean;
  priority: 'high' | 'medium' | 'low';
}

const notifications: Notification[] = [
  {
    id: 1,
    type: 'task',
    title: '申诉处理提醒',
    content: '您有3个申诉案件待处理，其中2个已超过24小时，请及时处理',
    time: '30分钟前',
    isRead: false,
    priority: 'high'
  },
  {
    id: 2,
    type: 'task',
    title: '申诉处理任务分配',
    content: '本周新增申诉案件15个，已分配给相关复核员，请跟进处理进度',
    time: '2小时前',
    isRead: false,
    priority: 'medium'
  },
  {
    id: 3,
    type: 'system',
    title: '系统公告',
    content: '关于五一假期排班及调休的通知已发布，请协调各团队安排',
    time: '1天前',
    isRead: true,
    priority: 'medium'
  },
  {
    id: 4,
    type: 'system',
    title: '质检标准更新通知',
    content: '客户满意度评价标准已更新，请组织各团队学习新标准',
    time: '2天前',
    isRead: true,
    priority: 'low'
  }
];

const pendingAppeals = [
  {
    id: 'AP2507210001',
    recordId: 'QM2507210001',
    agent: '张三',
    team: 'A班组',
    aiScore: 75.5,
    appealReason: '客户情绪激动，处理方式符合规范',
    appealTime: '2025-01-21 09:30:00'
  },
  {
    id: 'AP2507210002',
    recordId: 'QM2507200085',
    agent: '李四',
    team: 'B班组',
    aiScore: 68.0,
    appealReason: '系统故障导致操作延迟，非人为因素',
    appealTime: '2025-01-21 14:15:00'
  },
  {
    id: 'AP2507200003',
    recordId: 'QM2507190156',
    agent: '王五',
    team: 'C班组',
    aiScore: 72.5,
    appealReason: '客户要求特殊处理，已按流程操作',
    appealTime: '2025-01-20 16:45:00'
  }
];

const FinalSupervisorHomePage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 获取通知优先级样式
  const getNotificationPriorityStyle = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500 bg-red-50';
      case 'medium':
        return 'border-l-4 border-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-l-4 border-blue-500 bg-blue-50';
      default:
        return '';
    }
  };

  const teamColumns = [
    { title: '排名', dataIndex: 'rank', key: 'rank', width: 60 },
    { title: '班组', dataIndex: 'team', key: 'team' },
    { title: '平均分', dataIndex: 'avgScore', key: 'avgScore', render: (score: number) => <span className={getScoreColor(score)}>{score}</span> },
    { title: '合格率', dataIndex: 'qualificationRate', key: 'qualificationRate', render: (rate: number) => `${rate}%` },
    { title: '成员数', dataIndex: 'memberCount', key: 'memberCount' }
  ];

  const agentColumns = [
    { title: '排名', dataIndex: 'rank', key: 'rank', width: 60 },
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '班组', dataIndex: 'team', key: 'team' },
    { title: '平均分', dataIndex: 'avgScore', key: 'avgScore', render: (score: number) => <span className={getScoreColor(score)}>{score}</span> },
    { title: '合格率', dataIndex: 'qualificationRate', key: 'qualificationRate', render: (rate: number) => `${rate}%` }
  ];

  return (
    <div className="unified-page-container">
      <GlobalStyles />
      <UnifiedPageHeader
        title="质检主管视角"
        subtitle="欢迎回来，李主管 | 质检部 | 管理团队：3个"
        icon={Shield}
        badge={{ text: "主管视角", color: "purple" }}
        showDesignGuide={true}
        designGuideContent={designGuideContent}
        actions={[
          // {
          //   label: "本周数据",
          //   onClick: () => setSelectedPeriod('week'),
          //   icon: Calendar,
          //   variant: "outline",
          //   size: "sm"
          // }
        ]}
      />

      {/* 主要内容区域 */}
      <div className="unified-content-area space-y-6">

      {/* 核心绩效指标 */}
      <div className="space-y-6">
        {/* 第一行：4个指标 */}
        <div className="unified-metric-grid unified-metric-grid-4">
          <MetricCard title="总质检量" value={performanceData.totalQcCount.toLocaleString()} icon={FileCheck} iconColor="text-blue-600" iconBgColor="bg-blue-100" valueColor="!text-blue-600" trend={{type: 'up', value: '+8.5%', text: '较上周 +8.5%'}} helpInfo={supervisorMetricDescriptions.totalQcCount} />
          <MetricCard title="总复核量" value={performanceData.totalReviewCount} icon={UserCheck} iconColor="text-purple-600" iconBgColor="bg-purple-100" valueColor="!text-purple-600" trend={{type: 'up', value: '+12.3%', text: '较上周 +12.3%'}} helpInfo={supervisorMetricDescriptions.totalReviewCount} />
          <MetricCard title="质检平均分" value={performanceData.averageScore} icon={Award} iconColor="text-green-600" iconBgColor="bg-green-100" valueColor="!text-green-600" trend={{type: 'up', value: '+1.2', text: '较上周 +1.2'}} helpInfo={supervisorMetricDescriptions.averageScore} />
          <MetricCard title="质检合格率" value={`${performanceData.qualificationRate}%`} icon={CheckCircle} iconColor="text-orange-600" iconBgColor="bg-orange-100" valueColor="!text-orange-600" trend={{type: 'up', value: '+2.1%', text: '较上周 +2.1%'}} helpInfo={supervisorMetricDescriptions.qualificationRate} />
        </div>
        
        {/* 第二行：3个指标 */}
        <div className="unified-metric-grid unified-metric-grid-3">
          <MetricCard title="申诉率" value={`${performanceData.appealRate}%`} icon={MessageSquare} iconColor="text-red-600" iconBgColor="bg-red-100" valueColor="!text-red-600" trend={{type: 'down', value: '-0.5%', text: '较上周 -0.5%'}} helpInfo={supervisorMetricDescriptions.appealRate} />
          <MetricCard title="申诉成功率" value={`${performanceData.appealSuccessRate}%`} icon={Shield} iconColor="text-yellow-600" iconBgColor="bg-yellow-100" valueColor="!text-yellow-600" trend={{type: 'down', value: '-2.3%', text: '较上周 -2.3%'}} helpInfo={supervisorMetricDescriptions.appealSuccessRate} />
          <MetricCard title="AI质检准确率" value={`${performanceData.aiAccuracyRate}%`} icon={Brain} iconColor="text-indigo-600" iconBgColor="bg-indigo-100" valueColor="!text-indigo-600" trend={{type: 'up', value: '+1.8%', text: '较上周 +1.8%'}} helpInfo={supervisorMetricDescriptions.aiAccuracyRate} />
        </div>
      </div>

      {/* 成绩趋势分析 */}
      <Card 
        className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200"
        title={
          <Space>
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <span className="font-semibold text-gray-800">成绩趋势分析</span>
          </Space>
        }
      >
        <ResponsiveContainer width="100%" height={320}>
          <LineChart
            data={trendData}
            margin={{
              top: 10,
              right: 30,
              left: 0,
              bottom: 0,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#e0e0e0" />
            <XAxis dataKey="date" axisLine={false} tickLine={false} tick={{ fontSize: 12, fill: '#666' }} />
            <YAxis yAxisId="left" domain={[75, 95]} axisLine={false} tickLine={false} tick={{ fontSize: 12, fill: '#666' }} />
            <YAxis yAxisId="right" orientation="right" domain={[80, 100]} axisLine={false} tickLine={false} tick={{ fontSize: 12, fill: '#666' }} />
            <RechartsTooltip
              cursor={{ strokeDasharray: '3 3' }}
              contentStyle={{ 
                backgroundColor: '#1f2937', 
                border: 'none', 
                borderRadius: '8px', 
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                color: '#fff'
              }}
              labelStyle={{ color: '#d1d5db' }}
              itemStyle={{ color: '#fff' }}
              formatter={(value: number, name: string) => {
                if (name === '平均分') {
                  return [`${value.toFixed(1)}分`, '平均分'];
                } else {
                  return [`${value.toFixed(1)}%`, '合格率'];
                }
              }}
              labelFormatter={(label: string) => `日期: ${label}`}
            />
            <Line 
              yAxisId="left" 
              type="monotone" 
              dataKey="avgScore" 
              stroke="#3b82f6" 
              strokeWidth={3}
              dot={{ r: 5, fill: '#3b82f6', stroke: '#fff', strokeWidth: 2, className: 'drop-shadow-sm' }}
              activeDot={{ r: 8, fill: '#3b82f6', stroke: '#fff', strokeWidth: 3, className: 'drop-shadow-lg' }}
              name="平均分" 
            />
            <Line 
              yAxisId="right" 
              type="monotone" 
              dataKey="qualificationRate" 
              stroke="#10b981" 
              strokeWidth={3}
              dot={{ r: 5, fill: '#10b981', stroke: '#fff', strokeWidth: 2, className: 'drop-shadow-sm' }}
              activeDot={{ r: 8, fill: '#10b981', stroke: '#fff', strokeWidth: 3, className: 'drop-shadow-lg' }}
              name="合格率(%)" 
            />
          </LineChart>
        </ResponsiveContainer>
      </Card>

      {/* 排名统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 团队成绩排名 */}
        <Card 
          title={
            <Space>
              <Users className="w-5 h-5" />
              <span>团队成绩排名 Top5</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <Table 
            dataSource={teamRanking} 
            columns={teamColumns}
            pagination={false}
            size="small"
            rowKey="rank"
          />
        </Card>

        {/* 坐席成绩排名 */}
        <Card 
          title={
            <Space>
              <Award className="w-5 h-5" />
              <span>坐席成绩排名 Top5</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <Table 
            dataSource={agentRanking} 
            columns={agentColumns}
            pagination={false}
            size="small"
            rowKey="rank"
          />
        </Card>
      </div>

      {/* 错误分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 严重错误项 */}
        <Card 
          title={
            <Space>
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span>严重错误项 Top5</span>
            </Space>
          }
        >
          <div className="space-y-3">
            {seriousErrors.slice(0, 5).map((error, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                    {index + 1}
                  </span>
                  <span className="text-sm font-medium">{error.name}</span>
                </div>
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                  {error.count} 次
                </span>
              </div>
            ))}
          </div>
        </Card>

        {/* 高频失分项 */}
        <Card 
          title={
            <Space>
              <XCircle className="w-5 h-5 text-orange-500" />
              <span>高频失分项 Top5</span>
            </Space>
          }
        >
          <div className="space-y-3">
            {frequentDeductions.slice(0, 5).map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                    {index + 1}
                  </span>
                  <span className="text-sm font-medium">{item.name}</span>
                </div>
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">
                  {item.count} 次
                </span>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* 通知公告和待处理申诉 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 通知公告 */}
        <Card 
          title={
            <Space>
              <Bell className="w-5 h-5" />
              <span>通知公告</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {notifications.slice(0, 4).map((notification) => (
              <div 
                key={notification.id} 
                className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                  getNotificationPriorityStyle(notification.priority)
                }`}
              >
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  notification.isRead ? 'bg-gray-300' : 'bg-blue-500'
                }`} />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">{notification.title}</p>
                    {notification.priority === 'high' && (
                      <Badge count="重要" style={{ backgroundColor: '#f5222d' }} />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                  <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 待处理申诉列表 */}
        <Card 
          title={
            <Space>
              <MessageSquare className="w-5 h-5" />
              <span>待处理申诉</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {pendingAppeals.map((appeal) => (
              <div key={appeal.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{appeal.recordId}</span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>{appeal.agent} | {appeal.team}</span>
                    <span>AI分数: {appeal.aiScore}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">{appeal.appealReason}</p>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <Button type="primary" size="small">
                    开始处理
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
      </div>
    </div>
  );
};

export default FinalSupervisorHomePage;

// 添加自定义样式
const styles = `
  .supervisor-metric-help-tooltip .ant-tooltip-inner {
    background-color: #1f2937;
    border-radius: 8px;
    padding: 12px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}