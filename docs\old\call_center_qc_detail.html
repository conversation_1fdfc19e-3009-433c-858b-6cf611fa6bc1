<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质检明细 - 智能客服质检系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(90deg, #4a90e2, #357abd);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .status-badge {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            padding: 30px;
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(45deg, #f8f9ff, #e8ecff);
            padding: 20px 25px;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-content {
            padding: 25px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 14px;
            color: #111827;
            font-weight: 500;
        }

        .score-container {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .score-circle {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .score-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .score-number {
            font-size: 28px;
            font-weight: 700;
            color: #059669;
        }

        .score-max {
            font-size: 14px;
            color: #6b7280;
        }

        .score-details {
            flex: 1;
        }

        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .score-item:last-child {
            border-bottom: none;
        }

        .score-bar {
            width: 200px;
            height: 8px;
            background: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
            margin: 0 15px;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 4px;
            transition: width 0.8s ease;
        }

        .violation-item {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .violation-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .violation-title {
            font-weight: 600;
            color: #dc2626;
            flex: 1;
        }

        .violation-score {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .violation-time {
            color: #6b7280;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .violation-content {
            color: #374151;
            font-size: 14px;
            line-height: 1.5;
        }

        .audio-player {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
        }

        .player-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .play-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4a90e2;
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-btn:hover {
            background: #357abd;
            transform: scale(1.05);
        }

        .progress-bar {
            flex: 1;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4a90e2, #357abd);
            border-radius: 3px;
            width: 30%;
            transition: width 0.3s ease;
        }

        .time-display {
            font-size: 12px;
            color: #64748b;
            min-width: 80px;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(74, 144, 226, 0.4);
        }

        .btn-secondary {
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
        }

        .timeline {
            position: relative;
        }

        .timeline-item {
            position: relative;
            padding-left: 30px;
            padding-bottom: 20px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4a90e2;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            left: 13px;
            top: 18px;
            width: 2px;
            height: calc(100% - 12px);
            background: #e2e8f0;
        }

        .timeline-item:last-child::after {
            display: none;
        }

        .timeline-content {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .timeline-time {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .timeline-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }

        .timeline-desc {
            font-size: 14px;
            color: #6b7280;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #374151;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4a90e2;
        }

        textarea.form-control {
            height: 100px;
            resize: vertical;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .card {
            animation: slideIn 0.6s ease forwards;
        }

        .card:nth-child(2) { animation-delay: 0.1s; }
        .card:nth-child(3) { animation-delay: 0.2s; }
        .card:nth-child(4) { animation-delay: 0.3s; }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .score-container {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>质检明细 #QC202406260001</h1>
            <div class="status-badge">待复核</div>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <!-- 基础信息 -->
                <div class="card">
                    <div class="card-header">
                        <span>📋</span>
                        基础信息
                    </div>
                    <div class="card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">通话ID</div>
                                <div class="info-value">CALL-2024062601234</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">客服工号</div>
                                <div class="info-value">CS001 - 张小明</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">客户信息</div>
                                <div class="info-value">李先生 (139****8888)</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">通话时间</div>
                                <div class="info-value">2024-06-26 14:32:15</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">通话时长</div>
                                <div class="info-value">8分23秒</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">业务类型</div>
                                <div class="info-value">账单查询</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">质检时间</div>
                                <div class="info-value">2024-06-26 15:45:32</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">质检模型</div>
                                <div class="info-value">智能质检 v3.2.1</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 质检结果 -->
                <div class="card">
                    <div class="card-header">
                        <span>📊</span>
                        质检结果
                    </div>
                    <div class="card-content">
                        <div class="score-container">
                            <div class="score-circle">
                                <svg width="120" height="120" viewBox="0 0 120 120">
                                    <circle cx="60" cy="60" r="50" fill="none" stroke="#f3f4f6" stroke-width="8"/>
                                    <circle cx="60" cy="60" r="50" fill="none" stroke="url(#gradient)" stroke-width="8" 
                                            stroke-dasharray="314" stroke-dashoffset="62.8" 
                                            stroke-linecap="round" transform="rotate(-90 60 60)"/>
                                    <defs>
                                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:#10b981"/>
                                            <stop offset="100%" style="stop-color:#059669"/>
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="score-text">
                                    <div class="score-number">82</div>
                                    <div class="score-max">/ 100</div>
                                </div>
                            </div>
                            <div class="score-details">
                                <div class="score-item">
                                    <span>服务态度</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: 90%"></div>
                                    </div>
                                    <span>90分</span>
                                </div>
                                <div class="score-item">
                                    <span>专业能力</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: 85%"></div>
                                    </div>
                                    <span>85分</span>
                                </div>
                                <div class="score-item">
                                    <span>规范性</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: 75%"></div>
                                    </div>
                                    <span>75分</span>
                                </div>
                                <div class="score-item">
                                    <span>响应效率</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: 88%"></div>
                                    </div>
                                    <span>88分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 违规明细 -->
                <div class="card">
                    <div class="card-header">
                        <span>⚠️</span>
                        违规明细
                    </div>
                    <div class="card-content">
                        <div class="violation-item">
                            <div class="violation-header">
                                <div class="violation-title">未进行开场白标准问候</div>
                                <div class="violation-score">-10分</div>
                            </div>
                            <div class="violation-time">发生时间: 00:05 - 00:15</div>
                            <div class="violation-content">
                                检测到客服开场直接询问客户需求，未按标准流程进行问候和自我介绍。
                            </div>
                            <div class="audio-player">
                                <div class="player-controls">
                                    <button class="play-btn" onclick="togglePlay(this)">▶</button>
                                    <div class="progress-bar" onclick="seekAudio(event)">
                                        <div class="progress-fill"></div>
                                    </div>
                                    <div class="time-display">00:05 / 00:10</div>
                                </div>
                                <div style="font-size: 14px; color: #64748b; background: white; padding: 10px; border-radius: 8px; margin-top: 10px;">
                                    <strong>客服:</strong> "请问您需要查询什么业务？"<br>
                                    <strong>客户:</strong> "我想查询一下上个月的账单..."
                                </div>
                            </div>
                        </div>

                        <div class="violation-item">
                            <div class="violation-header">
                                <div class="violation-title">服务流程不规范</div>
                                <div class="violation-score">-8分</div>
                            </div>
                            <div class="violation-time">发生时间: 03:25 - 03:45</div>
                            <div class="violation-content">
                                在处理客户查询时，未按标准流程进行身份验证就直接提供账单信息。
                            </div>
                            <div class="audio-player">
                                <div class="player-controls">
                                    <button class="play-btn" onclick="togglePlay(this)">▶</button>
                                    <div class="progress-bar" onclick="seekAudio(event)">
                                        <div class="progress-fill"></div>
                                    </div>
                                    <div class="time-display">03:25 / 00:20</div>
                                </div>
                                <div style="font-size: 14px; color: #64748b; background: white; padding: 10px; border-radius: 8px; margin-top: 10px;">
                                    <strong>客户:</strong> "我要查询账单"<br>
                                    <strong>客服:</strong> "好的，您上个月的账单是268元..."
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 改进建议 -->
                <div class="card">
                    <div class="card-header">
                        <span>💡</span>
                        改进建议
                    </div>
                    <div class="card-content">
                        <div style="line-height: 1.6; color: #374151;">
                            <p style="margin-bottom: 12px;"><strong>1. 标准化开场白训练</strong></p>
                            <p style="margin-bottom: 20px; color: #6b7280;">建议加强标准开场白的练习，确保每次通话都能按规范进行问候和自我介绍。</p>
                            
                            <p style="margin-bottom: 12px;"><strong>2. 身份验证流程强化</strong></p>
                            <p style="margin-bottom: 20px; color: #6b7280;">在提供任何敏感信息前，必须完成客户身份验证，可参考相关培训材料。</p>
                            
                            <p style="margin-bottom: 12px;"><strong>3. 推荐培训课程</strong></p>
                            <p style="color: #6b7280;">• 客服标准流程培训（基础班）<br>• 信息安全与隐私保护（必修）</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <!-- 操作按钮 -->
                <div class="card">
                    <div class="card-header">
                        <span>🎯</span>
                        操作中心
                    </div>
                    <div class="card-content">
                        <div class="action-buttons">
                            <button class="btn btn-danger" onclick="openModal('appealModal')">
                                <span>📝</span>
                                发起申诉
                            </button>
                            <button class="btn btn-primary" onclick="exportReport()">
                                <span>📄</span>
                                导出报告
                            </button>
                            <button class="btn btn-secondary" onclick="shareRecord()">
                                <span>🔗</span>
                                分享记录
                            </button>
                            <button class="btn btn-secondary" onclick="addNote()">
                                <span>📎</span>
                                添加备注
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 处理流程 -->
                <div class="card">
                    <div class="card-header">
                        <span>🔄</span>
                        处理流程
                    </div>
                    <div class="card-content">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-time">2024-06-26 15:45</div>
                                    <div class="timeline-title">智能质检完成</div>
                                    <div class="timeline-desc">系统自动质检并生成报告</div>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-time">2024-06-26 16:20</div>
                                    <div class="timeline-title">待人工复核</div>
                                    <div class="timeline-desc">分配给质检员王小华</div>
                                </div>
                            </div>
                            <div class="timeline-item" style="opacity: 0.5;">
                                <div class="timeline-content">
                                    <div class="timeline-time">待处理</div>
                                    <div class="timeline-title">复核完成</div>
                                    <div class="timeline-desc">等待质检员确认结果</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计对比 -->
                <div class="card">
                    <div class="card-header">
                        <span>📈</span>
                        对比分析
                    </div>
                    <div class="card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">本次得分</div>
                                <div class="info-value" style="color: #059669;">82分</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">个人平均</div>
                                <div class="info-value">85分</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">团队平均</div>
                                <div class="info-value">83分</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">环比变化</div>
                                <div class="info-value" style="color: #dc2626;">-3分</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 申诉模态框 -->
    <div id="appealModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">发起申诉</div>
            <form>
                <div class="form-group">
                    <label class="form-label">申诉类型</label>
                    <select class="form-control">
                        <option>质检结果有误</option>
                        <option>扣分标准不当</option>
                        <option>技术问题导致</option>
                        <option>其他原因</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">申诉说明</label>
                    <textarea class="form-control" placeholder="请详细说明申诉理由..."></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">补充材料</label>
                    <input type="file" class="form-control" multiple>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('appealModal')">取消</button>
                    <button type="submit" class="btn btn-primary" onclick="submitAppeal()">提交申诉</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 音频播放控制
        function togglePlay(btn) {
            if (btn.innerHTML === '▶') {
                btn.innerHTML = '⏸';
                simulateAudioPlay(btn);
            } else {
                btn.innerHTML = '▶';
                // 停止播放逻辑
            }
        }

        function simulateAudioPlay(btn) {
            const progressBar = btn.parentElement.querySelector('.progress-fill');
            const timeDisplay = btn.parentElement.querySelector('.time-display');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += 2;
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    btn.innerHTML = '▶';
                }
            }, 100);
        }

        function seekAudio(event) {
            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const percent = (event.clientX - rect.left) / rect.width * 100;
            progressBar.querySelector('.progress-fill').style.width = percent + '%';
        }

        // 模态框控制
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // 提交申诉
        function submitAppeal() {
            alert('申诉已提交，我们会在2个工作日内处理您的申诉');
            closeModal('appealModal');
            // 更新状态为申诉中
            document.querySelector('.status-badge').textContent = '申诉中';
            document.querySelector('.status-badge').style.background = 'rgba(255, 193, 7, 0.2)';
        }

        // 导出报告
        function exportReport() {
            // 模拟导出功能
            const loadingToast = document.createElement('div');
            loadingToast.textContent = '正在生成报告...';
            loadingToast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4a90e2;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 1001;
                animation: slideInRight 0.3s ease;
            `;
            document.body.appendChild(loadingToast);
            
            setTimeout(() => {
                loadingToast.textContent = '报告导出成功！';
                loadingToast.style.background = '#10b981';
                setTimeout(() => {
                    loadingToast.remove();
                }, 2000);
            }, 1500);
        }

        // 分享记录
        function shareRecord() {
            if (navigator.share) {
                navigator.share({
                    title: '质检明细报告',
                    text: '质检编号: QC202406260001',
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showToast('链接已复制到剪贴板');
                });
            }
        }

        // 添加备注
        function addNote() {
            const note = prompt('请输入备注内容:');
            if (note) {
                const timeline = document.querySelector('.timeline');
                const newItem = document.createElement('div');
                newItem.className = 'timeline-item';
                newItem.innerHTML = `
                    <div class="timeline-content">
                        <div class="timeline-time">${new Date().toLocaleString()}</div>
                        <div class="timeline-title">添加备注</div>
                        <div class="timeline-desc">${note}</div>
                    </div>
                `;
                timeline.insertBefore(newItem, timeline.firstChild);
                showToast('备注添加成功');
            }
        }

        // 显示提示信息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 1001;
                animation: slideInRight 0.3s ease;
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
                .card {
                    opacity: 0;
                    animation: slideIn 0.6s ease forwards;
                }
                .card:nth-child(1) { animation-delay: 0s; }
                .card:nth-child(2) { animation-delay: 0.1s; }
                .card:nth-child(3) { animation-delay: 0.2s; }
                .card:nth-child(4) { animation-delay: 0.3s; }
                .card:nth-child(5) { animation-delay: 0.4s; }
            `;
            document.head.appendChild(style);
        });

        // 动态更新进度条动画
        setTimeout(() => {
            const scoreFills = document.querySelectorAll('.score-fill');
            scoreFills.forEach(fill => {
                const width = fill.style.width;
                fill.style.width = '0%';
                setTimeout(() => {
                    fill.style.width = width;
                }, 500);
            });
        }, 1000);
    </script>
</body>
</html>