import React from 'react';

/**
 * 角色判断配置接口
 */
interface RoleJudgmentConfigProps {
  config: {
    sentenceIndex: number;
    role: 'customer' | 'agent';
  };
  setConfig: React.Dispatch<React.SetStateAction<any>>;
}

/**
 * 角色判断配置组件
 * @param config 当前配置
 * @param setConfig 更新配置的函数
 * @constructor
 */
const RoleJudgmentConfig: React.FC<RoleJudgmentConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">配置演示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检查逻辑</label>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">检测第</span>
            <input
              type="number"
              className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.sentenceIndex}
              onChange={(e) => setConfig({ ...config, sentenceIndex: parseInt(e.target.value) || 0 })}
            />
            <span className="text-sm text-gray-700">句的角色是否为</span>
            <select
              className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.role}
              onChange={(e) => setConfig({ ...config, role: e.target.value as any })}
            >
              <option value="customer">客户</option>
              <option value="agent">客服</option>
            </select>
            <span
              className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50"
              title="正数代表从头开始（如1代表第1句），负数代表从末尾开始（如-1代表最后1句）。0或空无效。"
            >
              ?
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleJudgmentConfig; 