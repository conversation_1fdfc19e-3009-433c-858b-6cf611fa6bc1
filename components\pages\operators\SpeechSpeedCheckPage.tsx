import React, { useState } from 'react';
import SpeechSpeedConcepts from './speechSpeedCheck/SpeechSpeedConcepts';
import SpeechSpeedConfigComponent from './speechSpeedCheck/SpeechSpeedConfig';
import SpeechSpeedTester from './speechSpeedCheck/SpeechSpeedTester';
import SpeechSpeedCases from './speechSpeedCheck/SpeechSpeedCases';
import SpeechSpeedTips from './speechSpeedCheck/SpeechSpeedTips';

interface SpeechSpeedConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  speedThreshold: number;
  minCharCount: number;
  checkMode: 'single' | 'average';
}

/**
 * 语速检查详细演示页面
 * 提供语速检查算子的完整学习体验
 */
const SpeechSpeedCheckPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState<SpeechSpeedConfig>({
    detectionRole: 'agent',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 5,
    speedThreshold: 300,
    minCharCount: 4,
    checkMode: 'single'
  });

  // 测试文本
  const [testText, setTestText] = useState(
    '[00:05-00:08] 客服：您好，欢迎来电咨询\n[00:09-00:12] 客户：我想了解产品价格\n[00:13-00:16] 客服：好的，我来为您查询\n[00:17-00:20] 客户：谢谢'
  );

  // 加载案例
  const loadCase = (caseConfig: SpeechSpeedConfig, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🎤 语速检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测对话中的语速是否过快或过慢，支持单句检测和平均检测
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                🎯 智能算子
              </span>
              <span className="text-sm text-gray-500">
                算子类型：语音检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <SpeechSpeedConcepts />

                         {/* 配置演示 */}
             <SpeechSpeedConfigComponent config={config} setConfig={setConfig} />

            {/* 实时测试 */}
            <SpeechSpeedTester 
              config={config} 
              testText={testText} 
              setTestText={setTestText} 
            />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <SpeechSpeedCases onLoadCase={loadCase} />
            
            {/* 使用提示 */}
            <SpeechSpeedTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>理解语速计算</strong>：掌握语速=字符数÷时长×60的计算公式</p>
                <p>• <strong>合理设置阈值</strong>：根据业务场景设置合适的语速阈值</p>
                <p>• <strong>灵活选择检测模式</strong>：质检场景用单句检测，培训场景用平均检测</p>
                <p>• <strong>注意时间格式</strong>：确保时间戳格式正确，支持标准和简化两种格式</p>
                <p>• <strong>实际应用优化</strong>：结合短句过滤和范围限制提高检测准确性</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpeechSpeedCheckPage; 