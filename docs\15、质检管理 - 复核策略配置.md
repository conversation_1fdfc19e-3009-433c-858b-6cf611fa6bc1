
---

### 页面十三：复核策略配置 (FinalReviewStrategyListPage.tsx)

#### 1. 核心定位与目标
该页面是**质检流程自动化分发**的控制中心。其核心目标是让管理者能够创建一系列的**复核策略 (Review Strategy)**，这些策略定义了在什么条件下，一个AI自动质检完成的通话记录需要被**自动推送给人工进行复核**，以及应该**推送给谁**。这极大地提升了质检的效率和针对性，避免了大海捞针式的人工抽查。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "复核策略管理"
*   **副标题**: "管理质检复核策略，配置触发条件和分配规则"
*   **图标**: `Settings` (设置图标)，表明其配置属性。
*   **核心操作**: `新建策略` 按钮，点击后弹出创建策略的抽屉表单 (`FinalCreateReviewStrategyPage.tsx`)。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 快速查找特定的复核策略。
*   **筛选字段**:
    *   `策略名称`: 按名称模糊搜索。
    *   `状态`: 筛选“已启用”或“已禁用”的策略。

**c. 策略列表表格 (`Table`)**
*   **布局**: 以表格形式展示所有已配置的复核策略。
*   **表格列**:
    *   `序号`
    *   `策略名称`: 显示策略名称，旁边附有“已启用”/“已禁用”的状态徽章，下方是策略的简要描述。
    *   `触发条件`: **核心信息**，简要描述触发该策略的核心条件，例如 “AI初检得分 < 60”。对于复杂的多条件组合，可能会显示“...等N个条件”，并支持鼠标悬浮查看详情。
    *   `分配规则`: 描述符合条件的任务将被如何分配，例如“轮询分配给 张三, 李四”或“指定分配给 王五”。
    *   `创建人`
    *   `创建时间`
    *   `操作`:
        *   **编辑 (`Edit`图标)**: 修改策略的触发条件或分配规则。
        *   **启用/禁用 (`Eye`/`PowerOff`图标)**: 快速切换策略的生效状态。
        *   **删除 (`Trash2`图标)**: 删除该策略。

**d. 分页组件 (`UnifiedPagination`)**
*   用于浏览和导航策略列表。

**e. 创建/编辑策略的抽屉表单 (`FinalCreateReviewStrategyPage.tsx`)**
这是定义一个自动化复核流程的核心界面，通常包含“IF...THEN...”的逻辑结构。

*   **表单内容**:
    *   **第一部分：基本信息**
        *   `策略名称` 和 `策略描述`。
    *   **第二部分：触发条件 (IF)**
        *   **逻辑关系**: 允许选择多个条件之间的逻辑是“满足所有条件(AND)”还是“满足任一条件(OR)”。
        *   **条件构建器**:
            *   用户可以动态添加一个或多个条件。
            *   每个条件由 **[对象] + [操作符] + [值]** 构成。
            *   **对象 (Attribute)**: 可选的对象非常丰富，例如：
                *   `AI初检得分`
                *   `命中质检规则`
                *   `质检规则类型` (如“合规风险”类)
                *   `质检规则重要程度` (如“严重违规”)
                *   `通话时长`
                *   `坐席员` / `班组`
                *   `客户号码` (用于VIP客户等特殊场景)
            *   **操作符 (Operator)**: 根据所选对象动态变化，例如：
                *   对于“得分”，操作符可以是 `>`、`<`、`=` 等。
                *   对于“规则”，操作符可以是 `是`、`不是`。
                *   对于“坐席”，操作符可以是 `属于`、`不属于`。
            *   **值 (Value)**: 根据对象和操作符动态变化，可以是输入框、下拉选择器（选择规则、坐席等）、数值范围滑块等。
    *   **第三部分：执行分配 (THEN)**
        *   `分配模式`:
            *   **轮询分配**: 将任务依次循环分配给指定的一组复核员。
            *   **平均分配**: 尽量保证指定的一组复核员接收到的任务数量大致相等。
            *   **指定分配**: 将所有触发的任务都分配给唯一的一个复核员。
        *   `分配目标`: 一个多选框或单选框，用于选择接收任务的复核员。

#### 3. 核心交互与操作
*   **自动化工作流**: 该页面的核心是**定义自动化工作流**。它将“AI质检”和“人工复核”两个环节无缝地连接起来，实现了“问题发现 -> 任务分发”的自动化。
*   **精确筛选**: 通过灵活的条件构建器，管理者可以非常精确地定义出需要人工关注的通话类型，例如“所有由新人处理的、且得分低于80分的、并且命中了‘合规风险’类规则的通话”，从而将宝贵的人力资源聚焦在最高风险或最需要关注的场景上。
*   **负载均衡**: 分配模式的设计考虑了团队协作的现实需求，支持轮询和平均分配，避免了任务分配不均的问题。

---