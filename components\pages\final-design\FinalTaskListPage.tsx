import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Plus, Edit, Trash2, Eye, CheckSquare } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { DatePicker, message } from 'antd';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import FinalCreateTaskPage from './FinalCreateTaskPage';

const { RangePicker } = DatePicker;

const qualitySchemeMap: { [key: string]: string } = {
    scheme1: '标准服务流程质检方案',
    scheme2: '营销流程质检方案',
    scheme3: '新人上岗考察方案',
    scheme4: '金融合规质检方案',
};

/**
 * 质检任务管理 - 列表页
 * @constructor
 */
const FinalTaskListPage: React.FC = () => {
    const navigate = useNavigate();

    const initialTasks = [
        {
            key: '1',
            taskName: '11月营销活动通话质检',
            taskDescription: '针对11月大促活动的通话进行合规检查',
            status: '执行中',
            progress: 85,
            total: 10000,
            checked: 8500,
            dataSourceId: '1',
            fixedDateRange: [dayjs('2023-11-01 00:00:00'), dayjs('2023-11-10 23:59:59')],
            targetType: 'byStruct',
            targetKeys: ['0-0-0', '0-0-1', '0-1-0'],
            qualityScheme: 'scheme1',
            qualityMode: 'sampling',
            samplingRatio: 20,
            executionPlan: 'now',
            creator: '张主管',
            createTime: '2023-11-01 10:30',
            source: '手动创建'
        },
        {
            key: '2',
            taskName: '[每日检查] - 20231115期',
            taskDescription: '系统自动生成的每日服务规范检查任务',
            status: '已完成',
            progress: 100,
            total: 1250,
            checked: 1250,
            dataSourceId: '2',
            fixedDateRange: [dayjs('2023-11-14 00:00:00'), dayjs('2023-11-14 23:59:59')],
            targetType: 'all',
            targetKeys: [],
            qualityScheme: 'scheme1',
            qualityMode: 'full',
            executionPlan: 'now',
            creator: '系统',
            createTime: '2023-11-15 02:00',
            source: '由计划生成'
        },
        {
            key: '3',
            taskName: '12月新人上岗质检',
            taskDescription: '针对12月新入职员工的通话进行全面质检',
            status: '待执行',
            dataSourceId: '3',
            fixedDateRange: [dayjs('2023-12-01 00:00:00'), dayjs('2023-12-31 23:59:59')],
            targetType: 'byStruct',
            targetKeys: ['0-0-0'], // Assume '客服A组' is '新人组' for this mock
            qualityScheme: 'scheme3',
            qualityMode: 'full',
            executionPlan: 'timed',
            timedDate: dayjs('2023-12-01 09:00:00'),
            creator: '王经理',
            createTime: '2023-11-28 11:00',
            source: '手动创建'
        },
        {
            key: '4',
            taskName: '合规风险专项检查',
            taskDescription: '金融产品线相关的通话合规性检查',
            status: '执行失败',
            error: '数据源连接超时',
            dataSourceId: '1',
            fixedDateRange: [dayjs('2023-11-01 00:00:00'), dayjs('2023-11-30 23:59:59')],
            targetType: 'all',
            qualityScheme: 'scheme4',
            qualityMode: 'full',
            executionPlan: 'now',
            creator: '风控部门',
            createTime: '2023-11-05 09:00',
            source: '手动创建'
        },
    ];

    const [tasks, setTasks] = useState(initialTasks);
    const [isDrawerOpen, setDrawerOpen] = useState(false);
    const [editingTaskId, setEditingTaskId] = useState<string | undefined>();
    const [editingTaskInitialValues, setEditingTaskInitialValues] = useState<any | undefined>();
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
    });
    const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'running' | 'completed' | 'failed'>('all');
    const [searchFilters, setSearchFilters] = useState<any>({
        taskName: '',
        source: '',
        creator: '',
        dateRange: [],
        qualityScheme: '',
    });

    // 筛选字段配置
    const filterFields: FilterField[] = [
        { key: 'taskName', label: '任务名称', type: 'text', placeholder: '请输入任务名称关键词' },
        { key: 'source', label: '来源', type: 'select', options: [
            { value: '手动创建', label: '手动创建' },
            { value: '由计划生成', label: '由计划生成' }
        ]},
        { key: 'qualityScheme', label: '质检方案', type: 'select', options: Object.entries(qualitySchemeMap).map(([key, value]) => ({
            value: key,
            label: value
        }))},
        { key: 'creator', label: '创建人', type: 'text', placeholder: '请输入创建人姓名' },
        { key: 'dateRange', label: '创建时间', type: 'dateRange' }
    ];

    // 处理筛选条件变化
    const handleFiltersChange = (newFilters: Record<string, any>) => {
        setSearchFilters(newFilters);
    };

    // 处理查询
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, current: 1 }));
    };

    // 处理重置
    const handleReset = () => {
        setSearchFilters({
            taskName: '',
            source: '',
            creator: '',
            dateRange: [],
            qualityScheme: '',
        });
        setPagination(prev => ({ ...prev, current: 1 }));
    };

    const filteredAndPaginatedTasks = useMemo(() => {
        let filtered = tasks;
        if (activeTab !== 'all') {
            filtered = tasks.filter(task => {
                if (activeTab === 'pending' && task.status === '待执行') return true;
                if (activeTab === 'running' && task.status === '执行中') return true;
                if (activeTab === 'completed' && task.status === '已完成') return true;
                if (activeTab === 'failed' && task.status === '执行失败') return true;
                return false;
            });
        }

        // Apply search filters
        if (searchFilters.taskName) {
            filtered = filtered.filter(task => task.taskName.includes(searchFilters.taskName));
        }
        if (searchFilters.source) {
            filtered = filtered.filter(task => task.source === searchFilters.source);
        }
        if (searchFilters.creator) {
            filtered = filtered.filter(task => task.creator.includes(searchFilters.creator));
        }
        if (searchFilters.dateRange && searchFilters.dateRange.length === 2) {
            const [startDate, endDate] = searchFilters.dateRange;
            filtered = filtered.filter(task => {
                const taskCreateTime = dayjs(task.createTime);
                return taskCreateTime.isAfter(startDate) && taskCreateTime.isBefore(endDate);
            });
        }
        if (searchFilters.qualityScheme) {
            filtered = filtered.filter(task => task.qualityScheme === searchFilters.qualityScheme);
        }

        const total = filtered.length;
        const startIndex = (pagination.current - 1) * pagination.pageSize;
        const endIndex = startIndex + pagination.pageSize;
        const currentData = filtered.slice(startIndex, endIndex);
        const totalPages = Math.ceil(total / pagination.pageSize);

        return { data: currentData, total, totalPages };
    }, [activeTab, tasks, pagination.current, pagination.pageSize, searchFilters]);

    const handleCreate = () => {
        setEditingTaskId(undefined);
        setEditingTaskInitialValues(undefined);
        setDrawerOpen(true);
    };

    const handleEdit = (task: any) => {
        setEditingTaskId(task.key);
        setEditingTaskInitialValues(task);
        setDrawerOpen(true);
    };

    const handleFormSubmit = (values: any) => {
        if (values.taskId) {
            setTasks(tasks.map(t => t.key === values.taskId ? { ...t, ...values } : t));
            message.success(`任务 "${values.taskName}" 已更新`);
        } else {
            const newTask = {
                key: String(Date.now()),
                taskName: values.taskName,
                taskDescription: values.taskDescription || '',
                status: '待执行',
                progress: 0,
                total: 0,
                checked: 0,
                dataSourceId: values.dataSourceId,
                fixedDateRange: values.fixedDateRange,
                targetType: values.targetType,
                targetKeys: values.targetKeys || [],
                qualityScheme: values.qualityScheme,
                qualityMode: values.qualityMode,
                samplingRatio: values.samplingRatio,
                executionPlan: values.executionPlan,
                timedDate: values.timedDate,
                creator: '当前用户',
                createTime: dayjs().format('YYYY-MM-DD HH:mm'),
                source: '手动创建'
            };
            setTasks([newTask, ...tasks]);
            message.success(`任务 "${values.taskName}" 已创建`);
        }
        setDrawerOpen(false);
    };
    
    const handleDelete = (key: string) => {
        setTasks(tasks.filter(t => t.key !== key));
        // message.success('任务已删除');
    };
    
    const getStatusTag = (status: string) => {
        const statusColors: { [key: string]: string } = {
            '待执行': 'bg-gray-100 text-gray-800',
            '执行中': 'bg-blue-100 text-blue-800',
            '已完成': 'bg-green-100 text-green-800',
            '执行失败': 'bg-red-100 text-red-800',
        };
        return (
            <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
                {status}
            </span>
        );
    };

    const getProgressDisplay = (task: any) => {
        if (task.status === '执行中') {
            return (
                <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${task.progress}%` }}
                        title={`${task.checked} / ${task.total}`}
                    ></div>
                </div>
            );
        } else if (task.status === '执行失败') {
            return (
                <span className="text-red-500 text-sm" title={task.error}>
                    执行失败
                </span>
            );
        } else if (task.status === '已完成') {
            return (
                <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: '100%' }}
                        title={`${task.checked} / ${task.total}`}
                    ></div>
                </div>
            );
        }
        return <span className="text-gray-500">--</span>;
    };



    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="质检任务管理"
                subtitle="管理所有一次性的和由计划生成的质检任务。"
                icon={CheckSquare}
                badge={{ text: "任务管理", color: "blue" }}
                actions={[
                    {
                        label: "新建任务",
                        icon: Plus,
                        onClick: handleCreate,
                        variant: "primary"
                    }
                ]}
            />
            
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* 统一查询区域 */}
                    <UnifiedSearchFilter
                         fields={filterFields}
                         filters={searchFilters}
                         onFiltersChange={handleFiltersChange}
                         onSearch={handleSearch}
                         onReset={handleReset}
                     />
                    
                    {/* Tab切换: Moved inside the main content card */}
                    <div className="mt-6">
                        <div className="border-b border-gray-200">
                            <div className="flex">
                                {[ { key: 'all', label: '全部', count: tasks.length },
                                  { key: 'pending', label: '待执行', count: tasks.filter(t => t.status === '待执行').length },
                                  { key: 'running', label: '执行中', count: tasks.filter(t => t.status === '执行中').length },
                                  { key: 'completed', label: '已完成', count: tasks.filter(t => t.status === '已完成').length },
                                  { key: 'failed', label: '执行失败', count: tasks.filter(t => t.status === '执行失败').length },
                                ].map(tab => (
                                    <button
                                        key={tab.key}
                                        onClick={() => {
                                            setActiveTab(tab.key as any);
                                            setPagination(prev => ({ ...prev, current: 1 }));
                                        }}
                                        className={`px-6 py-3 font-medium text-sm border-b-2 ${activeTab === tab.key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
                                    >
                                        {tab.label} ({tab.count})
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Tasks Table */}
                    <div className="overflow-x-auto mt-6">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-4 py-3">序号</th>
                                    <th scope="col" className="px-6 py-3">任务名称</th>
                                    <th scope="col" className="px-6 py-3">任务状态</th>
                                    <th scope="col" className="px-6 py-3">任务进度</th>
                                    <th scope="col" className="px-6 py-3">质检方案</th>
                                    <th scope="col" className="px-6 py-3">来源</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">创建时间</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredAndPaginatedTasks.data.map((task, index) => (
                                    <tr key={task.key} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-4 py-4 text-gray-700">
                                            {(pagination.current - 1) * pagination.pageSize + index + 1}
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="font-semibold text-gray-900">{task.taskName}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            {getStatusTag(task.status)}
                                        </td>
                                        <td className="px-6 py-4">
                                            {getProgressDisplay(task)}
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{qualitySchemeMap[task.qualityScheme] || task.qualityScheme}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{task.source}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{task.creator}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{task.createTime}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                                onClick={() => navigate(`/final-design/task-detail/${task.key}`)}
                                                className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                                                title="查看详情"
                                            >
                                                <Eye className="w-4 h-4" />
                                            </button>
                                            {task.status === '待执行' && (
                                                <button
                                                    onClick={() => handleEdit(task)}
                                                    className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors mx-1"
                                                    title="编辑"
                                                >
                                                    <Edit className="w-4 h-4" />
                                                </button>
                                            )}
                                            {(task.status === '待执行' || task.status === '已完成' || task.status === '执行失败') && (
                                                <button
                                                    onClick={() => handleDelete(task.key)}
                                                    className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                                                    title="删除"
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredAndPaginatedTasks.data.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的任务。</p>
                        </div>
                    )}
                    
                    {/* 分页 */}
                    {filteredAndPaginatedTasks.data.length > 0 && (
                        <UnifiedPagination
                            current={pagination.current}
                            pageSize={pagination.pageSize}
                            total={filteredAndPaginatedTasks.total}
                            onChange={(page) => setPagination(prev => ({ ...prev, current: page }))}
                        />
                    )}
                </motion.div>
            </main>
            {isDrawerOpen && (
                <FinalCreateTaskPage
                    onClose={() => setDrawerOpen(false)}
                    onSubmit={handleFormSubmit}
                    taskId={editingTaskId}
                    initialValues={editingTaskInitialValues}
                />
            )}
        </div>
    );
};

export default FinalTaskListPage;