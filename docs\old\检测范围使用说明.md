检测范围使用说明
检测范围是通过一个数字区间来筛选出当前条件生效的范围，具体说明如下：

一、无前置条件时

1. 第1句到第3句，代表所检测角色所说的前三句；

2. 第-1句到第-3句，代表所检测角色所说的最后三句话；

3. 第3句到第-3句，代表所检测角色所说的正数第三句到倒数第三句。

二、有前置条件时
存在前置条件时，系统会将所有句子，以前置条件命中句为分割点，切分为三个段落：前置条件命中句之前的所有句子、前置条件命中句之后的所有句子、前置条件命中句前后的部分句子，详见下图中所标示的三个段落。需要您先选择一个段落，然后再通过数字区间在该段落内来筛选具体的生效范围。


①前置条件命中位置之前

1. 第1句到第3句，代表所检测角色在当前段落内所说的前三句（紧邻前置条件命中句的句子的是第一句）；

2. 第-1句到第-3句，代表所检测角色在当前段落内所说的最后三句(倒数第一句到倒数第三句)（距离前置条件命中句最远的是最后一句）；

3. 第1句到-1句，代表所检测角色在前置条件命中句 之前 的所有句子；

4. 第0句到第2句，代表前置条件命中句当句，到所检测角色所说的第二句之间的三句话（只有在当前条件与前置条件的适用角色一致时，才可以使用第0句来定位到前置条件命中句当句，若角色不同，则不允许使用）；

5. 以上仅为使用示例，并非只能使用以上四种情况，总体来说，正数代表当前段落正数第几句，负数代表当前段落倒数第几句。

②前置条件命中位置前后

1. 第0句到第0句，仅代表前置条件命中句的当前句；

2. 第1句到第3句，代表当前置条件命中时，在前置条件命中句“之后”的所检测角色所说的第1句到第3句；

3. 第-2句到第-4句，代表当前置条件命中时，在前置条件命中句子“之前”的所检测角色所说话术的第2句到第4句；

4. 第-3句到第3句，代表当前置条件命中时，在前置条件命中句子“之前”的所检测角色所说的第3句到“之后”的第3句；

5. 以上仅为使用示例，并非只能使用以上四种情况，总体来说，正数代表前置条件命中句之后的句子，负数代表前置条件命中句之前的句子。

③前置条件命中位置之后

1. 第1句到第3句，代表所检测角色在当前段落内所说的前三句（紧邻前置条件命中句的句子的是第一句）；

2. 第-1句到第-3句，代表所检测角色在当前段落内所说的最后三句(倒数第一句到倒数第三句)（距离前置条件命中句最远的是最后一句）；

3. 第1句到-1句，代表所检测角色在前置条件命中句 之后 的所有句子；

4. 第0句到第2句，代表前置条件命中句当句，到所检测角色所说的第二句之间的三句话（只有在当前条件与前置条件的适用角色一致时，才可以使用第0句来定位到前置条件命中句当句，若角色不同，则不允许使用）；

5. 以上仅为使用示例，并非只能使用以上四种情况，总体来说，正数代表当前段落正数第几句，负数代表当前段落倒数第几句。