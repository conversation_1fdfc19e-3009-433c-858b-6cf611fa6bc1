import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Card, Input, Button, Space, Radio, Select, Slider, Row, Col, Typography, Form, Tooltip
} from 'antd';
import { PlusOutlined, DeleteOutlined, QuestionCircleOutlined, CloseOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Title, Text } = Typography;

const mockReviewers = [
  { value: 'zhangsan', label: '张三' },
  { value: 'lisi', label: '李四' },
  { value: 'wangwu', label: '王五' },
];

const mockRules = [
  { value: 'RULE-001', label: '客户不满与合规词检测' },
  { value: 'RULE-002', label: '标准开场白检测' },
  { value: 'RULE-003', label: '合规声明-录音告知' },
  { value: 'RULE-004', label: '客户情绪-激动模型' },
  { value: 'RULE-005', label: '身份证号码格式校验' },
  { value: 'RULE-006', label: '客服辱骂检测模型' },
];

const mockImportanceLevels = [
    { value: '轻度违规', label: '轻度违规' },
    { value: '中度违规', label: '中度违规' },
    { value: '严重违规', label: '严重违规' },
];

const mockCategories = [
    { value: '合规风险', label: '合规风险' },
    { value: '服务规范', label: '服务规范' },
    { value: '服务质量', label: '服务质量' },
    { value: '服务效率', label: '服务效率' },
    { value: '服务红线', label: '服务红线' },
    { value: '信息安全', label: '信息安全' },
    { value: '客户体验', label: '客户体验' },
    { value: '信息提取', label: '信息提取' },
    { value: '销售合规', label: '销售合规' },
];

const mockAgents = [
    { value: 'agent_zhangsan', label: '坐席张三' },
    { value: 'agent_lisi', label: '坐席李四' },
    { value: 'agent_wangwu', label: '坐席王五' },
];

const mockTeams = [
    { value: 'team_A', label: '班组A' },
    { value: 'team_B', label: '班组B' },
    { value: 'team_C', label: '班组C' },
];

const mockEditData = {
  name: '低分录音复核策略 (模拟)',
  description: '总分低于60分的录音将自动进入复核流程',
  conditionLogic: 'AND',
  conditions: [
    { id: 1, type: 'score', operator: '<', value: 60 }
  ],
  allocationMode: 'round-robin',
  allocatedTo: ['zhangsan', 'lisi']
};

interface Condition {
    id: number;
    type?: string;
    operator?: string;
    value?: any;
}

interface FinalCreateReviewStrategyFormProps {
    onClose: () => void;
    onSubmit: (values: any) => void;
    strategyId?: string;
}

const drawerVariants = {
    hidden: { x: '100%' },
    visible: { x: 0 },
};

const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
}


/**
 * 复核策略配置 - 新建/编辑策略页
 * @constructor
 */
const FinalCreateReviewStrategyPage: React.FC<FinalCreateReviewStrategyFormProps> = ({ onClose, onSubmit, strategyId }) => {
    const isEditing = !!strategyId;
    const [form] = Form.useForm();

    const [conditions, setConditions] = useState<Condition[]>([{ id: 1 }]);
    const [conditionLogic, setConditionLogic] = useState('AND');
    const [allocationMode, setAllocationMode] = useState('round-robin');

    React.useEffect(() => {
      if (isEditing) {
        form.setFieldsValue({
          strategyName: mockEditData.name,
          strategyDescription: mockEditData.description,
          allocationTarget: mockEditData.allocatedTo,
        });
        setConditionLogic(mockEditData.conditionLogic);
        setConditions(mockEditData.conditions);
        setAllocationMode(mockEditData.allocationMode);
      }
    }, [isEditing, form]);

    const addCondition = () => {
        setConditions([...conditions, { id: Date.now() }]);
    };

    const removeCondition = (id: number) => {
        setConditions(conditions.filter(c => c.id !== id));
    };

    const handleConditionChange = (id: number, field: keyof Condition, value: any) => {
        const newConditions = conditions.map(c => {
            if (c.id === id) {
                // Reset operator and value if type changes
                if (field === 'type') {
                    return { ...c, [field]: value, operator: undefined, value: undefined };
                }
                return { ...c, [field]: value };
            }
            return c;
        });
        setConditions(newConditions);
    };

    const renderConditionInputs = (condition: Condition) => {
        switch (condition.type) {
            case 'score':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="<">&lt;</Select.Option>
                            <Select.Option value="<=">&lt;=</Select.Option>
                            <Select.Option value=">">&gt;</Select.Option>
                            <Select.Option value=">=">&gt;=</Select.Option>
                        </Select>
                        <Input type="number" placeholder="0-100" style={{ width: 100 }} value={condition.value} onChange={(e) => handleConditionChange(condition.id, 'value', e.target.value)} />
                    </>
                );
            case 'rule':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator || 'is'} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="is">是</Select.Option>
                            <Select.Option value="is_not">不是</Select.Option>
                        </Select>
                        <Select
                            placeholder="选择一个质检规则"
                            style={{ minWidth: 200 }}
                            value={condition.value}
                            onChange={(v) => handleConditionChange(condition.id, 'value', v)}
                            options={mockRules}
                        />
                    </>
                );
            case 'rule_category':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator || 'is'} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="is">属于</Select.Option>
                            <Select.Option value="is_not">不属于</Select.Option>
                        </Select>
                        <Select
                            placeholder="选择规则类型"
                            style={{ minWidth: 200 }}
                            value={condition.value}
                            onChange={(v) => handleConditionChange(condition.id, 'value', v)}
                            options={mockCategories}
                        />
                    </>
                );
            case 'rule_importance':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator || 'is'} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="is">是</Select.Option>
                            <Select.Option value="is_not">不是</Select.Option>
                        </Select>
                        <Select
                            placeholder="选择重要程度"
                            style={{ minWidth: 200 }}
                            value={condition.value}
                            onChange={(v) => handleConditionChange(condition.id, 'value', v)}
                            options={mockImportanceLevels}
                        />
                    </>
                );
            case 'duration':
                 return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="<">&lt;</Select.Option>
                            <Select.Option value="<=">&lt;=</Select.Option>
                            <Select.Option value=">">&gt;</Select.Option>
                            <Select.Option value=">=">&gt;=</Select.Option>
                        </Select>
                        <Input type="number" placeholder="秒" style={{ width: 100 }} value={condition.value} onChange={(e) => handleConditionChange(condition.id, 'value', e.target.value)} />
                        <Text>秒</Text>
                    </>
                );
            case 'agent':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator || 'is_in'} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="is_in">是</Select.Option>
                            <Select.Option value="is_not_in">不是</Select.Option>
                        </Select>
                        <Select
                            mode="multiple"
                            placeholder="选择一个或多个坐席员"
                            style={{ minWidth: 200 }}
                            value={condition.value}
                            onChange={(v) => handleConditionChange(condition.id, 'value', v)}
                            options={mockAgents}
                        />
                    </>
                );
            case 'team':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator || 'is'} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="is">属于</Select.Option>
                            <Select.Option value="is_not">不属于</Select.Option>
                        </Select>
                        <Select
                            placeholder="选择一个班组"
                            style={{ minWidth: 200 }}
                            value={condition.value}
                            onChange={(v) => handleConditionChange(condition.id, 'value', v)}
                            options={mockTeams}
                        />
                    </>
                );
            case 'customer_number':
                return (
                    <>
                        <Select placeholder="操作符" style={{ width: 120 }} value={condition.operator || 'is_in'} onChange={(v) => handleConditionChange(condition.id, 'operator', v)}>
                            <Select.Option value="is_in">是</Select.Option>
                            <Select.Option value="is_not_in">不是</Select.Option>
                        </Select>
                        <Select
                            mode="tags"
                            placeholder="输入一个或多个客户号码"
                            style={{ minWidth: 200 }}
                            value={condition.value}
                            onChange={(v) => handleConditionChange(condition.id, 'value', v)}
                        />
                    </>
                );
            default:
                return null;
        }
    };

    const onFinish = (values: any) => {
        onSubmit(values);
        onClose();
    };


    return (
        <AnimatePresence>
            <motion.div
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40"
                onClick={onClose}
            >
                <motion.div
                    variants={drawerVariants}
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    className="bg-gray-50 h-full w-full max-w-4xl shadow-2xl flex flex-col"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200 bg-white">
                        <h2 className="text-xl font-semibold text-gray-800">{isEditing ? "编辑复核策略" : "新建复核策略"}</h2>
                        <button
                            onClick={onClose}
                            className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors"
                        >
                            <CloseOutlined className="w-6 h-6" />
                        </button>
                    </div>
                    
                    <Form form={form} layout="vertical" onFinish={onFinish} className="flex-grow flex flex-col overflow-hidden">
                       <div className="flex-grow p-6 space-y-6 overflow-y-auto">
                            <Card>
                                <Title level={5}>第一部分：基本信息</Title>
                                <Row gutter={24}>
                                    <Col span={12}>
                                        <Form.Item label="策略名称" name="strategyName" required>
                                            <Input placeholder="为您的策略起一个清晰易懂的名称" />
                                        </Form.Item>
                                    </Col>
                                    <Col span={12}>
                                        <Form.Item label="策略描述" name="strategyDescription">
                                            <TextArea rows={1} placeholder="简单描述该策略的作用" />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </Card>

                            <Card title={<Title level={5}>第二部分：触发条件 (IF)</Title>} extra={
                                <Select value={conditionLogic} onChange={setConditionLogic}>
                                    <Select.Option value="AND">满足所有条件 (AND)</Select.Option>
                                    <Select.Option value="OR">满足任一条件 (OR)</Select.Option>
                                </Select>
                            }>
                                <div className="space-y-4">
                                    {conditions.map((condition, index) => (
                                        <Row key={condition.id} gutter={16} align="middle">
                                            <Col>
                                                <Select
                                                    style={{ width: 200 }}
                                                    placeholder={`条件 ${index + 1}`}
                                                    value={condition.type}
                                                    onChange={(v) => handleConditionChange(condition.id, 'type', v)}
                                                >
                                                    <Select.Option value="score">AI初检得分</Select.Option>
                                                    <Select.Option value="rule">命中质检规则</Select.Option>
                                                    <Select.Option value="rule_category">质检规则类型</Select.Option>
                                                    <Select.Option value="rule_importance">质检规则重要程度</Select.Option>
                                                    <Select.Option value="duration">通话时长</Select.Option>
                                                    <Select.Option value="agent">坐席员</Select.Option>
                                                    <Select.Option value="team">班组</Select.Option>
                                                    <Select.Option value="customer_number">客户号码</Select.Option>
                                                </Select>
                                            </Col>
                                            <Col>
                                                <Space>
                                                    {renderConditionInputs(condition)}
                                                </Space>
                                            </Col>
                                            <Col>
                                                <Button icon={<DeleteOutlined />} type="text" danger onClick={() => removeCondition(condition.id)} />
                                            </Col>
                                        </Row>
                                    ))}
                                </div>
                                <Button type="dashed" icon={<PlusOutlined />} onClick={addCondition} className="mt-4">
                                    添加条件
                                </Button>
                            </Card>

                            <Card title={<Title level={5}>第三部分：执行分配 (THEN)</Title>}>
                                <Form.Item
                                    label={
                                        <Space>
                                            <span>分配模式</span>
                                            <Tooltip title="将触发的复核任务按以下规则分配">
                                                <QuestionCircleOutlined />
                                            </Tooltip>
                                        </Space>
                                    }
                                    required
                                >
                                    <Radio.Group value={allocationMode} onChange={(e) => setAllocationMode(e.target.value)}>
                                        <Radio.Button value="round-robin">轮询分配</Radio.Button>
                                        <Radio.Button value="average">平均分配</Radio.Button>
                                        <Radio.Button value="designated">指定分配</Radio.Button>
                                    </Radio.Group>
                                </Form.Item>
                                
                                {allocationMode === 'round-robin' && 
                                    <Form.Item name="allocationTarget">
                                        <Select mode="multiple" placeholder="选择参与轮询的复核员" className="w-1/2" options={mockReviewers} />
                                    </Form.Item>
                                }
                                {allocationMode === 'average' && 
                                    <Form.Item name="allocationTarget">
                                        <Select mode="multiple" placeholder="选择参与平均分配的复核员" className="w-1/2" options={mockReviewers} />
                                    </Form.Item>
                                }
                                {allocationMode === 'designated' && 
                                    <Form.Item name="allocationTarget">
                                        <Select placeholder="选择唯一的接收人" className="w-1/2" options={mockReviewers} />
                                    </Form.Item>
                                }
                            </Card>
                        </div>
                        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
                             <div className="flex justify-end gap-3">
                                <Button onClick={onClose}>
                                    取消
                                </Button>
                                <Button type="primary" htmlType="submit">
                                    保存策略
                                </Button>
                            </div>
                        </div>
                    </Form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default FinalCreateReviewStrategyPage; 