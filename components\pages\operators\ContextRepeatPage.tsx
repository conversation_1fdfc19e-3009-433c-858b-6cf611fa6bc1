import React, { useState } from 'react';
import ContextRepeatConcepts from './contextRepeat/ContextRepeatConcepts';
import ContextRepeatConfigComponent, { ContextRepeatConfig } from './contextRepeat/ContextRepeatConfig';
import ContextRepeatTester, { calculateSimilarity } from './contextRepeat/ContextRepeatTester';
import ContextRepeatTips from './contextRepeat/ContextRepeatTips';
import ContextRepeatCases from './contextRepeat/ContextRepeatCases';

interface ContextRepeatTestResult {
  matched: boolean;
  repeatedSentences: {
    current: string;
    similar: string;
    similarity: number;
    repeatCount: number;
  }[];
  filteredSentences: string[];
  exceptionMatches: string[];
  details: string;
}

/**
 * 计算句子的字符数（去除标点后）
 */
function getWordCount(sentence: string): number {
  return sentence.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;
}

/**
 * 上下文重复检查详细演示页面
 * 提供上下文重复检查算子的完整学习体验
 */
const ContextRepeatPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState<ContextRepeatConfig>({
    detectionRole: 'customer',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 5,
    withinSentences: 3,
    similarityThreshold: 0.8,
    violationCount: 2,
    minWordCount: 2,
    exceptionSentences: []
  });

  // 测试文本
  const [testText, setTestText] = useState(
    '客服：您好，请问有什么可以帮助您的？\n客户：你们的服务太差了！\n客服：很抱歉给您带来不便，请告诉我具体问题。\n客户：你们的服务真的很差！\n客服：我会尽快为您解决。\n客户：服务太差了，我要投诉！'
  );

  // 测试结果
  const [testResult, setTestResult] = useState<ContextRepeatTestResult | null>(null);

  // 执行测试
  const runTest = () => {
    const sentences = testText.split('\n').filter(s => s.trim());
    let targetSentences: string[] = [];

    // 根据检测角色筛选句子
    if (config.detectionRole === 'agent') {
      targetSentences = sentences.filter(s => s.startsWith('客服：'));
    } else if (config.detectionRole === 'customer') {
      targetSentences = sentences.filter(s => s.startsWith('客户：'));
    } else {
      targetSentences = sentences;
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    // 过滤短句
    const filteredSentences: string[] = [];
    const validSentences = targetSentences.filter(sentence => {
      const wordCount = getWordCount(sentence);
      if (wordCount <= config.minWordCount) {
        filteredSentences.push(sentence);
        return false;
      }
      return true;
    });

    // 检测重复
    const repeatedSentences: {
      current: string;
      similar: string;
      similarity: number;
      repeatCount: number;
    }[] = [];
    const exceptionMatches: string[] = [];
    const repeatCounts = new Map<string, number>();

    for (let i = 0; i < validSentences.length; i++) {
      const currentSentence = validSentences[i];
      
      // 检查是否为例外句子
      const isException = config.exceptionSentences.some(exception => 
        currentSentence.includes(exception)
      );
      
      if (isException) {
        exceptionMatches.push(currentSentence);
        continue;
      }

      // 检查与前面句子的相似度
      for (let j = Math.max(0, i - config.withinSentences); j < i; j++) {
        const previousSentence = validSentences[j];
        
        // 跳过例外句子
        const isPreviousException = config.exceptionSentences.some(exception => 
          previousSentence.includes(exception)
        );
        if (isPreviousException) continue;

        const similarity = calculateSimilarity(currentSentence, previousSentence);
        
        if (similarity >= config.similarityThreshold) {
          // 记录重复次数
          const key = `${currentSentence}-${previousSentence}`;
          const count = (repeatCounts.get(key) || 0) + 1;
          repeatCounts.set(key, count);
          
          // 检查是否达到违规次数
          if (count >= config.violationCount) {
            repeatedSentences.push({
              current: currentSentence,
              similar: previousSentence,
              similarity,
              repeatCount: count
            });
          }
        }
      }
    }

    // 判断是否命中
    const matched = repeatedSentences.length > 0;
    
    let details = '';
    if (matched) {
      details = `检测到${repeatedSentences.length}处重复内容 - 相似度阈值：${(config.similarityThreshold * 100).toFixed(0)}%，检测窗口：${config.withinSentences}句`;
    } else {
      details = `未检测到重复内容 - 相似度阈值：${(config.similarityThreshold * 100).toFixed(0)}%，检测窗口：${config.withinSentences}句`;
    }

    setTestResult({
      matched,
      repeatedSentences,
      filteredSentences,
      exceptionMatches,
      details
    });
  };

  // 加载案例
  const loadCase = (caseConfig: ContextRepeatConfig, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🔄 上下文重复检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测当前句子与之前句子内容是否重复，有效识别冗余表达和重复沟通
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                🎯 进阶功能
              </span>
              <span className="text-sm text-gray-500">
                算子类型：文字检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <ContextRepeatConcepts />

            {/* 配置演示 */}
            <ContextRepeatConfigComponent 
              config={config}
              onConfigChange={setConfig}
            />

            {/* 实时测试 */}
            <ContextRepeatTester
              config={config}
              testText={testText}
              onTestTextChange={setTestText}
              testResult={testResult}
              onRunTest={runTest}
              onClearResult={() => setTestResult(null)}
            />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <ContextRepeatCases onLoadCase={loadCase} />
            
            {/* 使用提示 */}
            <ContextRepeatTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-orange-900 mb-2">
                学习建议
              </h3>
              <div className="text-orange-800 space-y-2">
                <p>• <strong>理解算法原理</strong>：掌握编辑距离和相似度计算的基本概念</p>
                <p>• <strong>调优参数设置</strong>：根据业务场景调整相似度阈值和检测窗口</p>
                <p>• <strong>合理设置例外</strong>：为常见的标准用语设置例外，避免误判</p>
                <p>• <strong>关注检测效果</strong>：平衡检测精度和业务需求，找到最佳配置</p>
                <p>• <strong>结合其他算子</strong>：可与关键词检查、文本相似度等算子组合使用</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextRepeatPage; 