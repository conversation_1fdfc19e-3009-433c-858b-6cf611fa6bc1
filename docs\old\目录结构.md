    1 D:/Resources/Cursor/rule-configuration-system/      # ➡️ 项目根目录
    2 ├─── .cursorindexingignore                        # - (Cursor编辑器配置) 指示Cursor AI功能应忽略的文件
    3 ├─── .gitignore                                   # - (Git配置) 定义了哪些文件或目录不应被Git版本控制
    4 ├─── .specstory/                                  # - (Spec-Story工具目录) 用于记录和管理项目需求与开发历史
    5 │    ├─── .project.json                          #   - Spec-Story的项目配置文件
    6 │    ├─── .what-is-this.md                       #   - Spec-Story工具的说明文件
    7 │    └─── history/                               #   - 存放开发历史记录的Markdown文件
    8 │         ├─── 2025-06-24_01-06-补全页面条件配置.md #     - (开发日志) 记录了某次具体开发任务
    9 │         ├─── ... (其他开发历史文件)
   10 │
   11 ├─── src/                                         # ➡️ 【源代码目录】应用的核心逻辑与起点
   12 │    ├─── index.tsx                              #   - React应用的入口文件，将App组件挂载到HTML中
   13 │    └─── styles/                                #   - 存放全局CSS样式
   14 │         └─── custom.css                       #     - 自定义全局样式表
   15 │
   16 ├─── components/                                  # ➡️ 【组件目录】存放所有React组件，是项目的绝对核心
   17 │    ├─── common/                                #   - 存放与业务无关的、高度复用的【通用基础组件】
   18 │    │    ├─── ConfirmationModal.tsx             #     - 通用的确认对话框/弹窗组件
   19 │    │    ├─── MermaidDiagram.tsx                #     - 用于渲染Mermaid流程图的组件
   20 │    │    ├─── ScrollToTop.tsx                   #     - 点击后返回页面顶部的按钮组件
   21 │    │    └─── Tooltip.tsx                       #     - 鼠标悬浮提示框组件
   22 │    │
   23 │    ├─── guides/                                #   - 存放【引导性组件】，用于在页面上向用户展示帮助信息
   24 │    │    ├─── ReviewStrategyDesignGuide.tsx     #     - 复核策略设计的引导说明组件
   25 │    │    └─── TaskManagementDesignGuide.tsx     #     - 任务管理设计的引导说明组件
   26 │    │
   27 │    ├─── layout/                                #   - 存放【布局组件】，负责应用的整体页面框架
   28 │    │    ├─── MainLayout.tsx                    #     - 主布局，组合了导航、侧边栏和内容区
   29 │    │    ├─── Navigation.tsx                    #     - 顶部导航栏组件
   30 │    │    └─── Sidebar.tsx                       #     - 侧边菜单栏组件
   31 │    │
   32 │    ├─── pages/                                 #   - 存放【页面级组件】，代表用户能访问的各个独立页面/功能模块
   33 │    │    ├─── operators/                        #     - ❗【原子规则配置页】存放所有“质检算子”的配置页面，是规则系统的基石
   34 │    │    │    ├─── AbnormalAnswerPage.tsx       #       - “异常回答”算子的配置页
   35 │    │    │    ├─── CallSilenceCheckPage.tsx      #       - “通话静音”算子的配置页
   36 │    │    │    ├─── KeywordCheckPage.tsx          #       - “关键词”算子的配置页
   37 │    │    │    └─── ... (其他几十种算子配置页面)
   38 │    │    ├─── appeals/                          #     - (目录) 申诉相关页面
   39 │    │    ├─── demos/                             #     - (目录) 用于演示特定功能的页面
   40 │    │    ├─── employee-growth/                  #     - (目录) 员工成长中心相关页面
   41 │    │    ├─── examples/                         #     - (目录) 完整的、复杂规则的示例页面
   42 │    │    ├─── qa-workbench/                     #     - (目录) 质检工作台（如申诉处理）相关页面
   43 │    │    ├─── reports/                          #     - (目录) 报表相关页面
   44 │    │    ├─── AgentMonitoringDashboardPage.tsx  #     - 坐席监控看板页面
   45 │    │    ├─── HomePage.tsx                      #     - 应用首页
   46 │    │    ├─── RuleCompositionPage.tsx           #     - 规则编排/组合页面
   47 │    │    ├─── RuleLibraryPage.tsx               #     - 规则库页面
   48 │    │    ├─── TaskManagementPage.tsx            #     - 任务管理页面
   49 │    │    └─── ... (其他功能页面)
   50 │    │
   51 │    ├─── BasicInfoSection.tsx                   #   - (业务组件) 创建规则时的“基本信息”表单部分
   52 │    ├─── ConditionConfigSection.tsx             #   - (业务组件) 创建规则时的“条件配置”部分
   53 │    ├─── CreateRuleForm.tsx                     #   - (业务组件) 创建一个完整质检规则的主表单
   54 │    ├─── RuleVisualization.tsx                  #   - (业务组件) 用于将规则逻辑可视化展示
   55 │    ├─── icons.tsx                              #   - 定义或导出项目中使用的图标
   56 │    └─── ... (其他各类UI和业务组件)
   57 │
   58 ├─── docs/                                        # ➡️ 【文档目录】存放项目的设计、说明和使用指南
   59 │    ├─── 质检算子/                              #   - ❗【核心文档】对每种“算子”的详细图文说明
   60 │    │    ├─── 模型检查/                         #     - “模型检查”类算子的文档
   61 │    │    ├─── 文字检查/                         #     - “文字检查”类算子的文档
   62 │    │    └─── 语音检查/                         #     - “语音检查”类算子的文档
   63 │    ├─── 大模型质检规则创建.md                  #   - (文档) 关于如何使用大模型创建规则的说明
   64 │    ├─── 质检规则组成说明.md                    #   - (文档) 解释规则是如何由算子构成的
   65 │    ├─── 使用流程.md                            #   - (文档) 系统的整体使用流程图文说明
   66 │    ├─── 开发规范.md                            #   - (文档) 团队的开发约定和代码规范
   67 │    └─── ... (其他 .md 和 .png/.svg 设计图/文档)
   68 │
   69 ├─── App.tsx                                      # - (React) 应用的根组件，负责集成路由和渲染顶层页面
   70 ├─── App_old.tsx                                  # - (代码文件) 旧的应用根组件备份
   71 ├─── index.html                                   # - (HTML) 单页面应用的入口HTML文件，React将在此挂载
   72 ├─── package.json                                 # - (NPM) 项目的“身份证”，定义名称、版本、依赖库和脚本
   73 ├─── package-lock.json                            # - (NPM) 锁定项目依赖的精确版本，保证环境一致性
   74 ├─── tsconfig.json                                # - (TypeScript) TS编译器的配置文件
   75 ├─── types.ts                                     # - (TypeScript) 全局共享的TS类型定义文件
   76 ├─── vite.config.ts                               # - (Vite) 前端构建工具Vite的配置文件
   77 ├─── README.md                                    # - (文档) 项目的说明文件，通常包含简介和启动指南
   78 └─── metadata.json                                # - (元数据) 可能用于存储项目或编辑器相关的元信息