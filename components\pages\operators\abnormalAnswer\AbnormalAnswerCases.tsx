import React from 'react';

/**
 * 非正常接听案例接口
 */
interface AbnormalAnswerCasesProps {
  loadCase: (config: any, testText: string) => void;
}

/**
 * 非正常接听预设案例组件
 * @constructor
 */
const AbnormalAnswerCases: React.FC<AbnormalAnswerCasesProps> = ({ loadCase }) => {
  const cases = [
    {
      name: '客服响应过慢',
      description: '检测客服是否在5秒后才响应',
      config: {
        operator: '>' as const,
        duration: 5,
        role: 'agent' as const,
      },
      testText: '[00:06] 客服：您好，久等了。\n[00:08] 客户：你好。',
      expectedResult: '应该命中'
    },
    {
      name: '客户先开口',
      description: '检测客户是否先说话，且客服在3秒后才说话',
      config: {
        operator: '>' as const,
        duration: 3,
        role: 'agent' as const,
      },
      testText: '[00:02] 客户：喂？有人吗？\n[00:04] 客服：您好，在的。',
      expectedResult: '应该命中（因为第一句话是客户说的，不符合角色为客服的设定，且客服响应超过3秒）'
    },
    {
      name: '正常快速响应',
      description: '检测客服是否在3秒内响应',
      config: {
        operator: '<=' as const,
        duration: 3,
        role: 'agent' as const,
      },
      testText: '[00:02] 客服：您好，很高兴为您服务。\n[00:04] 客户：你好。',
      expectedResult: '应该命中'
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">📂 案例库</h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
                <p className="text-xs text-blue-500 mt-1">{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c.config, c.testText)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 flex-shrink-0 ml-4"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AbnormalAnswerCases; 