import React, { useState, useMemo } from 'react';
import {
    Calendar,
    Users,
    FileCheck,
    BarChart3,
    AlertTriangle,
    Award,
    Download,
    RefreshC<PERSON>,
    Brain,
    UserCheck,
    MessageSquareX,
    Target,
    Zap
} from 'lucide-react';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { EnhancedKPICard } from './common/EnhancedKPICard';
import { EnhancedChartContainer } from './common/EnhancedChartContainer';
import { EnhancedRankingCard } from './common/EnhancedRankingCard';
import { EnhancedFilterSection } from './common/EnhancedFilterSection';
import { EnhancedSectionHeader } from './common/EnhancedSectionHeader';
import {
    ComposedChart,
    Line,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    BarChart,
    Cell
} from 'recharts';



interface TrendData {
    date: string;
    aiTotal: number;
    avgScore: number;
}

interface RankingItem {
    name: string;
    value: number;
    percentage?: number;
    count?: number;
    team?: string;
}





/**
 * 质检运营总览页面
 */
export const FinalQualityOperationOverviewPage: React.FC = () => {
    const [timeRange, setTimeRange] = useState('本月');
    const [selectedTeam, setSelectedTeam] = useState('全部班组');
    const [selectedScheme, setSelectedScheme] = useState('全部方案');
    const [rankingType, setRankingType] = useState<'team' | 'agent'>('team');

    // 质检运营总览页面设计说明 (业务角度)
    const designGuideContent = (
        <div className="space-y-4 text-gray-700 text-sm leading-relaxed">
            <h2 className="text-xl font-bold text-gray-800">页面：质检运营总览 (FinalQualityOperationOverviewPage.tsx) - 业务设计说明</h2>

            <h3 className="text-lg font-semibold text-gray-700 mt-4">1. 核心定位与目标</h3>
            <p>
                质检运营总览页面是为<b>最高层管理者（如质检主管、运营总监）</b>设计的<b>宏观驾驶舱</b>。
                其核心目标是提供一个高度概括的、可视化的界面，用于监控整个质检体系的
                <b>运营健康度、效率和核心风险</b>，帮助管理者快速把握整体态势，并发现需要进一步关注的领域。
            </p>

            <h3 className="text-lg font-semibold text-gray-700 mt-4">2. 主要功能模块与内容</h3>

            <h4 className="text-md font-semibold text-gray-700 mt-3">a. 页面顶部区域</h4>
            <p>此区域提供页面的核心概览信息和全局操作入口：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>标题</b>: "质检运营总览"，清晰定义页面的核心职能。</li>
                <li><b>副标题</b>: "从最高维度宏观监控整体质检工作的健康度、AI运行效率和核心风险，为管理决策提供数据支持"，阐述页面提供的核心业务价值。</li>
                <li><b>角色标识</b>: 使用条形图图标 (<code className="bg-gray-100 px-1 py-0.5 rounded text-red-500">BarChart3</code>)，直观代表宏观统计与分析的功能属性。</li>
                <li><b>核心操作</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>刷新数据</b>: 允许管理者获取最新的运营数据，确保信息实时性。</li>
                        <li><b>导出报表</b>: 提供将当前报表数据导出为可离线分析的格式，方便后续深入分析或汇报。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">b. 统一搜索筛选器</h4>
            <p>此模块提供灵活的数据筛选功能，允许管理者聚焦特定业务维度进行分析：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>目的</b>: 允许管理者对整个报表的数据范围进行精细化筛选，以进行更具针对性的业务分析和问题定位。</li>
                <li><b>筛选字段及业务含义</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>时间范围</b>: 作为<b>最重要的筛选条件</b>，用于定义当前报表的统计周期（例如：今日、本周、本月、自定义日期范围）。管理者可通过调整时间范围，观察业务指标在不同时间粒度下的波动和趋势。</li>
                        <li><b>班组/团队</b>: 允许管理者将数据下钻到特定班组或团队的运营表现，便于对不同团队进行横向对比分析，发现优秀团队或需要改进的团队。</li>
                        <li><b>质检方案</b>: 可根据已应用的质检方案进行数据筛选，用于评估特定质检方案在实际业务中的执行效果和其对质检结果的影响。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">c. 核心KPI指标卡片（全局核心指标概览）</h4>
            <p>此模块以直观的卡片形式展示了整个质检体系的**全局核心绩效指标**，旨在提供质检运营的整体健康度和效率概览。所有指标的数据均为系统自动聚合，反映最高维度的业务表现。指标内容、业务含义及计算实现方式如下：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>总质检量</b>:
                    <p>业务含义：衡量AI质检系统的覆盖广度和工作效率。</p>
                    <p>计算/实现：统计在选定时间范围内，系统AI完成初次质检的通话总量。</p>
                </li>
                <li><b>总复核量</b>:
                    <p>业务含义：反映人工质检（复核）工作的投入成本和工作量。</p>
                    <p>计算/实现：统计在选定时间范围内，人工复核员完成的复核任务总数。</p>
                </li>
                <li><b>复核率</b>:
                    <p>业务含义：人工审核任务占总质检量的比例，是平衡质检成本与质量精度的关键运营指标。</p>
                    <p>计算/实现：<code>(总复核量 / 总质检量) × 100%</code>。</p>
                </li>
                <li><b>整体平均分</b>:
                    <p>业务含义：反映所有被质检通话的平均服务质量水平，是全局服务质量的基准线。</p>
                    <p>计算/实现：<code>所有质检通话的最终得分总和 / 质检通话总数</code>。</p>
                </li>
                <li><b>整体合格率</b>:
                    <p>业务含义：衡量整体服务质量达标情况，即有多少通话达到了预设的合格标准。</p>
                    <p>计算/实现：<code>(质检结果达到合格标准的通话数量 / 质检通话总数) × 100%</code>。</p>
                </li>
                <li><b>整体申诉率</b>:
                    <p>业务含义：衡量质检结果的公正性和规则的合理性，是全局质检公信力的重要指标。</p>
                    <p>计算/实现：<code>(总申诉次数 / 总质检量) × 100%</code>。</p>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">d. 运营趋势分析（质量与效率组合图表）</h4>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>图表标题</b>: "质量与效率趋势分析"。</li>
                <li><b>内容</b>: 通过一个组合图表，直观展示两个不同量纲的核心业务指标在时间维度上的变化趋势。</li>
                <li><b>图表构成与实现</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>总质检量（柱状图）</b>: 以柱状图形式展示每日或每周的质检量，反映工作负荷的波动情况。数据来源于系统每日/每周的质检任务完成量统计。</li>
                        <li><b>整体平均分（折线图）</b>: 以折线图形式叠加在柱状图之上，展示同期所有质检通话的平均得分趋势。数据来源于系统每日/每周质检得分的汇总计算。</li>
                    </ul>
                </li>
                <li><b>业务价值</b>: 管理者可以非常直观地分析"质检量"与"服务质量得分"之间的内在联系。例如，当业务高峰期（质检量显著增加）时，服务质量（平均分）是否出现下滑？这种洞察为质检资源的动态调配、风险预警机制的优化以及质检策略的调整提供了强有力的数据支撑。</li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">e. 问题风险聚焦（排名卡片组）</h4>
            <p>此模块旨在帮助管理者快速定位最突出的业务问题和在质检表现上优秀或有待提升的团队/个人。</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>左侧卡片 - 高频失分规则 TOP 10</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 列出在选定时间范围内，导致全公司坐席累计失分次数最多的10条质检规则。</li>
                        <li><b>业务价值与实现</b>: 这些规则直接指明了当前服务流程或员工培训中最普遍存在的短板和痛点。通过识别这些高频失分项，质检主管可以有针对性地安排培训、优化SOP（标准作业程序）或调整质检规则，以提升整体服务质量。数据来源于质检系统中所有扣分规则的命中统计与聚合。</li>
                    </ul>
                </li>
                <li><b>中间卡片 - 严重错误规则 TOP 10</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 列出在全公司范围内，被触发次数最多的"严重违规"级别质检规则。</li>
                        <li><b>业务价值与实现</b>: 这些严重错误规则暴露了公司当前面临的核心合规风险或不可触犯的服务红线问题。质检主管需要对此类问题保持高度警惕，并迅速采取措施进行干预和纠正。数据来源于系统设定的严重违规规则的命中统计。</li>
                    </ul>
                </li>
                <li><b>右侧卡片 - 平均分排名（可切换）</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 这是一个<b>可切换</b>的排名榜，支持管理者从不同维度查看绩效表现。
                            <ul className="list-[square] pl-6 space-y-1">
                                <li>默认显示<b>团队排名</b>: 按照平均分对所有质检班组进行降序排序。</li>
                                <li>提供"团队/坐席"切换按钮，点击后可切换为显示<b>坐席排名</b>: 在所有坐席中按照平均分进行降序排序。</li>
                            </ul>
                        </li>
                        <li><b>业务价值与实现</b>: 此排名功能帮助质检主管快速识别出在质检表现方面优异的团队和个人（标杆），同时也能发现有待提升的团队和坐席。这为绩效评估、激励机制的制定、优秀经验的推广以及针对性辅导和资源倾斜提供了重要的业务参考。数据来源于各团队及坐席的质检平均分汇总与排序。</li>
                    </ul>
                </li>
            </ul>

            <h3 className="text-lg font-semibold text-gray-700 mt-4">3. 核心交互与操作</h3>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>宏观洞察</b>: 页面所有图表和数据均为全局聚合性质，旨在为最高层管理者提供"一览无余"的顶层业务视图，支持其对整体运营态势的快速、准确把握。</li>
                <li><b>数据筛选与下钻</b>: 通过页面顶部的统一筛选器，管理者能够灵活地将宏观视图聚焦到特定的业务线、时间范围或团队，从而进行初步的下钻分析，以探究数据背后的具体原因。</li>
                <li><b>问题导向与快速发现</b>: 页面的设计思路强调"问题导向"，通过核心KPI、趋势分析和各类排名榜，主动且直观地将最值得关注的"亮点"和"痛点"业务信息呈现给管理者，提高问题发现的效率。</li>
                <li><b>决策支持</b>: 质检运营总览页面是管理层制定季度/月度质检目标、评估质检工作实际成效、以及动态调整运营策略的核心数据来源。它提供的数据支持能够确保管理决策的科学性和有效性。</li>
            </ul>
        </div>
    );

    // 模拟KPI数据
    const kpiData = useMemo(() => [
        {
            title: '总质检量',
            value: '15,234',
            unit: '通',
            trend: 'up' as const,
            trendValue: '+12.5%',
            icon: Brain,
            gradient: 'bg-gradient-to-br from-blue-500 to-blue-600',
            description: '周期内AI完成初检的通话总量'
        },
        {
            title: '总复核量',
            value: '2,847',
            unit: '通',
            trend: 'up' as const,
            trendValue: '+8.3%',
            icon: UserCheck,
            gradient: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
            description: '周期内人工完成复核的总量'
        },
        {
            title: '复核率',
            value: '18.7',
            unit: '%',
            trend: 'down' as const,
            trendValue: '-2.1%',
            icon: Target,
            gradient: 'bg-gradient-to-br from-purple-500 to-purple-600',
            description: '人工审核的投入比例'
        },
        {
            title: '整体平均分',
            value: '87.2',
            unit: '分',
            trend: 'up' as const,
            trendValue: '+1.8%',
            icon: Award,
            gradient: 'bg-gradient-to-br from-orange-500 to-orange-600',
            description: '所有质检通话的最终得分平均值'
        },
        {
            title: '整体合格率',
            value: '92.4',
            unit: '%',
            trend: 'up' as const,
            trendValue: '+3.2%',
            icon: Zap,
            gradient: 'bg-gradient-to-br from-teal-500 to-teal-600',
            description: '最终得分达到合格标准的通话占比'
        },
        {
            title: '整体申诉率',
            value: '4.6',
            unit: '%',
            trend: 'down' as const,
            trendValue: '-0.8%',
            icon: MessageSquareX,
            gradient: 'bg-gradient-to-br from-red-500 to-red-600',
            description: '总申诉数占总质检量的比例'
        }
    ], []);

    // 模拟趋势数据
    const trendData: TrendData[] = useMemo(() => [
        { date: '01-01', aiTotal: 1200, avgScore: 85.2 },
        { date: '01-02', aiTotal: 1350, avgScore: 86.1 },
        { date: '01-03', aiTotal: 1180, avgScore: 84.8 },
        { date: '01-04', aiTotal: 1420, avgScore: 87.3 },
        { date: '01-05', aiTotal: 1380, avgScore: 86.9 },
        { date: '01-06', aiTotal: 1250, avgScore: 85.7 },
        { date: '01-07', aiTotal: 1100, avgScore: 88.1 },
        { date: '01-08', aiTotal: 1480, avgScore: 87.8 },
        { date: '01-09', aiTotal: 1520, avgScore: 88.5 },
        { date: '01-10', aiTotal: 1390, avgScore: 87.2 }
    ], []);

    // 模拟高频失分规则数据
    const highFreqRules: RankingItem[] = useMemo(() => [
        { name: '未主动询问客户需求', value: 0, count: 342, percentage: 22.4 },
        { name: '服务用语不规范', value: 0, count: 298, percentage: 19.5 },
        { name: '通话结束未确认客户满意度', value: 0, count: 276, percentage: 18.1 },
        { name: '等待时间过长未道歉', value: 0, count: 234, percentage: 15.3 },
        { name: '未核实客户身份信息', value: 0, count: 198, percentage: 13.0 },
        { name: '转接前未告知客户', value: 0, count: 156, percentage: 10.2 },
        { name: '重复询问相同问题', value: 0, count: 143, percentage: 9.4 },
        { name: '未提供解决方案', value: 0, count: 128, percentage: 8.4 },
        { name: '语速过快影响理解', value: 0, count: 112, percentage: 7.3 },
        { name: '未记录客户关键信息', value: 0, count: 98, percentage: 6.4 }
    ], []);

    // 模拟严重错误规则数据
    const seriousErrorRules: RankingItem[] = useMemo(() => [
        { name: '泄露客户隐私信息', value: 0, count: 23, percentage: 28.4 },
        { name: '提供错误的产品信息', value: 0, count: 19, percentage: 23.5 },
        { name: '未经授权承诺优惠', value: 0, count: 15, percentage: 18.5 },
        { name: '恶意中断客户通话', value: 0, count: 12, percentage: 14.8 },
        { name: '使用不当言辞', value: 0, count: 8, percentage: 9.9 },
        { name: '违反合规操作流程', value: 0, count: 4, percentage: 4.9 },
        { name: '未经客户同意录音', value: 0, count: 3, percentage: 3.7 },
        { name: '擅自修改客户资料', value: 0, count: 2, percentage: 2.5 },
        { name: '恶意诱导客户消费', value: 0, count: 2, percentage: 2.5 },
        { name: '泄露公司机密信息', value: 0, count: 1, percentage: 1.2 }
    ], []);

    // 模拟团队排名数据
    const teamRanking: RankingItem[] = useMemo(() => [
        { name: 'A班组', value: 91.2, percentage: 94.5 },
        { name: 'B班组', value: 89.8, percentage: 92.1 },
        { name: 'C班组', value: 88.4, percentage: 90.3 },
        { name: 'D班组', value: 86.9, percentage: 88.7 },
        { name: 'E班组', value: 85.1, percentage: 86.2 },
        { name: 'F班组', value: 83.7, percentage: 84.8 },
        { name: 'G班组', value: 82.3, percentage: 82.9 },
        { name: 'H班组', value: 80.9, percentage: 80.5 }
    ], []);

    // 模拟坐席排名数据
    const agentRanking: RankingItem[] = useMemo(() => [
        { name: '张小明', value: 95.8, percentage: 98.2, team: 'A班组' },
        { name: '李小红', value: 94.6, percentage: 96.8, team: 'A班组' },
        { name: '王小强', value: 93.2, percentage: 95.1, team: 'B班组' },
        { name: '刘小芳', value: 92.7, percentage: 94.3, team: 'B班组' },
        { name: '陈小华', value: 91.9, percentage: 93.5, team: 'C班组' },
        { name: '赵小军', value: 90.8, percentage: 92.1, team: 'C班组' },
        { name: '孙小丽', value: 89.6, percentage: 90.8, team: 'D班组' },
        { name: '周小伟', value: 88.4, percentage: 89.2, team: 'D班组' },
        { name: '吴小燕', value: 87.1, percentage: 87.9, team: 'E班组' },
        { name: '郑小东', value: 85.9, percentage: 86.4, team: 'E班组' }
    ], []);

    // Filter fields configuration
    const filterFields = [
        {
            key: 'timeRange',
            label: '时间范围',
            type: 'select' as const,
            value: timeRange,
            options: [
                { value: '今日', label: '今日' },
                { value: '本周', label: '本周' },
                { value: '本月', label: '本月' },
                { value: '自定义', label: '自定义' }
            ],
            onChange: setTimeRange
        },
        {
            key: 'selectedTeam',
            label: '班组/团队',
            type: 'select' as const,
            value: selectedTeam,
            options: [
                { value: '全部班组', label: '全部班组' },
                { value: 'A班组', label: 'A班组' },
                { value: 'B班组', label: 'B班组' },
                { value: 'C班组', label: 'C班组' },
                { value: 'D班组', label: 'D班组' }
            ],
            onChange: setSelectedTeam
        },
        {
            key: 'selectedScheme',
            label: '质检方案',
            type: 'select' as const,
            value: selectedScheme,
            options: [
                { value: '全部方案', label: '全部方案' },
                { value: '金融合规质检方案', label: '金融合规质检方案' },
                { value: '客服标准质检方案', label: '客服标准质检方案' },
                { value: '销售质检方案', label: '销售质检方案' }
            ],
            onChange: setSelectedScheme
        }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
            <UnifiedPageHeader
                title="质检运营总览"
                subtitle="从最高维度宏观监控整体质检工作的健康度、AI运行效率和核心风险，为管理决策提供数据支持"
                icon={BarChart3}
                badge={{ text: "运营监控", color: "blue" }}
                actions={[
                    {
                        label: "刷新数据",
                        icon: RefreshCw,
                        variant: "secondary",
                        onClick: () => console.log('刷新数据')
                    },
                    {
                        label: "导出报表",
                        icon: Download,
                        variant: "primary",
                        onClick: () => console.log('导出报表')
                    }
                ]}
                showDesignGuide={true}
                designGuideContent={designGuideContent}
            />

            <main className="p-6 space-y-8">
                {/* Enhanced Filter Section */}
                <EnhancedFilterSection
                    fields={filterFields}
                    onSearch={() => console.log('搜索')}
                    onReset={() => {
                        setTimeRange('本月');
                        setSelectedTeam('全部班组');
                        setSelectedScheme('全部方案');
                    }}
                />

                {/* Enhanced KPI Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {kpiData.map((kpi, index) => (
                        <EnhancedKPICard
                            key={index}
                            delay={index * 0.1}
                            {...kpi}
                        />
                    ))}
                </div>

                {/* Enhanced Trend Analysis Chart */}
                <EnhancedChartContainer
                    title="质量与效率趋势分析"
                    subtitle="监控质检量与服务质量的关联变化"
                    icon={BarChart3}
                    delay={0.3}
                >
                    <ResponsiveContainer width="100%" height="100%">
                        <ComposedChart data={trendData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                            <XAxis
                                dataKey="date"
                                tick={{ fontSize: 12, fill: '#64748b' }}
                                tickLine={{ stroke: '#e2e8f0' }}
                                axisLine={{ stroke: '#e2e8f0' }}
                            />
                            <YAxis
                                yAxisId="left"
                                tick={{ fontSize: 12, fill: '#64748b' }}
                                tickLine={{ stroke: '#e2e8f0' }}
                                axisLine={{ stroke: '#e2e8f0' }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                tick={{ fontSize: 12, fill: '#64748b' }}
                                tickLine={{ stroke: '#e2e8f0' }}
                                axisLine={{ stroke: '#e2e8f0' }}
                            />
                            <Tooltip
                                contentStyle={{
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                    border: '1px solid #e2e8f0',
                                    borderRadius: '12px',
                                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                                    backdropFilter: 'blur(8px)'
                                }}
                            />
                            <Legend
                                wrapperStyle={{
                                    paddingTop: '20px'
                                }}
                            />
                            <Bar
                                yAxisId="left"
                                dataKey="aiTotal"
                                fill="url(#blueGradient)"
                                name="总质检量"
                                radius={[4, 4, 0, 0]}
                            />
                            <Line
                                yAxisId="right"
                                type="monotone"
                                dataKey="avgScore"
                                stroke="#f59e0b"
                                strokeWidth={3}
                                name="整体平均分"
                                dot={{ fill: '#f59e0b', strokeWidth: 2, r: 5, stroke: '#fff' }}
                                activeDot={{ r: 7, stroke: '#f59e0b', strokeWidth: 2, fill: '#fff' }}
                            />
                            <defs>
                                <linearGradient id="blueGradient" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8}/>
                                    <stop offset="100%" stopColor="#3b82f6" stopOpacity={0.3}/>
                                </linearGradient>
                            </defs>
                        </ComposedChart>
                    </ResponsiveContainer>
                </EnhancedChartContainer>

                {/* Enhanced Problem Risk Focus Section */}
                <div className="space-y-8">
                    <EnhancedSectionHeader
                        title="问题风险聚焦"
                        subtitle="识别高频问题和潜在风险点"
                        icon={AlertTriangle}
                        iconColor="text-red-600"
                        iconBgColor="bg-gradient-to-br from-red-100 to-rose-100"
                        delay={0.4}
                    />

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <EnhancedRankingCard
                            title="高频失分规则 TOP 10"
                            subtitle="最常见的服务质量问题"
                            data={highFreqRules}
                            type="rules"
                            icon={AlertTriangle}
                            iconColor="text-red-600"
                            iconBgColor="bg-gradient-to-br from-red-100 to-rose-100"
                            delay={0.5}
                        />

                        <EnhancedRankingCard
                            title="严重错误规则 TOP 10"
                            subtitle="需要重点关注的严重违规"
                            data={seriousErrorRules}
                            type="rules"
                            icon={AlertTriangle}
                            iconColor="text-orange-600"
                            iconBgColor="bg-gradient-to-br from-orange-100 to-amber-100"
                            delay={0.6}
                        />

                        <EnhancedRankingCard
                            title="平均分排名"
                            subtitle="团队和个人表现排行"
                            data={rankingType === 'team' ? teamRanking : agentRanking}
                            type="teams"
                            icon={Users}
                            iconColor="text-blue-600"
                            iconBgColor="bg-gradient-to-br from-blue-100 to-indigo-100"
                            showToggle={true}
                            toggleType={rankingType}
                            onToggleChange={setRankingType}
                            delay={0.7}
                        />
                    </div>
                </div>
            </main>
        </div>
    );
};

export default FinalQualityOperationOverviewPage;