好的，我们来详细设计【复核策略配置】模块的前端页面。

---

### **2. 复核策略配置 - 前端页面设计**

我们将设计两个核心页面：**策略列表页**和**新建/编辑策略页**。

---

#### **1. 复核策略配置 - 策略列表页**

**页面目标:** 集中展示和管理所有的复核策略，让管理者可以快速了解当前生效的策略，并进行启用、停用等操作。

**页面布局:**

采用简洁的"操作区 + 列表区"布局。

##### **1.1 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 复核策略配置`
*   **页面标题:** `复核策略配置`
*   **主要操作按钮:**
    *   `+ 新建策略` (醒目的蓝色主按钮)

##### **1.2 策略列表区**

使用卡片列表或表格（Table）组件展示数据。卡片式布局更现代，信息密度稍低但更清晰；表格布局信息密度高，适合专业用户。这里我们以功能更全面的**表格**为例进行设计。

**表格列 (Columns):**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **策略名称** | - 显示用户自定义的策略名称，如"低分录音复核策略"。<br>- 下方可有一行灰色小字，显示策略的简要描述。 |
| **触发条件 (摘要)** | - **核心信息，需简洁明了地概括规则。**<br>- 示例1: `总分 < 60分` <br>- 示例2: `命中标签 [严重违规] 或 [客户投诉风险]` <br>- 示例3: `随机抽取 5%` <br>- 如果条件过多，则显示前两条，并加上"...等N个条件"，鼠标悬浮时用弹窗（Tooltip）显示全部条件。 |
| **分配规则** | - 显示复核任务的分配方式。<br>- 示例1: `轮询分配给 [复核组A]` <br>- 示例2: `分配给 [被检坐席的班组长]` |
| **状态** | - **非常重要的操作列。**<br>- 使用一个"**开关 (Switch)**"组件，直观地显示和控制策略的 `启用` / `停用` 状态。用户点击开关即可切换，系统会给出"操作成功"的提示。 |
| **创建信息** | - 显示策略的 `创建人` 和 `创建时间`。 |
| **操作** | - `编辑`: 点击后跳转到"编辑策略"页面，页面布局与"新建"页相同，但填充了现有数据。<br>- `复制`: 点击后跳转到"新建策略"页面，并自动填充此策略的所有配置，方便微调后创建新策略。<br>- `删除`: 点击后弹出二次确认对话框，防止误删。 |

---

#### **2. 复核策略配置 - 新建/编辑策略页**

**页面目标:** 提供一个结构化、引导性强的界面，让用户能够通过组合不同的条件和动作，创建出强大而精准的复核策略。

**设计理念:** 采用"**IF...THEN...**"的逻辑结构，将页面分为"**触发条件**"和"**执行分配**"两大块，非常符合人类的自然思维方式。

##### **2.1 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 复核策略配置 / 新建策略`
*   **页面标题:** `新建复核策略`

##### **2.2 表单区域**

**第一部分：基本信息**

*   **策略名称:** `[输入框]`，必填项 `*`。右侧有字数限制提示。
*   **策略描述:** `[文本域 Textarea]`，选填项。

**第二部分：触发条件 (IF)**

这是一个动态表单区域，核心是"**规则构建器**"。

*   **卡片标题:** `当满足以下条件时，自动推送进行人工复核`
*   **条件逻辑:**
    *   在卡片右上角有一个下拉框 `[满足所有条件(AND) / 满足任一条件(OR)]`，用于定义多个条件之间的关系。
*   **规则行:**
    *   默认有一行规则，用户可以点击 `[+ 添加条件]` 按钮动态增加。
    *   每一行代表一个独立的条件，由 `[条件类型]` + `[操作符]` + `[值]` 构成。
    *   **第一列 (条件类型):** `[下拉选择框]`
        *   `AI质检总分`
        *   `AI质检单项得分`
        *   `命中AI标签`
        *   `通话时长`
        *   `静音时长占比`
        *   `随机抽样`
    *   **第二、三列 (操作符和值):** 根据第一列的选择动态变化。
        *   **如果选"AI质检总分":**
            *   第二列是 `[下拉框: <、<=、>、>=、=、!=]`
            *   第三列是 `[数字输入框]` (可输入0-100)
        *   **如果选"AI质检单项得分":**
            *   第二列是 `[选择框: 选择评分项]` (如"服务开场白")
            *   第三列是 `[下拉框: =]`
            *   第四列是 `[数字输入框]` (通常填 `0`)
        *   **如果选"命中AI标签":**
            *   第二列是 `[下拉框: 包含]`
            *   第三列是 `[标签选择器]` (可多选，如"严重违规"、"承诺超纲")
        *   **如果选"随机抽样":**
            *   第二列是 `[滑块 Slider 或 数字输入框]`，用于设置百分比。
            *   这一行比较特殊，通常不能与其他条件组合（即单独使用）。当选中此项时，其他条件行可以被禁用或隐藏。
    *   **行操作:** 每行规则的末尾都有一个 `[删除]` (垃圾桶图标) 按钮。

**第三部分：执行分配 (THEN)**

*   **卡片标题:** `将触发的复核任务按以下规则分配`
*   **分配模式:** `[下拉选择框]`，必填项 `*`
    *   `轮询分配`
        *   右侧出现 `[人员/角色选择器]`，用于选择要参与轮询的复核员或复核组。
    *   `指定分配`
        *   右侧出现 `[人员/角色选择器]`，用于选择唯一的接收人或接收组。
    *   `关联分配`
        *   右侧出现 `[下拉框]`，选项为：
            *   `分配给被检坐席的直属班组长`
            *   `分配给被检坐席的指定复核员` (此选项需要后台有坐席与复核员的绑定关系)

##### **2.3 页面底部操作区**

*   `保存策略` (蓝色主按钮)
*   `取消` (灰色文字链接)

---

### **设计亮点总结**

1.  **IF-THEN 逻辑:** 页面结构完全遵循"如果...那么..."的逻辑，直观且易于理解，大大降低了配置复杂规则的门槛。
2.  **动态与交互性:** "触发条件"区域是动态的，用户可以自由组合、增删条件，系统会根据用户选择动态改变后续的输入项，提供了极高的灵活性。
3.  **清晰的视觉引导:** 通过卡片式分区、必填项标识、灰色小字提示等方式，引导用户一步步完成配置，避免遗漏和混淆。
4.  **状态管理便捷:** 在列表页使用"开关 (Switch)"组件来管理策略的启用/停用状态，是目前业界最直观、高效的交互方式。
5.  **复用性设计:** "复制"功能的加入，使得创建一系列相似但略有不同的策略变得非常简单，提升了管理效率。