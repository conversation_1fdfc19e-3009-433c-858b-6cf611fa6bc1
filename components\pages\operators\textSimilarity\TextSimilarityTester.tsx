import React from 'react';
import { TextSimilarityConfig } from './TextSimilarityConfig';

interface TestResult {
  matched: boolean;
  bestMatch?: {
    template: string;
    similarity: number;
  };
  matchedSentences: string[];
  details: string;
}

interface TextSimilarityTesterProps {
  config: TextSimilarityConfig;
  testText: string;
  setTestText: (text: string) => void;
  testResult: TestResult | null;
  setTestResult: (result: TestResult | null) => void;
}

/**
 * 计算两个字符串的相似度（简化版编辑距离算法）
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  const len1 = str1.length;
  const len2 = str2.length;
  
  if (len1 === 0) return len2 === 0 ? 100 : 0;
  if (len2 === 0) return 0;
  
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));
  
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  
  const maxLen = Math.max(len1, len2);
  const similarity = ((maxLen - matrix[len1][len2]) / maxLen) * 100;
  return Math.round(similarity);
};

/**
 * 文本相似度实时测试组件
 */
const TextSimilarityTester: React.FC<TextSimilarityTesterProps> = ({ 
  config, 
  testText, 
  setTestText, 
  testResult, 
  setTestResult 
}) => {
  
  // 执行测试
  const runTest = () => {
    const sentences = testText.split('\n').filter(s => s.trim());
    let targetSentences: string[] = [];

    // 根据检测角色筛选句子
    if (config.detectionRole === 'agent') {
      targetSentences = sentences.filter(s => s.startsWith('客服：'));
    } else if (config.detectionRole === 'customer') {
      targetSentences = sentences.filter(s => s.startsWith('客户：'));
    } else {
      targetSentences = sentences;
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    if (config.templates.length === 0) {
      setTestResult({
        matched: false,
        matchedSentences: [],
        details: '未配置相似话术模板'
      });
      return;
    }

    const matchedSentences: string[] = [];
    let bestMatch: { template: string; similarity: number } | undefined;
    let maxSimilarity = 0;

    // 相似度匹配逻辑
    targetSentences.forEach(sentence => {
      const cleanSentence = sentence.replace(/^[^：]+：/, '').trim();
      
      if (config.singleSentence) {
        // 单句话内生效：按标点分割小句
                    const subSentences = cleanSentence.split(/[，。！？；：,.!?;:""'']/);
            subSentences.forEach(subSentence => {
          if (subSentence.trim()) {
            config.templates.forEach(template => {
              const similarity = calculateSimilarity(subSentence.trim(), template);
              if (similarity > maxSimilarity) {
                maxSimilarity = similarity;
                bestMatch = { template, similarity };
              }
              if (similarity >= config.similarityThreshold) {
                if (!matchedSentences.includes(sentence)) {
                  matchedSentences.push(sentence);
                }
              }
            });
          }
        });
      } else {
        // 整句检测
        config.templates.forEach(template => {
          const similarity = calculateSimilarity(cleanSentence, template);
          if (similarity > maxSimilarity) {
            maxSimilarity = similarity;
            bestMatch = { template, similarity };
          }
          if (similarity >= config.similarityThreshold) {
            if (!matchedSentences.includes(sentence)) {
              matchedSentences.push(sentence);
            }
          }
        });
      }
    });

    const matched = matchedSentences.length > 0;
    let details = '';
    
    if (matched) {
      details = `规则已命中 - 最高相似度：${maxSimilarity}%`;
      if (bestMatch) {
        details += `，匹配模板："${bestMatch.template}"`;
      }
    } else {
      details = `规则未命中 - 最高相似度：${maxSimilarity}%（需要：${config.similarityThreshold}%）`;
      if (bestMatch) {
        details += `，最接近模板："${bestMatch.template}"`;
      }
    }

    setTestResult({
      matched,
      bestMatch,
      matchedSentences,
      details
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              setTestText('客服：您好，请问有什么可以帮助您的吗？\n客户：我想咨询一下你们的产品信息。\n客服：好的，我来为您详细介绍一下。');
              setTestResult(null);
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* 测试文本输入区 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
              📝 测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {testText.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              id="test-text"
              rows={10}
              className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="请输入需要测试的对话文本，每行一句话...&#10;&#10;示例：&#10;客服：您好，请问有什么可以帮助您的？&#10;客户：我想咨询产品信息&#10;客服：好的，我来为您详细介绍"
            />
            {testText.trim() && (
              <button
                onClick={() => {
                  setTestText('');
                  setTestResult(null);
                }}
                className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="清空文本"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-4">
            <span>💡 提示：每行代表一句对话，使用"客服："或"客户："开头</span>
          </div>
        </div>

        {/* 快速测试按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={runTest}
            disabled={!testText.trim() || config.templates.length === 0}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>执行测试</span>
            </span>
          </button>
          {testResult && (
            <button
              onClick={() => setTestResult(null)}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>
      </div>
      
      {/* 测试结果 */}
      {testResult && (
        <div className="mt-8 space-y-6">
          {/* 结果总览 */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <span>📊</span>
                <span>测试结果</span>
              </h3>
              <button
                onClick={() => setTestResult(null)}
                className="text-sm text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* 主要结果卡片 */}
            <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                      {testResult.matched ? '✓' : '✗'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                      {testResult.matched ? '规则命中' : '规则未命中'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      testResult.matched 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {testResult.matched ? '✅ 通过' : '❌ 未通过'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 详细结果 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 最佳匹配 */}
            {testResult.bestMatch && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-blue-500">🎯</span>
                  <h4 className="font-semibold text-gray-800">最佳匹配</h4>
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                    {testResult.bestMatch.similarity}%
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="p-3 bg-blue-50 border border-blue-100 rounded-md">
                    <div className="text-sm text-gray-800 font-medium">
                      "{testResult.bestMatch.template}"
                    </div>
                    <div className="mt-1 text-xs text-blue-600">
                      相似度：{testResult.bestMatch.similarity}%
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 统计信息 */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-purple-500">📈</span>
                <h4 className="font-semibold text-gray-800">统计信息</h4>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">命中句子数</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {testResult.matchedSentences.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">配置模板数</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {config.templates.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">相似度阈值</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {config.similarityThreshold}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 命中句子详情 */}
          {testResult.matchedSentences.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-orange-500">💬</span>
                <h4 className="font-semibold text-gray-800">命中句子</h4>
                <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {testResult.matchedSentences.length} 条
                </span>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {testResult.matchedSentences.map((sentence, i) => (
                  <div key={i} className="p-3 bg-orange-50 border border-orange-100 rounded-md">
                    <div className="text-sm text-gray-800 font-medium">
                      {sentence}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 配置摘要 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-gray-500">⚙️</span>
              <h4 className="font-semibold text-gray-800">当前配置</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">检测角色:</span>
                  <span className="font-medium">{config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">检测范围:</span>
                  <span className="font-medium">{config.detectionScope === 'full' ? '全文' : `第${config.rangeStart}-${config.rangeEnd}句`}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">相似度阈值:</span>
                  <span className="font-medium">{config.similarityThreshold}%</span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">模板数量:</span>
                  <span className="font-medium">{config.templates.length}个</span>
                </div>
                {config.singleSentence && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">单句生效:</span>
                    <span className="font-medium text-green-600">✓ 启用</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextSimilarityTester;