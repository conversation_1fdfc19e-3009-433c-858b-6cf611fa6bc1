# 质检系统页面开发规范

## 0. 技术栈规范

### 0.1 核心技术栈
- 框架：React 18.x
- 语言：TypeScript 5.x
- 构建工具：Vite 5.x
- 包管理器：npm/yarn/pnpm

### 0.2 UI框架和样式
- 样式解决方案：Tailwind CSS 3.x
- UI组件：自研组件库
- 图标：自定义SVG图标

### 0.3 状态管理
- 局部状态：React Hooks (useState, useReducer)
- 全局状态：React Context
- 状态持久化：localStorage/sessionStorage

### 0.4 路由管理
- 路由方案：React Router v6
- 路由模式：Browser History Mode
- 路由守卫：自定义HOC实现

### 0.5 工具库
- 工具函数：lodash-es
- 日期处理：dayjs
- HTTP请求：axios

### 0.6 开发工具
- IDE：VS Code
- 代码规范：ESLint + Prettier
- Git工作流：Conventional Commits
- 调试工具：React DevTools

### 0.7 测试框架
- 单元测试：Jest + React Testing Library
- E2E测试：Playwright
- 测试覆盖率：Jest Coverage

### 0.8 部署和CI/CD
- 构建工具：Vite
- 容器化：Docker
- CI/CD：GitHub Actions
- 静态分析：SonarQube

## 1. 目录结构规范

```
components/
  └── pages/
      └── operators/  # 质检算子页面
          ├── [算子名]/                     # 1. 算子子组件目录: 存放所有子组件
          │   ├── [算子名]Concepts.tsx
          │   ├── [算子名]Config.tsx
          │   ├── [算子名]Tester.tsx
          │   ├── [算子名]Cases.tsx
          │   └── [算子名]Tips.tsx
          │
          ├── [算子名]Page.tsx              # 2. 算子主页面: 与子组件目录同级

# --- 示例 ---
          ├── keywordCheck/
          │   ├── KeywordCheckConcepts.tsx
          │   └── ...
          ├── KeywordCheckPage.tsx
```
**重要说明**:
- 每个算子的 **主页面组件 (`...Page.tsx`)** 必须直接放在 `components/pages/operators/` 目录下。
- 每个算子的 **所有子组件 (`...Concepts.tsx`, `...Config.tsx` 等)** 必须放在 `components/pages/operators/[算子名]/` 的独立子目录中。
- 主页面组件通过相对路径 (`./[算子名]/...`) 导入其子组件。
- **注意相对路径层级**：从子组件目录（如 `[算子名]/`）中引用上层公共组件（如 `components/common/`）时，需要使用 `../../../` 返回三级目录。

## 2. 组件开发规范

### 2.1 组件拆分原则
- 按功能模块拆分组件，每个组件职责单一
- 主要包含以下模块：
  - 核心概念模块（*Concepts）：提供算子的基本概念说明
  - 配置模块（*Config）：提供算子的配置界面
  - 实时测试模块（*Tester）：提供实时测试功能
  - 案例库模块（*Cases）：提供预设测试案例
  - 使用提示模块（*Tips）：提供使用说明和建议

### 2.2 组件命名规范
- 使用大驼峰命名法（PascalCase）
- 组件名称应当能清晰表达其功能
- 页面组件以Page结尾
- 子组件以功能命名，如Config、Tester等
- 组件顶部添加JSDoc注释说明功能

### 2.3 代码风格规范
- 使用TypeScript进行开发
- 使用函数式组件和Hooks
- 必须添加适当的类型定义
- 组件顶部添加JSDoc注释说明功能

### 2.4 导入/导出规范
- **统一使用命名导出**: 为了保证代码一致性和可维护性，所有组件、函数和类型都应使用 **命名导出** (`export const MyComponent` 或 `export type MyType`)。
- **禁止使用默认导出**: 项目中应避免使用 `export default`。
- **匹配导入方式**: 在导入时，必须使用花括号 `{}` 进行命名导入，如 `import { MyComponent } from '...'`。这可以有效避免 "does not provide an export named 'default'" 类的错误。

### 2.5 Props 规范
- **核对接口定义**：在向一个组件传递 `props` 之前，必须仔细阅读并核对该组件的 `Props` TypeScript 接口（`interface` 或 `type`）。
- **确保属性匹配**：确保传入的 `props` 对象与接口定义 **完全匹配**，包括属性的 **名称**、**类型** 和 **可选性**。
- **禁止未知属性**：不要传递接口中未定义的属性。对于必需的属性，确保其值已提供，避免出现因缺少属性（如 `badge` 属性）而导致的类型错误。

### 2.6 JSX 属性中的引号使用规范
- **问题场景**: 当一个使用双引号定义的JSX属性（如 `text="..."`），其内容本身也需要包含双引号时，会引发 `Expected ">" but found "..."` 此类的编译错误。
- **错误示例**: `<Tooltip text="这是一个"带引号"的文本">`
- **解决方案**:
    - **推荐**: 将字符串内部的引号改为中文全角引号 `" "`。
        - **正确示例**: `<Tooltip text="这是一个"带引号"的文本">`
    - **备选**: 将整个属性用花括号和模板字符串包裹。
        - **正确示例**: `<Tooltip text={'这是一个"带引号"的文本'}>`

## 3. 功能模块规范

### 3.1 主页面（*Page.tsx）
- 布局要求：
  - 页面标题区：包含标题、描述、标签
  - 左右布局：左侧60%，右侧40%
  - 背景色：bg-gray-50
- 状态管理：
  - 统一管理配置状态
  - 提供状态重置功能
- 组件组织：
  - 按照核心概念、配置演示、实时测试、案例库、使用提示的顺序排列
  - 各模块之间保持统一的间距（space-y-6）

### 3.2 核心概念模块（*Concepts.tsx）
- **布局规范**:
  - 使用白色背景（`bg-white`）
  - 圆角阴影边框（`rounded-lg shadow-sm border border-gray-200`）
  - 内边距统一（`p-6`）
- **内容结构**:
  - 模块标题固定为 "💡 核心概念"。
  - 内部应包含固定的子模块，并严格遵循以下顺序：
    1.  **`功能介绍`**: 对算子的核心功能进行简明扼要的说明。使用蓝色渐变背景卡片。
    2.  **`应用场景`**: 以列表形式展示算子的典型应用场景。使用绿色渐变背景卡片。
    3.  **`优势特点`**: 以列表形式突出算子的主要优点和特色。使用紫色渐变背景卡片。
    4.  **`检测原理`**: 用一小段文字解释算子背后的技术实现逻辑。使用橙色渐变背景卡片。
- **样式要求**:
  - 每个子模块都应作为一个独立的卡片，使用不同的渐变背景以作区分。
  - 每个子模块卡片应包含一个代表性的图标，以增强视觉识别度。

### 3.3 配置模块（*Config.tsx）
- 布局规范：
  - 使用白色背景（bg-white）
  - 圆角阴影边框（rounded-lg shadow-sm border border-gray-200）
  - 内边距统一（p-6）
- 配置项布局：
  - 标签对齐：使用固定宽度（w-20）
  - 输入控件：统一样式和尺寸
  - 提示信息：使用问号图标+悬浮提示
- 交互规范：
  - 实时更新：配置变更立即生效
  - 禁用状态：明确的视觉反馈
  - 联动逻辑：相关选项的启用/禁用

### 3.4 实时测试模块（*Tester.tsx）
- 布局要求：
  - 文本输入区：多行文本框
  - 操作按钮：执行测试、重置
  - 结果展示：清晰的视觉反馈
- 功能特性：
  - 默认加载示例文本
  - 实时字数统计
  - 清空和重置功能
- 测试结果展示：
  - 结果状态：成功/失败的明确标识
  - 详细信息：命中详情、统计数据
  - 结果分类：关键信息分类展示

### 3.5 案例库模块（*Cases.tsx）
- 布局规范：
  - 案例卡片：统一的卡片样式
  - 信息展示：名称、描述、预期结果
  - 操作按钮：加载案例
- 案例内容：
  - 典型场景覆盖
  - 清晰的预期结果
  - 完整的配置信息

### 3.6 使用提示模块（*Tips.tsx）
- 布局要求：
  - 分类展示：基础操作、高级功能、最佳实践
  - 渐变背景：区分不同类别
  - 图标装饰：增强视觉效果
- 内容组织：
  - 简明扼要
  - 重点突出
  - 实用性强

### 3.7 学习建议模块
- 位置：页面底部，独立区块
- 样式：
  - 使用蓝色渐变背景（bg-blue-50）
  - 圆角边框（rounded-lg）
  - 蓝色边框（border-blue-200）
  - 内边距统一为 p-6
- 内容结构：
  - 标题：使用"💡 学习建议"作为固定标题
  - 字体：标题使用 text-lg + font-semibold
  - 颜色：标题使用 text-blue-900，内容使用 text-blue-800
  - 内容格式：使用无序列表，每项前添加"•"符号
  - 建议分类：
    1. 基础入门建议
    2. 功能掌握建议
    3. 实际应用建议
    4. 进阶优化建议

## 4. UI设计规范

### 4.1 颜色规范
- 主色调：
  - 蓝色系：blue-600（主要按钮）
  - 灰色系：gray-50（背景）、gray-200（边框）
- 状态颜色：
  - 成功：green-600
  - 错误：red-600
  - 警告：yellow-600
  - 信息：blue-600

### 4.2 字体规范
- 标题：
  - 主标题：text-2xl + font-bold
  - 副标题：text-xl + font-semibold
  - 小标题：text-lg + font-semibold
- 正文：
  - 默认：text-sm
  - 说明文字：text-xs + text-gray-500

### 4.3 间距规范
- 组件间距：space-y-6
- 内部间距：space-y-4
- 边距：
  - 页面：px-6 py-6
  - 卡片：p-6
  - 表单项：space-x-4

### 4.4 交互规范
- 按钮：
  - 主要按钮：蓝色背景+白色文字
  - 次要按钮：灰色边框+深色文字
  - 禁用状态：降低透明度
- 输入控件：
  - 统一的聚焦效果
  - 明确的禁用状态
  - 错误状态反馈

## 5. 性能优化规范

### 5.1 代码优化
- 使用React.memo()优化渲染
- 合理使用useMemo和useCallback
- 避免不必要的重渲染

### 5.2 加载优化
- 组件按需加载
- 资源懒加载
- 合理的缓存策略

## 6. 测试规范

### 6.1 测试用例
- 覆盖主要功能点
- 包含边界条件
- 异常情况处理

### 6.2 测试方法
- 单元测试
- 集成测试
- 端到端测试

## 7. 文档规范

### 7.1 代码注释
- 组件顶部必须添加JSDoc注释
- 关键逻辑必须添加注释
- 复杂算法需要详细注释

### 7.2 文档要求
- README.md文件必须包含：
  - 项目说明
  - 安装步骤
  - 使用说明
  - 注意事项

## 8. 开发流程规范

### 8.1 开发步骤
1. 需求分析
2. 组件设计
3. 功能实现
4. 测试验证
5. 代码提交

### 8.2 代码提交
- 清晰的提交信息
- 合理的分支管理
- 代码审查流程

## 9. 维护规范

### 9.1 代码维护
- 定期代码重构
- 及时修复问题
- 性能监控和优化

### 9.2 文档维护
- 及时更新文档
- 版本变更记录
- 问题修复记录

## 10. 注意事项

### 10.1 通用注意事项
- 保持代码简洁清晰
- 遵循TypeScript类型定义
- 注意性能优化
- 保持组件复用性

### 10.2 特殊注意事项
- 配置变更时注意联动效果
- 测试数据的真实性
- 错误处理的完整性
- 用户体验的一致性
