import React from 'react';
import { Outlet } from 'react-router-dom';
import Navigation from './Navigation';
import Sidebar from './Sidebar';

/**
 * 主布局组件
 * 提供顶部导航栏、侧边栏和主要内容区域
 */
const MainLayout: React.FC = () => {
  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* 顶部导航栏，固定 */}
      <Navigation />
      
      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏，固定 */}
        <Sidebar />
        
        {/* 主要内容区域，带滚动条 */}
        <main id="main-content" className="flex-1 overflow-y-auto">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MainLayout; 