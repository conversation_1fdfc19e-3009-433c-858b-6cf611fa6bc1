---
### **1. 质检任务管理**

**概念说明:** 一次具体的质检执行实例。它定义了要对一批**固定范围**的通话录音，在**指定时间**（立即或定时执行一次）、使用"哪个质检方案"来进行检查。任务是质检工作的最终执行单元，它既可以由管理员为满足特定、临时的质检需求而手动创建，也可以由一个"质检计划"在预定时间自动生成。

---

### **1.1 质检任务管理 - 任务列表页**

**页面目标:** 让管理者能够清晰地看到所有一次性任务的执行情况和结果，并进行操作。

**页面布局:** 经典的"筛选区 + 列表区"布局。

#### **页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 质检任务`
*   **页面标题:** `质检任务管理`
*   **主要操作按钮:**
    *   `+ 新建任务` (蓝色主按钮)

#### **筛选与搜索区**

*   **任务名称:** `[输入框]`
*   **任务状态:** `[下拉选择框]` (全部, 待执行, 执行中, 已完成, 执行失败)
*   **创建人:** `[人员搜索框]`
*   **创建时间:** `[日期范围选择器]`
*   **来源:** `[下拉选择框]` (全部, 手动创建, 由计划生成)

#### **任务列表区**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **任务名称** | 显示任务名称。如果是由计划生成的，可以显示为 `[计划名称] - 20231120期`。|
| **任务状态** | 用标签显示：`待执行` (灰色), `执行中` (蓝色), `已完成` (绿色), `执行失败` (红色)。|
| **任务进度** | `执行中`状态下显示进度条 `[||||--] 60%` 和数字 `600 / 1000`。|
| **质检范围（摘要）** | 简洁展示核心范围信息，如 `坐席组: A组 | 通话时间: 2023.11.01-11.10`。|
| **质检方案** | 显示选用的方案名称。|
| **创建信息** | `创建人` 和 `创建时间`。|
| **操作** | - `查看详情`: 跳转到任务详情页(Dashboard)。<br>- `复制`: 跳转到新建任务页并填充配置。<br>- `状态为"待执行"`: 可 `编辑`、`删除`。<br>- `状态为"已完成/失败"`: 可 `删除`。 |

---

### **1.2 质检任务管理 - 新建任务页**

**页面目标:** 提供一个极致简化的界面，用于快速创建一次性的质检任务。

#### **页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 质检任务 / 新建任务`
*   **页面标题:** `新建一次性质检任务`

#### **表单区域**

**第一部分：基本信息**

*   **任务名称:** `[输入框]`，必填。
*   **任务描述:** `[文本域 Textarea]`，选填。

**第二部分：质检范围**

*   **数据来源:** `[单选按钮组]`，必填。
    *   `◎ 原始通话数据`
    *   `◎ 二次质检 (既有质检结果)`
    *   `◎ 手动上传数据文件`

*   **核心区别:** 在这里，所有与时间相关的筛选，都**必须是固定的、绝对的时间范围选择器 `[日期范围选择器]`**，不再有"动态范围"的选项。所有配置都与"质检计划"中的配置一一对应，只是去除了周期性和动态时间的部分。

**第三部分：质检规则与模式**

*   **质检方案:** `[下拉选择框]`，必填。
*   **质检模式:** `[单选按钮组]`，必填。(全量质检 / 抽样质检)

**第四部分：执行计划**

*   **执行方式:** `[单选按钮组]`，必填。
    *   `◎ 立即执行`
    *   `◎ 定时执行` -> `[日期时间选择器]`
*   **这里没有"周期性任务"的开关。**

#### **页面底部操作区**

*   `创建任务` (蓝色主按钮)
*   `取消` 