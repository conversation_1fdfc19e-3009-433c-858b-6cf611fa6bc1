import React from 'react';

/**
 * 录音时长检测提示组件
 * @constructor
 */
const DurationCheckTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">使用提示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>
      <div className="space-y-4">
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <h4 className="font-semibold text-gray-800 mb-2">数据格式</h4>
          <p className="text-xs text-gray-600">
            测试文本需要包含 `[mm:ss]` 格式的时间戳。系统会自动寻找最后一个时间戳作为对话的总时长。
          </p>
        </div>
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <h4 className="font-semibold text-gray-800 mb-2">配置技巧</h4>
          <p className="text-xs text-gray-600">
            单位为"秒"。如果您想检测是否超过15分钟，应输入 `900` 秒。
          </p>
        </div>
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <h4 className="font-semibold text-gray-800 mb-2">业务场景建议</h4>
          <p className="text-xs text-gray-600">
            此规则通常用作一个独立的质检项，或者作为其他复杂规则（如"SOP流程合规"）的前置过滤条件，先筛掉明显不合规的短通话。
          </p>
        </div>
      </div>
    </div>
  );
};

export default DurationCheckTips; 