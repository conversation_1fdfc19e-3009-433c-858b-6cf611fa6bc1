import React, { useState, useCallback } from 'react';
import { BasicInfoSection } from '../BasicInfoSection';
import { ConditionConfigSection } from '../ConditionConfigSection';
import { ScoringConfigSection } from '../ScoringConfigSection';
import { FooterActions } from '../FooterActions';
import { RuleExplanation } from '../RuleExplanation';
import { RuleVisualization } from '../RuleVisualization';
import { RuleTester } from '../RuleTester';
import type { Condition, BasicInfo, ScoringConfig } from '../../types';
import { ConditionType, DetectionRole, PrerequisiteCondition, DetectionScope, AnalysisMethod, KeywordDetectionType, ScoringMode, HitScoringTarget } from '../../types';

// 初始化基本信息
const initialBasicInfo: BasicInfo = {
  ruleName: '【规律】业务规则示例',
  ruleCategory: '合规流程规则',
  remarks: '规则目的：检测是否涉及公司业务流程执行（检测上下文逻辑关系）；使用的条件类型：包含某某关键词语或符合某个特定述述；测试文本示例：客户提出关于公司特定流程， 一种办证信用卡，一种选办证，文本是否对话用的正逻辑分析，由于条件比系统中，所以要扩规则。',
  importance: '轻度违规',
  effectiveTime: '全年期生效',
  ruleType: '服务规范',
  applicableBusiness: '',
  manualReview: '',
};

// 初始化条件配置
const initialConditions: Condition[] = [
  {
    id: 'a',
    name: '正则表达式检查①',
    type: ConditionType.REGEX,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.NONE,
    hitOrder: 1,
    detectionScope: DetectionScope.ENTIRE_TEXT,
    rangeStart: 1,
    rangeEnd: 999,
    effectiveInSingleSentence: false,
    hitPattern: '.*(拥有|持有|办过).*(卡|信用卡).*',
    excludePattern: '',
    testInput: '',
    isCollapsed: false,
  },
  {
    id: 'b',
    name: '关键词检查②',
    type: ConditionType.KEYWORD,
    detectionRole: DetectionRole.CUSTOMER,
    prerequisite: PrerequisiteCondition.A,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 2,
    effectiveInSingleSentence: false,
    keywords: ['办过'],
    currentKeywordInput: '',
    analysisMethod: AnalysisMethod.SINGLE_SENTENCE,
    keywordDetectionType: KeywordDetectionType.CONTAINS_ANY,
    isCollapsed: false,
  },
  {
    id: 'c',
    name: '关键词检查③',
    type: ConditionType.KEYWORD,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.B,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 2,
    effectiveInSingleSentence: false,
    keywords: ['发卡行', '额度'],
    currentKeywordInput: '',
    analysisMethod: AnalysisMethod.SINGLE_SENTENCE,
    keywordDetectionType: KeywordDetectionType.CONTAINS_ANY,
    isCollapsed: false,
  },
  {
    id: 'd',
    name: '关键词检查④',
    type: ConditionType.KEYWORD,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.A,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 20,
    effectiveInSingleSentence: false,
    keywords: ['工作单位', '公司名称', '公司全称'],
    currentKeywordInput: '',
    analysisMethod: AnalysisMethod.SINGLE_SENTENCE,
    keywordDetectionType: KeywordDetectionType.CONTAINS_ANY,
    isCollapsed: false,
  },
  {
    id: 'e',
    name: '关键词检查⑤',
    type: ConditionType.KEYWORD,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.D,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 20,
    effectiveInSingleSentence: false,
    keywords: ['年收入', '税前'],
    currentKeywordInput: '',
    analysisMethod: AnalysisMethod.SINGLE_SENTENCE,
    keywordDetectionType: KeywordDetectionType.CONTAINS_ANY,
    isCollapsed: false,
  },
  {
    id: 'f',
    name: '关键词检查⑥',
    type: ConditionType.KEYWORD,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.D,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 20,
    effectiveInSingleSentence: false,
    keywords: ['年收入', '税前'],
    currentKeywordInput: '',
    analysisMethod: AnalysisMethod.SINGLE_SENTENCE,
    keywordDetectionType: KeywordDetectionType.CONTAINS_ALL,
    isCollapsed: false,
  },
  {
    id: 'g',
    name: '正则表达式检查⑦',
    type: ConditionType.REGEX,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.D,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 20,
    effectiveInSingleSentence: false,
    hitPattern: '.*名下.*(房|车).*',
    excludePattern: '',
    testInput: '',
    isCollapsed: false,
  },
  {
    id: 'h',
    name: '关键词检查⑧',
    type: ConditionType.KEYWORD,
    detectionRole: DetectionRole.CUSTOMER_SERVICE,
    prerequisite: PrerequisiteCondition.D,
    hitOrder: 1,
    detectionScope: DetectionScope.AROUND_HIT,
    rangeStart: 1,
    rangeEnd: 20,
    effectiveInSingleSentence: false,
    keywords: ['真实', '准确'],
    currentKeywordInput: '',
    analysisMethod: AnalysisMethod.SINGLE_SENTENCE,
    keywordDetectionType: KeywordDetectionType.CONTAINS_ALL,
    isCollapsed: false,
  },
];

// 初始化评分配置
const initialScoringConfig: ScoringConfig = {
  ruleScoringEnabled: true,
  scoringMode: ScoringMode.BY_HIT,
  hitScoringTarget: HitScoringTarget.AGENT_SCORE,
  scoreValue: -10,
};

/**
 * 复杂示例页面组件
 * 展示完整的业务规则配置案例
 */
const ComplexExamplePage: React.FC = () => {
  const [basicInfo, setBasicInfo] = useState<BasicInfo>(initialBasicInfo);
  const [detectionLogic, setDetectionLogic] = useState<string>('(a&&b&&!c)||(a&&d&&!(e&&f)&&g&&h)');
  const [conditions, setConditions] = useState<Condition[]>(initialConditions);
  const [scoringConfig, setScoringConfig] = useState<ScoringConfig>(initialScoringConfig);

  const handleBasicInfoChange = useCallback((field: keyof BasicInfo, value: string) => {
    setBasicInfo(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleDetectionLogicChange = useCallback((value: string) => {
    setDetectionLogic(value);
  }, []);

  const handleConditionChange = useCallback(<K extends keyof Condition>(index: number, field: K, value: Condition[K]) => {
    setConditions(prev => {
      const newConditions = [...prev];
      newConditions[index] = { ...newConditions[index], [field]: value };
      return newConditions;
    });
  }, []);
  
  const handleKeywordAdd = useCallback((conditionIndex: number, keyword: string) => {
    if (!keyword.trim()) return;
    setConditions(prev => {
        const newConditions = [...prev];
        const condition = newConditions[conditionIndex];
        if (condition.type === ConditionType.KEYWORD) {
            const updatedKeywords = [...(condition.keywords || []), keyword.trim()];
            newConditions[conditionIndex] = { ...condition, keywords: updatedKeywords, currentKeywordInput: '' };
        }
        return newConditions;
    });
  }, []);

  const handleKeywordRemove = useCallback((conditionIndex: number, keywordToRemove: string) => {
    setConditions(prev => {
        const newConditions = [...prev];
        const condition = newConditions[conditionIndex];
        if (condition.type === ConditionType.KEYWORD && condition.keywords) {
            const updatedKeywords = condition.keywords.filter(keyword => keyword !== keywordToRemove);
            newConditions[conditionIndex] = { ...condition, keywords: updatedKeywords };
        }
        return newConditions;
    });
  }, []);

  const handleRemoveCondition = useCallback((indexToRemove: number) => {
    setConditions(prev => prev.filter((_, index) => index !== indexToRemove));
  }, []);

  const handleToggleCollapse = useCallback((indexToToggle: number) => {
    setConditions(prev => {
      const newConditions = [...prev];
      const condition = newConditions[indexToToggle];
      newConditions[indexToToggle] = { ...condition, isCollapsed: !condition.isCollapsed };
      return newConditions;
    });
  }, []);

  const handleScoringConfigChange = useCallback(<K extends keyof ScoringConfig>(field: K, value: ScoringConfig[K]) => {
    setScoringConfig(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleConfirm = useCallback(() => {
    console.log('确认配置');
  }, []);

  const handleSaveAndTest = useCallback(() => {
    console.log('保存并测试');
  }, []);

  const handleCancel = useCallback(() => {
    console.log('取消');
  }, []);

  const handleBasicInfoEdit = useCallback(() => {
    console.log('编辑基本信息');
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                逻辑规则说明
              </h1>
              <p className="text-gray-600 mt-1">
                完整的业务规则配置案例，展示8个条件的复杂逻辑关系
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                ✨ 推荐学习
              </span>
              <span className="text-sm text-gray-500">
                案例复杂度：高级
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：58%宽度 */}
          <div className="w-[58%] space-y-6">
            {/* 基本信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <BasicInfoSection 
                info={basicInfo}
                onChange={handleBasicInfoChange}
                onEdit={handleBasicInfoEdit}
              />
            </div>

            {/* 条件配置 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 divide-y divide-gray-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold leading-6 text-gray-900">条件组合逻辑</h3>
                <p className="mt-1 text-sm text-gray-600">
                  使用条件ID (a, b, c...) 和逻辑运算符 (&&, ||, !) 来定义规则的触发逻辑。
                </p>
                <input
                  type="text"
                  value={detectionLogic}
                  onChange={(e) => handleDetectionLogicChange(e.target.value)}
                  className="mt-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="例如: (a && b) || c"
                />
              </div>
              <div className="p-6">
                 <h3 className="text-lg font-semibold leading-6 text-gray-900 mb-4">条件详情</h3>
                <ConditionConfigSection
                  conditions={conditions}
                  onConditionChange={handleConditionChange}
                  onAddKeyword={handleKeywordAdd}
                  onRemoveKeyword={handleKeywordRemove}
                  onRemoveCondition={handleRemoveCondition}
                  onToggleCollapse={handleToggleCollapse}
                />
              </div>
            </div>

            {/* 评分配置 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <ScoringConfigSection 
                config={scoringConfig}
                onChange={handleScoringConfigChange}
              />
            </div>

            {/* 底部操作按钮 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <FooterActions 
                onConfirm={handleConfirm}
                onSaveAndTest={handleSaveAndTest}
                onCancel={handleCancel}
              />
            </div>
          </div>

          {/* 右侧：42%宽度 */}
          <div className="w-[42%] space-y-6">
            {/* 规则解释 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <RuleExplanation />
            </div>

            {/* 可视化展示 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <RuleVisualization 
                conditions={conditions}
                detectionLogic={detectionLogic}
              />
            </div>

            {/* 规则测试 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <RuleTester 
                conditions={conditions}
                detectionLogic={detectionLogic}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 学习提示 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>理解逻辑关系</strong>：重点关注条件间的前置关系和逻辑表达式</p>
                <p>• <strong>测试验证</strong>：使用右侧的测试功能验证规则配置的正确性</p>
                <p>• <strong>可视化理解</strong>：通过依赖关系图和流程图理解规则执行流程</p>
                <p>• <strong>实际应用</strong>：将学到的配置方法应用到实际业务场景中</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplexExamplePage; 