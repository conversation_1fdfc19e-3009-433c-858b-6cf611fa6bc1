import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Send,
  Mail,
  Webhook,
  MessageSquare as MessageSquareIcon,
  Save,
  TestTube,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  Settings,
  Globe,
  Shield
} from 'lucide-react';
import { Card, Form, Input, Select, Button, Switch, InputNumber, message, Table, Modal, Tooltip, Tag, Radio, Badge, Drawer } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import UnifiedPageHeader from './components/UnifiedPageHeader';

const { Option } = Select;

/**
 * SMTP配置接口
 */
interface SmtpConfig {
  serverAddress: string;
  port: number;
  encryption: 'ssl' | 'tls';
  senderEmail: string;
  password?: string;
}

/**
 * Webhook配置接口
 */
interface WebhookConfig {
  id: string;
  name: string;
  url: string;
  method: 'POST' | 'GET';
  headers?: Record<string, string>;
  bodyTemplate?: string;
  enabled: boolean;
}

/**
 * 系统弹窗配置接口
 */
interface PopupConfig {
  enabled: boolean;
  autoCloseSeconds: number; // 0 for no auto-close
  sound: string; // URL or name of sound
}

/**
 * 通知渠道管理页面组件
 */
const NotificationChannelManagementPage: React.FC = () => {
  const [smtpForm] = Form.useForm();
  const [popupForm] = Form.useForm();
  const [webhookForm] = Form.useForm();

  const [smtpStatus, setSmtpStatus] = useState<'connected' | 'error' | 'unconfigured'>('unconfigured');
  const [webhooks, setWebhooks] = useState<WebhookConfig[]>([
    {
      id: '1',
      name: '钉钉-质检主管群机器人',
      url: 'https://oapi.dingtalk.com/robot/send?access_token=xxxxx',
      method: 'POST',
      enabled: true,
      bodyTemplate: `{"msgtype": "text", "text": {"content": "【质检预警】\\n等级: {{alert.level}}\\n规则: {{rule.name}}\\n坐席: {{agent.name}}"}}`
    },
    {
      id: '2',
      name: '企业微信-服务告警',
      url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxx',
      method: 'POST',
      enabled: false,
      bodyTemplate: `{"msgtype": "markdown", "markdown": {"content": ">**实时预警**\\n>级别：<font color=\\"warning\\">{{alert.level}}</font>\\n>触发规则：{{rule.name}}\\n>坐席：{{agent.name}}"}}`
    },
  ]);
  const [isWebhookModalVisible, setIsWebhookModalVisible] = useState(false);
  const [editingWebhook, setEditingWebhook] = useState<WebhookConfig | null>(null);

  // SMTP表单处理
  const handleSaveSmtp = (values: SmtpConfig) => {
    console.log('Saving SMTP config:', values);
    message.success('邮件服务器配置已保存');
  };

  const handleTestSmtp = () => {
    smtpForm.validateFields().then(values => {
      console.log('Testing SMTP with:', values);
      message.loading({ content: '正在发送测试邮件...', key: 'smtp_test' });
      setTimeout(() => {
        // Simulate test result
        const success = Math.random() > 0.3;
        if (success) {
          setSmtpStatus('connected');
          message.success({ content: '测试邮件发送成功！', key: 'smtp_test' });
        } else {
          setSmtpStatus('error');
          message.error({ content: '测试邮件发送失败，请检查配置。', key: 'smtp_test' });
        }
      }, 2000);
    });
  };

  // 弹窗配置处理
  const handleSavePopup = (values: PopupConfig) => {
    console.log('Saving Popup config:', values);
    message.success('系统弹窗配置已保存');
  };

  // Webhook 处理
  const handleShowWebhookModal = (webhook?: WebhookConfig) => {
    if (webhook) {
      setEditingWebhook(webhook);
      webhookForm.setFieldsValue(webhook);
    } else {
      setEditingWebhook(null);
      webhookForm.resetFields();
      webhookForm.setFieldsValue({ method: 'POST', enabled: true });
    }
    setIsWebhookModalVisible(true);
  };

  const handleSaveWebhook = () => {
    webhookForm.validateFields().then(values => {
      if (editingWebhook) {
        setWebhooks(webhooks.map(wh => wh.id === editingWebhook.id ? { ...wh, ...values } : wh));
        message.success('Webhook 更新成功');
      } else {
        const newWebhook = { id: Date.now().toString(), ...values };
        setWebhooks([...webhooks, newWebhook]);
        message.success('Webhook 创建成功');
      }
      setIsWebhookModalVisible(false);
    });
  };

  const handleDeleteWebhook = (id: string) => {
    setWebhooks(webhooks.filter(wh => wh.id !== id));
    message.success('Webhook 删除成功');
  };

  const handleTestWebhook = (webhook: WebhookConfig) => {
    message.loading({ content: `正在向 ${webhook.name} 发送测试推送...`, key: 'webhook_test' });
    setTimeout(() => {
        message.success({ content: '测试推送发送成功！', key: 'webhook_test' });
    }, 2000);
  };

  const webhookColumns: ColumnsType<WebhookConfig> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <div className="flex items-center gap-3">
          <div className={`w-2 h-2 rounded-full ${record.enabled ? 'bg-green-500' : 'bg-gray-400'}`} />
          <span className="font-medium text-gray-900">{name}</span>
        </div>
      ),
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      render: (url: string) => (
        <div className="flex items-center gap-2">
          <Globe className="w-4 h-4 text-gray-400" />
          <span className="text-gray-600 font-mono text-xs bg-gray-50 px-2 py-1 rounded border">
            {url.length > 40 ? `${url.substring(0, 40)}...` : url}
          </span>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean) => (
        <Badge 
          status={enabled ? 'success' : 'default'} 
          text={
            <span className={`font-medium ${enabled ? 'text-green-600' : 'text-gray-500'}`}>
              {enabled ? '已启用' : '已禁用'}
            </span>
          }
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <div className="flex items-center gap-1">
            <Tooltip title="编辑">
                <Button 
                  size="small"
                  icon={<Edit className="w-4 h-4" />} 
                  onClick={() => handleShowWebhookModal(record)}
                  className="hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-all duration-200"
                />
            </Tooltip>
            <Tooltip title="测试推送">
                <Button 
                  size="small"
                  icon={<TestTube className="w-4 h-4" />} 
                  onClick={() => handleTestWebhook(record)}
                  className="hover:bg-green-50 hover:border-green-300 hover:text-green-600 transition-all duration-200"
                />
            </Tooltip>
            <Tooltip title="删除">
                <Button 
                  size="small"
                  icon={<Trash2 className="w-4 h-4" />} 
                  danger 
                  onClick={() => handleDeleteWebhook(record.id)}
                  className="hover:bg-red-50 transition-all duration-200"
                />
            </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/20">
      <UnifiedPageHeader
        title="通知渠道管理"
        subtitle="配置系统用于发送邮件、Webhook等通知的外部服务"
        icon={Send}
      />
      
      {/* 统计卡片区域 */}
      <div className="px-6 md:px-10 pt-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }} 
            animate={{ opacity: 1, y: 0 }} 
            transition={{ duration: 0.5 }}
            className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">邮件服务状态</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {smtpStatus === 'connected' ? '已连接' : smtpStatus === 'error' ? '连接异常' : '未配置'}
                </p>
              </div>
              <div className={`p-3 rounded-full ${
                smtpStatus === 'connected' ? 'bg-green-100' : 
                smtpStatus === 'error' ? 'bg-red-100' : 'bg-gray-100'
              }`}>
                <Mail className={`w-6 h-6 ${
                  smtpStatus === 'connected' ? 'text-green-600' : 
                  smtpStatus === 'error' ? 'text-red-600' : 'text-gray-600'
                }`} />
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }} 
            animate={{ opacity: 1, y: 0 }} 
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Webhook 端点</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{webhooks.length}</p>
              </div>
              <div className="p-3 rounded-full bg-purple-100">
                <Webhook className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }} 
            animate={{ opacity: 1, y: 0 }} 
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">活跃端点</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{webhooks.filter(w => w.enabled).length}</p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      <main className="px-6 md:px-10 pb-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* SMTP 配置卡片 */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }} 
            animate={{ opacity: 1, x: 0 }} 
            transition={{ duration: 0.6 }}
          >
            <Card
              className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden"
              title={
                <div className="flex items-center gap-3 py-2">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Mail className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <span className="text-lg font-semibold text-gray-900">邮件服务器配置</span>
                    <p className="text-sm text-gray-500 mt-0.5">SMTP 服务配置</p>
                  </div>
                </div>
              }
              extra={
                <div className="flex items-center gap-2">
                  {smtpStatus === 'connected' && (
                    <div className="flex items-center gap-2 bg-green-50 px-3 py-1 rounded-full">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-700">连接正常</span>
                    </div>
                  )}
                  {smtpStatus === 'error' && (
                    <div className="flex items-center gap-2 bg-red-50 px-3 py-1 rounded-full">
                      <XCircle className="w-4 h-4 text-red-600" />
                      <span className="text-sm font-medium text-red-700">连接失败</span>
                    </div>
                  )}
                  {smtpStatus === 'unconfigured' && (
                    <div className="flex items-center gap-2 bg-gray-50 px-3 py-1 rounded-full">
                      <AlertCircle className="w-4 h-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">未配置</span>
                    </div>
                  )}
                </div>
              }
            >
              <Form form={smtpForm} layout="vertical" onFinish={handleSaveSmtp} className="space-y-4">
                <Form.Item name="serverAddress" label="SMTP服务器地址" rules={[{ required: true }]}>
                  <Input 
                    placeholder="smtp.example.com" 
                    className="rounded-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </Form.Item>
                <div className="grid grid-cols-2 gap-4">
                  <Form.Item name="port" label="端口" rules={[{ required: true }]}>
                    <InputNumber 
                      placeholder="465" 
                      className="w-full rounded-lg"
                      controls={false}
                    />
                  </Form.Item>
                  <Form.Item name="encryption" label="加密方式" rules={[{ required: true }]}>
                    <Select className="rounded-lg">
                      <Option value="ssl">SSL</Option>
                      <Option value="tls">TLS</Option>
                    </Select>
                  </Form.Item>
                </div>
                <Form.Item name="senderEmail" label="发件人邮箱地址" rules={[{ required: true, type: 'email' }]}>
                  <Input 
                    placeholder="<EMAIL>" 
                    className="rounded-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </Form.Item>
                <Form.Item name="password" label="发件人密码/授权码">
                  <Input.Password className="rounded-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500" />
                </Form.Item>
                <div className="flex justify-end gap-3 pt-4 border-t border-gray-100">
                  <Button 
                    icon={<TestTube className="w-4 h-4" />} 
                    onClick={handleTestSmtp}
                    className="rounded-lg border-gray-200 hover:border-blue-300 hover:text-blue-600 transition-all duration-200"
                  >
                    发送测试邮件
                  </Button>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    icon={<Save className="w-4 h-4" />}
                    className="rounded-lg bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 shadow-lg hover:shadow-blue-200"
                  >
                    保存配置
                  </Button>
                </div>
              </Form>
            </Card>
          </motion.div>
          
          {/* 系统弹窗配置卡片 */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }} 
            animate={{ opacity: 1, x: 0 }} 
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card
              className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden"
              title={
                <div className="flex items-center gap-3 py-2">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <MessageSquareIcon className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <span className="text-lg font-semibold text-gray-900">系统弹窗配置</span>
                    <p className="text-sm text-gray-500 mt-0.5">浏览器通知设置</p>
                  </div>
                </div>
              }
            >
              <Form 
                form={popupForm} 
                layout="vertical" 
                onFinish={handleSavePopup} 
                initialValues={{ enabled: true, autoCloseSeconds: 10, sound: 'default' }}
                className="space-y-4"
              >
                <Form.Item name="enabled" label="启用系统弹窗通知" valuePropName="checked">
                  <div className="flex items-center gap-3">
                    <Switch className="bg-gray-200" />
                    <span className="text-sm text-gray-600">开启后将显示浏览器通知</span>
                  </div>
                </Form.Item>
                <Form.Item label="自动关闭时间">
                  <div className="flex items-center gap-3">
                    <Form.Item name="autoCloseSeconds" noStyle>
                      <InputNumber 
                        min={0} 
                        className="w-24 rounded-lg"
                        controls={false}
                      />
                    </Form.Item>
                    <span className="text-sm text-gray-600">秒 (0为不自动关闭)</span>
                  </div>
                </Form.Item>
                <Form.Item name="sound" label="声音提示">
                  <Select className="rounded-lg">
                    <Option value="default">
                      <div className="flex items-center gap-2">
                        <Settings className="w-4 h-4" />
                        默认提示音
                      </div>
                    </Option>
                    <Option value="ding">
                      <div className="flex items-center gap-2">
                        <MessageSquareIcon className="w-4 h-4" />
                        钉钉
                      </div>
                    </Option>
                    <Option value="sonar">
                      <div className="flex items-center gap-2">
                        <Shield className="w-4 h-4" />
                        声呐
                      </div>
                    </Option>
                    <Option value="none">无声音</Option>
                  </Select>
                </Form.Item>
                <div className="flex justify-end pt-4 border-t border-gray-100">
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    icon={<Save className="w-4 h-4" />}
                    className="rounded-lg bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 shadow-lg hover:shadow-green-200"
                  >
                    保存配置
                  </Button>
                </div>
              </Form>
            </Card>
          </motion.div>

          {/* Webhook 管理卡片 */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }} 
            animate={{ opacity: 1, x: 0 }} 
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card
              className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 rounded-xl overflow-hidden"
              title={
                <div className="flex items-center gap-3 py-2">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Webhook className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <span className="text-lg font-semibold text-gray-900">Webhook 端点管理</span>
                    <p className="text-sm text-gray-500 mt-0.5">外部系统集成</p>
                  </div>
                </div>
              }
              extra={
                <Button 
                  type="primary" 
                  icon={<Plus className="w-4 h-4" />} 
                  onClick={() => handleShowWebhookModal()}
                  className="rounded-lg bg-purple-600 hover:bg-purple-700 border-purple-600 hover:border-purple-700 shadow-lg hover:shadow-purple-200"
                >
                  新建 Webhook
                </Button>
              }
            >
              <div className="space-y-4">
                <Table 
                  columns={webhookColumns} 
                  dataSource={webhooks} 
                  rowKey="id" 
                  pagination={false}
                  className="rounded-lg overflow-hidden"
                  rowClassName="hover:bg-gray-50 transition-colors duration-200"
                />
                {webhooks.length === 0 && (
                  <div className="text-center py-8">
                    <Webhook className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">暂无 Webhook 配置</p>
                    <p className="text-sm text-gray-400">点击上方按钮创建第一个 Webhook</p>
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        </div>
      </main>

      {/* Webhook Modal */}
      <Drawer
        title={
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Webhook className="w-5 h-5 text-purple-600" />
            </div>
            <span className="text-lg font-semibold">
              {editingWebhook ? '编辑 Webhook' : '新建 Webhook'}
            </span>
          </div>
        }
        open={isWebhookModalVisible}
        onClose={() => setIsWebhookModalVisible(false)}
        footer={[
          <div className="flex justify-end gap-2 w-full">
            <Button 
              key="back" 
              onClick={() => setIsWebhookModalVisible(false)}
              className="rounded-lg"
            >
              取消
            </Button>,
            <Button 
              key="submit" 
              type="primary" 
              onClick={handleSaveWebhook}
              className="rounded-lg bg-purple-600 hover:bg-purple-700 border-purple-600 hover:border-purple-700"
            >
              保存
            </Button>,
          </div>
        ]}
        width={700}
        className="rounded-xl overflow-hidden"
      >
        <Form form={webhookForm} layout="vertical" className="space-y-4 mt-6">
          <Form.Item name="name" label="名称" rules={[{ required: true, message: '请输入Webhook的别名' }]}>
            <Input 
              placeholder="如：钉钉-质检主管群机器人" 
              className="rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500"
            />
          </Form.Item>
          <Form.Item name="url" label="URL" rules={[{ required: true, message: '请输入Webhook的URL地址' }, { type: 'url', message: '请输入有效的URL地址'}]}>
            <Input 
              placeholder="https://oapi.dingtalk.com/robot/send?access_token=..." 
              className="rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500"
            />
          </Form.Item>
          <div className="grid grid-cols-2 gap-4">
            <Form.Item name="method" label="请求方法" rules={[{ required: true }]}>
              <Radio.Group className="flex gap-4">
                <Radio value="POST" className="flex-1">POST</Radio>
                <Radio value="GET" className="flex-1">GET</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="enabled" label="状态" valuePropName="checked">
              <div className="flex items-center gap-3">
                <Switch 
                  checkedChildren="启用" 
                  unCheckedChildren="禁用" 
                  className="bg-gray-200"
                />
              </div>
            </Form.Item>
          </div>
          <Form.Item name="headers" label="请求头 (Headers)">
            <Input.TextArea 
              rows={3} 
              placeholder={`{\n  "Content-Type": "application/json"\n}`}
              className="rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 font-mono text-sm"
            />
          </Form.Item>
          <Form.Item 
            label={
              <div className="flex items-center gap-2">
                请求体模板 (Body Template)
                <Tooltip title={
                  <div>
                    <p>使用 `{'{{variable}}'}` 语法插入动态变量。</p>
                    <p>可用变量: `alert.level`, `rule.name`, `agent.name`, `agent.id`, `customer.number`, `context.text` 等。</p>
                  </div>
                }>
                  <Info className="w-4 h-4 text-gray-400 hover:text-gray-600 transition-colors" />
                </Tooltip>
              </div>
            }
            name="bodyTemplate"
          >
            <Input.TextArea 
              rows={6} 
              placeholder={`{\n  "msgtype": "text",\n  "text": {\n    "content": "【质检预警】来自 {{agent.name}} 的通话触发了规则 {{rule.name}}"\n  }\n}`}
              className="rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 font-mono text-sm"
            />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default NotificationChannelManagementPage; 