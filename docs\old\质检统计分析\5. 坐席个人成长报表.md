### **5. 坐席个人成长报表 - 详细设计**

---

**版本:** 1.0
**设计者:** AI Senior Engineer
**日期:** 2024-06-30

---

#### **一、 核心目的与面向用户**

*   **面向用户:** 坐席、班组长。
*   **核心目的:** 帮助坐席清晰、直观地了解个人在服务质量、业务能力和合规性方面的表现。通过与团队及中心平均水平的对比，定位个人优势与短板，并结合具体的失分案例和学习资源，为个人能力提升提供明确的指引和数据支持。

---

#### **二、 报表布局与组件设计**

采用标准的四区域布局：**A区（筛选器）**、**B区（核心指标）**、**C区（可视化分析）**、**D区（明细列表）**。

![坐席个人成长报表布局](https://i.imgur.com/example.png)  <!-- 预留图 -->

---

#### **三、 各区域详细说明**

##### **A区：筛选器**

此区域提供全局的数据筛选能力，所有后续区域的数据均会根据此处的选择进行联动刷新。

*   **组件1：时间范围选择器 (DatePicker.RangePicker)**
    *   **功能:** 用户可以选择预设的时间段（如"上周"、"上月"、"近90天"）或自定义起止日期来查看该时段内的个人表现。
    *   **默认值:** "上月"。

*   **组件2：质检方案选择器 (Select)**
    *   **功能:** 用户可以筛选特定的质检方案（如"服务流程类"、"销售技巧类"），以查看自己在特定业务场景下的表现。
    *   **选项:** 动态加载所有该坐席被质检过的方案列表。
    *   **默认值:** "全部方案"。

##### **B区：个人成绩仪表盘**

此区域展示坐席在选定时间范围内的核心绩效指标，并提供与历史表现的对比。

*   **指标卡1：近期平均分**
    *   **主指标:** 坐席在筛选周期内的质检平均分。
    *   **附指标:** 与上一个周期的平均分对比，显示上升/下降箭头和具体分值变化。

*   **指标卡2：近期合格率**
    *   **主指标:** 坐席在筛选周期内的质检合格率。
    *   **附指标:** 与上一个周期的合格率对比，显示上升/下降箭头和百分比变化。

*   **指标卡3：班内排名**
    *   **主指标:** `xx / xx` (例如 `5 / 22`)，显示个人在所属班组内的分数排名。
    *   **附指标:** `前 xx%`，显示排名所处的百分位。

*   **指标卡4：中心排名**
    *   **主指标:** `xx / xx` (例如 `80 / 350`)，显示个人在整个呼叫中心的排名。
    *   **附指标:** `前 xx%`，显示排名所处的百分位。

##### **C区：可视化分析**

此区域通过图表形式，多维度、动态地展示坐席的能力状况和历史趋势。

*   **图表1：个人能力雷达图**
    *   **图表类型:** 雷达图 (Radar Chart)。
    *   **目的:** 从多个能力维度（如`服务态度`、`业务能力`、`合规遵循`、`沟通技巧`、`问题解决`等，维度来自质检方案的一级评分项），综合评估个人能力。
    *   **数据系列:**
        1.  **个人得分 (实线):** 展示坐席在各个能力维度的平均得分率。
        2.  **班组平均 (虚线):** 展示坐席所在班组在这些维度上的平均得分率。
        3.  **中心平均 (点状线):** 展示呼叫中心全体人员在这些维度上的平均得分率。
    *   **交互:** 鼠标悬浮在节点上时，显示具体得分。

*   **图表2：历史成绩曲线**
    *   **图表类型:** 折线图 (Line Chart)。
    *   **目的:** 展示个人质检平均分随时间的变化趋势，直观反映进步或退步。
    *   **X轴:** 时间（按日/周/月聚合）。
    *   **Y轴:** 平均分。
    *   **交互:** 鼠标悬浮在数据点上时，显示具体日期的平均分和质检量。

##### **D区：待改进项与学习资源**

此区域提供具体的失分明细和相关的学习材料，将数据分析落地到具体的改进动作。

*   **列表1：知识点/失分项详情**
    *   **目的:** 清晰列出所有被扣分的质检项，帮助坐席聚焦于需要改进的具体环节。
    *   **表格列:**
        *   `失分项`: 被扣分的具体规则项名称（如"未使用标准开场白"）。
        *   `质检方案`: 该规则所属的质检方案。
        *   `扣分次数`: 在选定周期内，该项被扣分的总次数。
        *   `平均分率`: 该项的平均得分率。
        *   `操作`: 提供"查看案例"按钮，点击后筛选出所有在该项上失分的具体通话记录。
    *   **排序:** 默认按"扣分次数"降序排列。

*   **列表2：案例库推荐**
    *   **目的:** 提供正反两方面的案例，供坐席模仿学习和对比反思。
    *   **内容:**
        *   **我的问题案例:** 自动筛选出该坐席得分最低的 Top 5 通话记录。
        *   **团队优秀案例:** 自动筛选出其所在班组得分最高的 Top 5 通话记录。
    *   **表格列 (对两种案例通用):**
        *   `通话ID`
        *   `质检得分`
        *   `质检日期`
        *   `亮点/失分点标签`: 用标签形式展示该案例的主要优点或缺点。
        *   `操作`: 提供"播放录音"和"查看详情"的链接。 