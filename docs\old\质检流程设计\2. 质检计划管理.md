---

### **2. 质检计划管理**

**概念说明:** 一个用于自动、周期性创建"质检任务"的规则模板。它定义了"以什么频率"（例如每天、每周）、针对"哪个动态时间段"（例如昨天、上周）的通话数据、使用"哪个质检方案"来生成具体的质检任务。计划本身不执行质检，只负责在预定时间自动创建任务。其生命周期为"活动中"、"已暂停"和"已过期"。

### **2. 质检计划管理 - 计划列表页**

**页面目标:** 让质检主管能够集中管理所有自动、周期性运行的质检规则（即"计划"），并清晰地了解每个计划的运行状态和历史。

**页面布局:**

采用经典的"筛选区 + 列表区"布局。

#### **2.1 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 质检计划管理`
*   **页面标题:** `质检计划管理`
*   **主要操作按钮:**
    *   `+ 新建计划` (醒目的蓝色主按钮)

#### **2.2 筛选与搜索区**

*   **计划名称:** `[输入框]`，提示文字为"请输入计划名称关键词"。
*   **计划状态:** `[下拉选择框]`，选项包括：
    *   全部状态
    *   活动中
    *   已暂停
    *   已过期
*   **创建人:** `[人员搜索框]`
*   **创建时间:** `[日期范围选择器]`
*   **操作按钮:** `查询`、 `重置`

#### **2.3 计划列表区**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **计划名称** | - 显示用户自定义的计划名称。<br>- 下方可附带一行小字的计划描述。 |
| **计划状态** | - 使用带有颜色和文字的"标签 (Tag)"组件：<br>  - `活动中` (绿色) <br>  - `已暂停` (橙色) <br>  - `已过期` (灰色) |
| **执行周期** | - 简洁描述重复规则。<br>- 示例: `每天 02:00` 或 `每周一 09:00` |
| **下次运行时间**| - 动态计算并显示下一次计划触发并生成任务的时间。<br>- 示例: `2023-11-20 02:00:00` |
| **上次运行状态**| - 显示此计划最近一次生成的任务的执行状态。<br>- `成功` (绿色) / `失败` (红色) / `执行中` (蓝色) / `无` |
| **质检范围（摘要）** | - 简洁地展示核心范围信息。<br>- 示例: `数据来源: 原始数据 | 质检对象: A组, B组 | 时间范围: 昨天` |
| **质检方案** | - 显示该计划选用的方案名称。 |
| **创建信息** | - 显示 `创建人` 和 `创建时间`。 |
| **操作** | - `查看历史`: 跳转到 **质检任务列表页**，并自动筛选出所有由该计划生成的历史任务记录。<br>- `复制`: 点击后跳转到"新建计划"页面，并填充此计划的所有配置。<br>- **条件操作:**<br>  - `状态为"活动中"`: 显示 `暂停`。<br>  - `状态为"已暂停"`: 显示 `继续`。<br>  - `任何状态`: 均可 `编辑`、`删除` (删除需二次确认)。 |

#### **2.4 页面底部**

*   **分页器 (Pagination):** 标准的分页组件。

---

### **2. 质检计划管理 - 新建计划页**

**页面目标:** 通过清晰的引导，帮助用户创建一个可长期、自动运行的质检计划。

**设计理念:** 页面核心是定义 **"什么时间"、"对什么数据"、"做什么检查"**。

#### **2.1 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 质检计划管理 / 新建计划`
*   **页面标题:** `新建质检计划`

#### **2.2 表单区域**

**第一部分：基本信息**

*   **计划名称:** `[输入框]`，必填。
*   **计划描述:** `[文本域 Textarea]`，选填。

**第二部分：执行计划 (核心)**

*   **重复频率:** `[下拉选择框]`，必填。(每天、每周、每月)
*   **执行时间:** `[时间选择器]`，必填。用于设定在频率当天几点执行。
*   **结束于:** `[日期选择器]` (选填，不填则为永不结束)。

**第三部分：质检范围 (数据从哪里来)**

*   **数据来源:** `[单选按钮组]`，必填。
    *   `◎ 原始通话数据`
    *   `◎ 二次质检 (既有质检结果)`
    *   `◎ 导入外部数据`

---

***当选择 `原始通话数据` 时:***

*   **通话时间范围:** `[下拉选择框]`，必填。
    *   **由于是计划，必须是动态时间范围。** 选项为相对时间规则，如 `过去24小时`、`昨天`、`本周`、`上周`、`本月`、`上月`。
*   **质检对象:** `[单选按钮组]`，必填。
    *   `◎ 全部坐席`
    *   `◎ 按组织架构选择` -> `[组织架构树]`
    *   `◎ 指定坐席` -> `[穿梭框]`

---

***当选择 `二次质检` 时:***

*   **筛选逻辑同原设计，但所有时间范围相关选项变为动态。**
*   **来源任务:** `[下拉多选框]`，选填。
*   **来源质检方案:** `[下拉多D选框]`，选填。
*   **原始质检得分:** `[范围输入框]`，选填。
*   **被检坐席:** `[人员搜索框]`，选填。
*   **原始质检完成时间:** `[下拉选择框]`，必填。选项同上方的动态时间范围，如`昨天`、`上周`。

---

***当选择 `导入外部数据` 时:***

*   **数据指定方式:** `[单选按钮组]`
    *   `◎ 监控目录`
    *   `◎ 按API接口同步`
    *   `◎ 数据库直连`
*   **所有与时间相关的参数都必须使用动态/相对值。**
    *   例如，在"按API接口同步"时，如果方案需要时间参数，则输入框变为 `[下拉选择框]`，选项为 `{{plan.execution_date}}` (计划执行日期) 等系统变量。

**第四部分：质检规则 (做什么检查)**

*   **质检方案:** `[下拉选择框]`，必填项。
*   **质检模式:** `[单选按钮组]`，必填项。
    *   `◎ 全量质检`
    *   `◎ 抽样质检` -> 抽样规则配置...

#### **2.3 页面底部操作区**

*   `创建计划` (蓝色主按钮)
*   `保存草稿`
*   `取消`

---

### **设计亮点总结 (新)**

1.  **职责分离:** "计划"专注于定义自动化规则，"任务"专注于记录单次执行。概念清晰，管理方便。
2.  **简化配置:** "新建计划"页面强制使用动态时间范围和周期性设置，移除了所有一次性任务的选项，大大降低了用户配置的复杂度和出错可能性。
3.  **强大的追溯性:** 从"计划列表"可以一键查看由该计划生成的所有历史"任务"，便于追踪自动化执行的效果和问题排查。

---

### **质检任务详情页设计**

**页面定位:** 这是一个数据展示和分析的汇总页面，是质检结果的"第一站"。用户从任务列表点击"查看详情"后进入此页。

**设计理念:** 采用"仪表盘（Dashboard）"式布局，将关键信息以卡片（Card）的形式模块化展示，主次分明，便于快速获取核心数据，同时支持下钻到更详细的分析。

---

#### **1. 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 质检任务管理 / [任务名称] 详情` (动态显示当前任务名称)
*   **页面主标题:** `[任务名称]` (例如: "11月营销活动通话质检")
*   **页面副标题/标签:** 在主标题下方，用标签（Tag）展示任务的核心属性，方便用户快速回忆。
    *   `已完成` (绿色标签) / `执行中` (蓝色标签)
    *   `全量质检` / `抽样质检`
    *   `质检方案: 营销流程质检方案`

#### **2. 任务概览卡片区 (Key Metrics)**

页面顶部下方，横向排列 4-5 个数据卡片，用大号数字和简洁的标题突出显示最重要的宏观指标。

*   **卡片1: 质检进度 (如果任务执行中)**
    *   **标题:** `质检进度`
    *   **内容:**
        *   一个环形进度图（Circular Progress Bar）。
        *   中间显示百分比，如 `85%`。
        *   下方显示具体数字 `已检 8500 / 总量 10000`。
*   **卡片2: 质检总量 (如果任务已完成)**
    *   **标题:** `质检录音总数`
    *   **内容:**
        *   一个醒目的大号数字，如 `10,000`。
        *   下方小字说明，如 `条`。
*   **卡片3: 整体平均分**
    *   **标题:** `任务平均分`
    *   **内容:**
        *   大号数字，如 `82.5` 分。
        *   与上个周期或公司平均分对比的升降箭头，如 `↑ 2.1 分` (绿色) 或 `↓ 1.5 分` (红色)。
*   **卡片4: 整体合格率**
    *   **标题:** `合格率` (假设合格线为60分)
    *   **内容:**
        *   大号数字，如 `92%`。
        *   下方可以有一个简单的说明 `(合格分数 ≥ 60)`。
*   **卡片5: 触发复核量**
    *   **标题:** `进入人工复核`
    *   **内容:**
        *   大号数字，如 `430` 条。
        *   下方小字显示占比 `(占总量 4.3%)`。

---

#### **3. 数据分析图表区 (Data Visualization)**

概览卡片下方，是页面的核心内容，通过图表对质检结果进行多维度分析。

**左侧主区域 (占用约 2/3 宽度):**

*   **模块一: 质检分数分布图 (直方图)**
    *   **标题:** `质检分数段分布`
    *   **图表类型:** 直方图 (Histogram) 或 条形图 (Bar Chart)。
    *   **X轴:** 分数段，如 `<60`, `60-70`, `70-80`, `80-90`, `90-100`。
    *   **Y轴:** 该分数段内的录音数量。
    *   **交互:** 鼠标悬浮在每个柱子上时，显示具体数量和占比。点击柱子，下方"质检明细列表"可以联动筛选出对应分数段的录音。

*   **模块二: 质检明细列表 (可下钻的表格)**
    *   **标题:** `质检明细`
    *   **功能:** 这是查看具体每一条录音质检结果的入口。
    *   **上方筛选器:**
        *   `坐席姓名/工号`: [输入框]
        *   `质检分数`: [范围输入框，如 60 - 80]
        *   `是否已复核`: [下拉框：全部、是、否]
        *   `[查询]` `[重置]` 按钮
    *   **表格列 (Columns):**
        | 列标题 | 内容与设计说明 |
        | :--- | :--- |
        | **录音ID** | 唯一标识符。 |
        | **被检坐席** | 姓名/工号。 |
        | **所属团队** | 坐席所在的技能组/班组。 |
        | **通话时间** | YYYY-MM-DD HH:mm:ss。 |
        | **AI初检得分** | 智能质检给出的分数。 |
        | **人工复核得分** | - 如果未复核，显示 "--"。 <br>- 如果已复核，显示最终分数。分数与AI不同时，可加星号或背景色提示。 |
        | **状态** | 用标签显示：`待复核` (橙色)、`已复核` (绿色)、`无需复核` (灰色)。 |
        | **操作** | `查看` - 点击后跳转到单条录音的**质检报告页**（包含录音播放、文本、评分表等）。 |
    *   **分页:** 表格下方有标准的分页组件。

**右侧副区域 (占用约 1/3 宽度):**

*   **模块三: 高频失分项分析 (水平条形图)**
    *   **标题:** `TOP 5 高频失分项`
    *   **图表类型:** 水平条形图，按失分次数降序排列。
    *   **Y轴:** 评分项名称，如"未使用标准开场白"、"未主动确认客户问题"、"服务态度消极"。
    *   **X轴:** 出现该失分的次数（或占比）。
    *   **交互:** 点击某个失分项，可以联动左侧的"质检明细列表"，筛选出所有在该项上失分的录音。

*   **模块四: 优秀/待改进坐席榜单**
    *   **标题:** `坐席表现榜`
    *   **设计:** 使用两个页签（Tabs）切换：`红榜 (TOP 5)` 和 `黑榜 (BOTTOM 5)`。
    *   **红榜 (高分榜):**
        *   列表展示平均分最高的5位坐席。
        *   内容: `排名 | 坐席姓名 | 平均分 | 合格率`
    *   **黑榜 (低分榜):**
        *   列表展示平均分最低的5位坐席。
        *   内容: `排名 | 坐席姓名 | 平均分 | 不合格录音数`
    *   **交互:** 点击任一坐席姓名，可以联动左侧的"质检明细列表"，筛选出该坐席的所有质检记录。

*   **模块五: 任务基础信息**
    *   **标题:** `任务信息`
    *   **设计:** 一个简洁的只读信息列表。
    *   **内容:**
        *   `任务ID:` 10086
        *   `质检方案:` [营销流程质检方案] (可点击预览)
        *   `质检范围:` 坐席组A、B、C
        *   `通话时段:` 2023.11.01 至 2023.11.10
        *   `创建人:` 张主管
        *   `创建时间:` 2023.11.12 09:00:00
        *   `完成时间:` 2023.11.12 18:30:00

---

### **设计亮点总结**

1.  **信息分层，主次分明:** 从顶部的宏观指标，到中部的多维图表，再到底部的详细列表，构成了一个从"总-分-细"的分析路径，符合用户认知习惯。
2.  **高度可视化:** 大量使用图表（环形图、直方图、条形图），将枯燥的数据转化为直观的视觉信息，便于快速发现问题和亮点。
3.  **强交互与联动:** 页面上的各个模块不是孤立的。通过点击图表、榜单，可以联动筛选明细数据，实现了"指哪打哪"的探索式分析，极大地提升了数据挖掘的效率。
4.  **提供 actionable insights (可行动的洞察):** "高频失分项"直接指明了培训改进的方向，"红黑榜"则为绩效管理和标杆学习提供了直接依据。
5.  **下钻能力:** 页面提供了从任务全局 -> 单条录音明细 的下钻入口（通过明细列表的"查看"按钮），保证了分析的深度和完整性。

---

### **单条录音质检报告页设计**

**页面定位:** 这是质检结果的最终落脚点，是坐席查看成绩、发起申诉，以及复核员/主管进行复核、处理申诉的工作台。其设计必须兼顾信息展示的全面性和操作的高效性。

**设计理念:** 采用经典的**三栏式布局**，将不同类型的信息在逻辑上进行物理分区，让用户的视线可以自然地在"听"（音频）、"读"（文本）、"评"（打分）之间流转。

---

#### **页面整体布局**

```
+--------------------------------------------------------------------------+
| 面包屑导航 & 录音基础信息 & 申诉状态                                     |
+--------------------------------------------------------------------------+
| 左栏 (1/4宽度)          | 中栏 (1/2宽度)                | 右栏 (1/4宽度)       |
|                         |                               |                      |
| [ 音频播放器 ]          | [ 通话转写文本 ]              | [ 评分详情卡片 ]     |
| [ 坐席/客户画像 ]       | [ AI智能分析标注 ]            | [ 评分表 ]           |
| [ 质检历史 ]            |                               | [ 复核/申诉意见 ]    |
|                         |                               |                      |
|                         |                               | [ 操作按钮 ]         |
+--------------------------------------------------------------------------+
```

---

#### **1. 页面顶部区域**

*   **面包屑导航:** `首页 / ... / 任务详情 / 质检报告 (录音ID: 3B45F1)`
*   **录音基础信息条:**
    *   **主叫:** 138****1234
    *   **被叫:** 8001 (王小明)
    *   **通话时间:** 2023-11-05 14:30:15
    *   **通话时长:** 05:28
*   **状态标签区 (非常重要):** 在右侧醒目位置，用不同颜色的标签展示当前记录的生命周期状态。
    *   `最终得分: 85分` (主标签)
    *   `已复核` (绿色)
    *   `申诉已关闭 (维持原判)` (灰色)
    *   *其他可能的状态：`待复核` (橙色)、`申诉中` (蓝色)、`申诉成功` (绿色)*

---

#### **2. 左栏：音频与辅助信息区**

*   **音频播放器卡片:**
    *   **标题:** `通话录音`
    *   **双音轨波形图:** 上方是客户音轨，下方是坐席音轨，视觉上区分。
    *   **播放控件:** `播放/暂停`、`进度条拖拽`、`音量控制`、`倍速播放 (0.5x, 1x, 1.5x, 2x)`。
    *   **联动高亮:** 播放时，中栏的转写文本会同步滚动并高亮当前说到的句子。
*   **坐席/客户画像卡片:**
    *   **标题:** `通话画像`
    *   **坐席信息:**
        *   **姓名/工号:** 王小明 / 8001
        *   **所属团队:** 客服A组
    *   **客户信息 (如有数据):**
        *   **客户标签:** `高价值客户` `近期有投诉`
        *   **来电意图 (AI预测):** `咨询退货流程`
*   **质检历史卡片:**
    *   **标题:** `处理流程`
    *   **时间轴 (Timeline) 组件:** 垂直展示此条记录的处理历史。
        *   `2023-11-10 10:00` AI质检完成，得分 `75`。
        *   `2023-11-10 10:01` 因"低分"自动进入人工复核池。
        *   `2023-11-11 15:20` 复核员【李主管】完成复核，得分修正为 `85`。
        *   `2023-11-12 09:30` 坐席【王小明】发起申诉。
        *   `2023-11-12 16:45` 申诉处理人【张总监】完成处理，维持原判。

---

#### **3. 中栏：通话转写与AI分析区**

*   **标题:** `通话内容 (智能转写)`
*   **功能切换:** 提供两个页签 `[对话视图]` 和 `[全文视图]`。
    *   **对话视图 (默认):** 模拟聊天软件的样式，左侧是客户气泡，右侧是坐席气泡，清晰区分发言人。
    *   **全文视图:** 纯文本展示，方便复制。
*   **文本与AI标注:**
    *   **高亮与图标:**
        *   **关键词:** 命中的关键词（如"投诉"、"优惠"）会用 `黄色背景高亮`。
        *   **情绪:** 客户的负面情绪句子旁，会有一个 `红色愤怒表情` 😠；正面情绪句子旁有 `绿色笑脸` 😊。
        *   **静音:** 超过阈值的静音段落，会显示一个 `静音图标` 🔇 和时长（如 `静音 15s`）。
        *   **抢话/打断:** 出现抢话的地方，会有 `抢话图标` ⚡️。
        *   **语速:** 语速过快/过慢的段落，会有 `快进` ⏩ 或 `慢放` ⏪ 图标。
    *   **交互:**
        *   **点击文本跳转音频:** 点击任意一句话，左侧的音频播放器会立即跳转到该句话开始播放。
        *   **鼠标悬浮看详情:** 鼠标悬浮在各种高亮和图标上时，会弹出小窗（Tooltip）显示详细信息，如"命中规则：严禁词-退款"、"客户情绪得分：-0.8 (愤怒)"。

---

#### **4. 右栏：评分与操作区**

这是质检结果的核心呈现区域，也是人工干预的主要区域。

*   **评分详情卡片:**
    *   **标题:** `质检评分详情`
    *   **质检方案:** `标准服务流程质检方案`
    *   **总分展示:**
        *   **AI初检得分:** `75`
        *   **人工复核得分:** `85` (如果与AI不同，用醒目颜色展示)
        *   **最终得分:** `85` (大号字体)
*   **评分表 (可折叠的树状结构):**
    *   以可折叠的列表展示评分表的每一项。
    *   **一级类目 (如: 服务开场 - 10/10分):**
        *   **二级评分项:**
            *   `标准开场白 (5/5分)` ✅
            *   `主动核身 (5/5分)` ✅
    *   **一级类目 (如: 业务解决 - 35/40分):**
        *   **二级评分项:**
            *   `准确理解客户问题 (10/10分)` ✅
            *   `提供正确解决方案 (15/20分)` ❌ (用红色叉号或感叹号表示失分)
                *   *失分原因 (AI或人工标注): 未告知客户退货的运费险规则。*
            *   `...`
    *   **只读/可编辑:**
        *   对于**坐席和普通查看者**，此区域为**只读**。
        *   对于**复核员或申诉处理人**，在他们的工作台模式下，分数是**可编辑**的，修改后需要填写理由。
*   **评语与意见卡片:**
    *   **标题:** `评语与意见`
    *   **AI评语 (可选):** 基于AI分析生成的简短总结。
    *   **复核员评语:** `【李主管】: 这次通话整体不错，能快速定位问题。但在解决方案环节，关于运费险的解释不够清晰，导致客户追问，这是主要失分点，下次注意。`
    *   **坐席申诉理由:** `【王小明】: 我认为关于运费险的解释是清晰的，录音3分15秒处有提到，申请重听。`
    *   **申诉处理意见:** `【张总监】: 已重听录音。3分15秒处确实提及运费险，但未主动说明"仅限首次退货"，客户后续追问点在此。复核员判定准确，维持原判。`
*   **操作按钮区 (根据用户角色和状态动态显示):**
    *   **对坐席:**
        *   如果未申诉且在申诉期内: `[发起申诉]` (蓝色按钮)
        *   如果已申诉: (无操作按钮)
    *   **对复核员/处理人 (在他们的任务中打开时):**
        *   `[提交复核结果]` / `[提交申诉结果]`
        *   `[暂存]`

---

### **设计亮点总结**

1.  **360度信息全景:** 将一次通话的所有相关方、所有信息（音、文、评、史）聚合在单一视图，构建了信息透明的"唯一真相来源"。
2.  **角色化视图与操作:** 同一个页面，根据登录用户的角色和当前记录的状态，展示不同的信息权限和操作按钮，做到了精准控制。
3.  **强关联与联动:** 音频、文本、AI标注三者深度联动，极大提升了听音和分析的效率。
4.  **过程可追溯:** "质检历史"时间轴让整个处理流程（从AI质检到申诉关闭）一目了然，保证了过程的公正和透明。
5.  **聚焦核心:** 三栏式布局让用户的注意力自然分区，避免了信息过载，无论是快速浏览结果还是深度分析细节，都能找到舒适的路径。