### “关键词检查”算子核心用途

**关键词检查** 主要用于在客服、客户或双方的对话中，检测**是否存在**或**不存在**某些特定的词语或短语。

它的配置非常灵活，可以细化到：
*   **谁说的**（检测角色：客服/客户/所有角色）
*   **在哪段对话里说**（检测范围：全文/指定句子）
*   **关键词的组合逻辑**（包含任意一个、包含全部、不包含等）
*   **分析的粒度**（单句分析/多句分析）

### 主要应用场景及示例

根据其功能，关键词检查通常用于以下三大场景：

---

#### 场景一：确保服务流程的完整性（必须提及）

这是为了确保坐席在服务过程中使用了必要的话术，例如开场白、结束语、关键信息确认等。

*   **示例1：检查标准的开场白**
    *   **目标**：确保客服在对话开始时使用了标准的欢迎语。
    *   **配置**：
        *   **检测角色**：客服
        *   **检测范围**：前3句
        *   **关键词**：`您好`, `很高兴为您服务`, `有什么可以帮您`
        *   **检测类型**：包含任意关键词
    *   **说明**：如果客服在前三句话中说了以上任意一个词，则认为“使用了开场白”，规则命中。反之，如果需要检查是否“没有使用开场白”，可以创建一个相反的规则。

*   **示例2：检查关键信息的确认**
    *   **目标**：在金融或电商场景下，确保客服在下单或办理业务时，明确提及了风险或确认了关键信息。
    *   **配置**：
        *   **检测角色**：客服
        *   **关键词**：`确认订单`, `风险提示`, `您是否同意`
        *   **检测类型**：包含全部上述关键词
        *   **分析方式**：多句分析
    *   **说明**：在整个通话中，客服必须同时提到 “确认订单”、“风险提示” 和 “您是否同意” 这几个关键词才算合规。这能有效避免因关键信息遗漏导致的后续纠纷。

---

#### 场景二：监控服务中的违规或负面言论（禁止提及）

用于检测对话中是否出现了辱骂、承诺保证、泄露公司机密或使用不文明用语等情况。

*   **示例1：检测不文明用语**
    *   **目标**：确保客服和客户在对话中没有使用脏话或攻击性言论。
    *   **配置**：
        *   **检测角色**：所有角色
        *   **关键词**：(此处列出不文明词汇库) `傻*`, `滚`, `垃圾`...
        *   **检测类型**：全部不包含 (的反向逻辑，即包含任意一个就触发)
    *   **说明**：在整个通话中，只要任何一方说出任何一个预设的“黑名单词汇”，规则就会立即命中，并标记为严重违规。

*   **示例2：禁止过度承诺**
    *   **目标**：防止销售人员做出“100%盈利”、“绝对有效”等无法兑现的承诺。
    *   **配置**：
        *   **检测角色**：客服
        *   **关键词**：`保证`, `百分百`, `绝对没问题`, `肯定能`
        *   **检测类型**：包含任意关键词
    *   **说明**：只要客服在对话中使用了任何一个表示过度承诺的词语，就会被系统标记，便于后续审查。

---

#### 场景三：识别客户意图或业务机会

通过检测客户提到的关键词，可以自动识别客户的意图，如投诉、购买意向、咨询特定产品等，从而进行后续的数据分析或触发特定流程。

*   **示例1：识别客户投诉意图**
    *   **目标**：快速识别出表达不满或有投诉倾向的客户。
    *   **配置**：
        *   **检测角色**：客户
        *   **关键词**：`投诉`, `不满意`, `太差了`, `找你们领导`
        *   **检测类型**：包含任意关键词
    *   **说明**：一旦检测到客户说了这些词，系统可以自动给该通会话打上“投诉意向”的标签，方便质检员优先复核或数据统计。

*   **示例2：发现产品改进建议**
    *   **目标**：从客户的反馈中挖掘产品或服务的改进点。
    *   **配置**：
        *   **检测角色**：客户
        *   **关键词**：`建议`, `希望增加`, `要是能`
        *   **检测类型**：包含任意关键词
    *   **说明**：此方法可捕捉包含明确建议词汇的语句。对于如“如果...就好了”这类更复杂的**文本模式**，则需要使用 **`正则表达式检查`** 算子（例如，使用表达式 `如果.*就好了`）来实现更精确的匹配。