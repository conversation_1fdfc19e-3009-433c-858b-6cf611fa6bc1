import React from 'react';

export interface TextSimilarityConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  similarityThreshold: number;
  templates: string[];
  singleSentence: boolean;
}

interface TextSimilarityConfigProps {
  config: TextSimilarityConfig;
  setConfig: (config: TextSimilarityConfig) => void;
}

/**
 * 文本相似度配置演示组件
 */
const TextSimilarityConfigComponent: React.FC<TextSimilarityConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      {/* 配置规则说明 */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📋</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>客服</strong>：仅检测"客服："开头的对话</li>
                    <li>• <strong>客户</strong>：仅检测"客户："开头的对话</li>
                    <li>• <strong>所有角色</strong>：检测全部对话内容</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>全文</strong>：检测所有符合角色条件的句子</li>
                    <li>• <strong>指定范围</strong>：只检测第X~Y句之间的内容</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📊 相似度阈值</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• 设置0-100的相似度要求</li>
                    <li>• 建议设置80%左右的阈值</li>
                    <li>• 阈值越高匹配越严格</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">⚡ 扩展功能</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>单句话内生效</strong>：按标点符号拆分后分别检测</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 前置条件 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="none">无</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-20 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeStart}
                onChange={(e) => setConfig({ ...config, rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeEnd}
                onChange={(e) => setConfig({ ...config, rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="指定检测的句子范围，例如第1~3句表示只检测前3句对话"
              >
                ?
              </span>
            </div>
          </div>
        )}

        {/* 相似度阈值 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">相似度达到</label>
          <div className="flex items-center space-x-2">
            <input
              type="number"
              min="0"
              max="100"
              className="w-20 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.similarityThreshold}
              onChange={(e) => setConfig({ ...config, similarityThreshold: parseInt(e.target.value) || 0 })}
            />
            <span className="text-sm text-gray-700">%</span>
            <span 
              className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
              title="设置相似度阈值，建议80%。阈值越高匹配越严格，越低越宽松。"
            >
              ?
            </span>
          </div>
        </div>

        {/* 相似话术 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">相似话术</label>
          <div className="flex-1">
            <div className="flex">
              <input
                type="text"
                className="flex-1 max-w-lg px-3 py-2 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入标准话术，按Enter键添加，支持逗号分隔批量输入"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    const input = (e.target as HTMLInputElement).value.trim();
                    if (input) {
                      // 支持逗号分隔的批量输入
                      const templates = input.split(/[，,]/).map(tpl => tpl.trim()).filter(tpl => tpl);
                      const newTemplates = templates.filter(tpl => !config.templates.includes(tpl));
                      if (newTemplates.length > 0) {
                        setConfig({ ...config, templates: [...config.templates, ...newTemplates] });
                      }
                      (e.target as HTMLInputElement).value = '';
                    }
                  }
                }}
              />
              <button 
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm rounded-r-md hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                onClick={(e) => {
                  const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement;
                  const inputValue = input.value.trim();
                  if (inputValue) {
                    // 支持逗号分隔的批量输入
                    const templates = inputValue.split(/[，,]/).map(tpl => tpl.trim()).filter(tpl => tpl);
                    const newTemplates = templates.filter(tpl => !config.templates.includes(tpl));
                    if (newTemplates.length > 0) {
                      setConfig({ ...config, templates: [...config.templates, ...newTemplates] });
                    }
                    input.value = '';
                  }
                }}
              >
                🤖 AI优化
              </button>
            </div>
            {config.templates.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {config.templates.map((tpl, i) => (
                  <span key={i} className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-800">
                    {tpl}
                    <button
                      onClick={() => setConfig({ ...config, templates: config.templates.filter((_, idx) => idx !== i) })}
                      className="ml-1 text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 扩展功能 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">扩展功能</label>
          <div className="flex flex-col space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-2 text-blue-600 focus:ring-blue-500 rounded"
                checked={config.singleSentence}
                onChange={(e) => setConfig({ ...config, singleSentence: e.target.checked })}
              />
              <span className="text-sm text-gray-700">单句话内生效</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="将句子按标点符号拆分为小段，每小段单独与模板进行相似度比较。"
              >
                ?
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextSimilarityConfigComponent; 