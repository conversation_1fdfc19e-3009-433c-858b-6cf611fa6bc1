import React, { useState, useMemo } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { Plus, Edit, Trash2, PlayCircle, PauseCircle, Eye, Calendar } from 'lucide-react';
import { motion } from 'framer-motion';
import FinalCreatePlanPage from './FinalCreatePlanPage';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import dayjs from 'dayjs';

const qualitySchemeMap: { [key: string]: string } = {
    scheme1: '标准服务流程质检方案',
    scheme2: '营销流程质检方案',
    scheme3: '深度分析方案',
    scheme4: '促销活动专用方案',
};

const frequencyMap: { [key: string]: string } = {
    daily: '每天',
    weekly: '每周',
    monthly: '每月',
};

const initialPlans = [
    {
        key: '1',
        planName: '每日营销流程合规检查',
        planDescription: '检查所有营销坐席的通话是否符合最新合规要求',
        status: '活动中',
        frequency: 'daily',
        executionTime: dayjs('02:00:00', 'HH:mm:ss'),
        dataSourceId: '1',
        dynamicDateRange: 'yesterday',
        targetType: 'byStruct',
        targetKeys: ['0-1-0'],
        qualityScheme: 'scheme2',
        qualityMode: 'full',
        creator: '张主管',
        createTime: '2023-10-01 10:30',
        lastRunStatus: '成功',
        lastRunTime: '2024-06-30 02:00',
    },
    {
        key: '2',
        planName: '每周服务质量抽检计划',
        planDescription: '对服务部门进行10%的随机抽检',
        status: '已暂停',
        frequency: 'weekly',
        executionTime: dayjs('09:00:00', 'HH:mm:ss'),
        dataSourceId: '2',
        dynamicDateRange: 'lastWeek',
        targetType: 'byStruct',
        targetKeys: ['0-0-0', '0-0-1'],
        qualityScheme: 'scheme1',
        qualityMode: 'sampling',
        samplingRatio: 10,
        creator: '李经理',
        createTime: '2023-09-15 14:00',
        lastRunStatus: '成功',
        lastRunTime: '2024-06-24 09:00',
    },
    {
        key: '3',
        planName: '月度不满意通话复检',
        planDescription: '对所有历史质检不及格的通话进行二次分析',
        status: '活动中',
        frequency: 'monthly',
        executionTime: dayjs('04:00:00', 'HH:mm:ss'),
        dataSourceId: '3',
        dynamicDateRange: 'lastMonth',
        targetType: 'byScore',
        minScore: 0,
        maxScore: 60,
        qualityScheme: 'scheme3',
        qualityMode: 'full',
        creator: '王总监',
        createTime: '2023-08-20 11:00',
        lastRunStatus: '失败',
        lastRunTime: '2024-07-01 04:00',
    },
    {
        key: '4',
        planName: '旧的促销活动检查',
        planDescription: '此活动已结束',
        status: '已过期',
        frequency: 'daily',
        executionTime: dayjs('01:00:00', 'HH:mm:ss'),
        dataSourceId: '1',
        dynamicDateRange: 'yesterday',
        targetType: 'byStruct',
        targetKeys: ['0-2-0'],
        qualityScheme: 'scheme4',
        qualityMode: 'full',
        creator: '张主管',
        createTime: '2022-05-01 18:00',
        lastRunStatus: '成功',
        lastRunTime: '2022-05-02 01:00',
    }
];

const FinalPlanListPage: React.FC = () => {
    const navigate = useNavigate();
    const [plans, setPlans] = useState(initialPlans);
    const [isDrawerOpen, setDrawerOpen] = useState(false);
    const [editingPlan, setEditingPlan] = useState<any | undefined>();
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
    });
    const [activeTab, setActiveTab] = useState<'all' | '活动中' | '已暂停' | '已过期'>('all');
    const [searchFilters, setSearchFilters] = useState<any>({
        planName: '',
        creator: '',
        createTime: [],
        qualityScheme: '',
    });

    // 筛选字段配置
    const filterFields: FilterField[] = [
        { key: 'planName', label: '计划名称', type: 'text', placeholder: '请输入计划名称' },
        { key: 'qualityScheme', label: '质检方案', type: 'select', options: [
            { value: 'scheme1', label: '标准服务流程质检方案' },
            { value: 'scheme2', label: '营销流程质检方案' },
            { value: 'scheme3', label: '深度分析方案' },
            { value: 'scheme4', label: '促销活动专用方案' }
        ]},
        { key: 'creator', label: '创建人', type: 'text', placeholder: '请输入创建人姓名' },
        { key: 'createTime', label: '创建时间', type: 'dateRange' }
    ];

    const filteredAndPaginatedPlans = useMemo(() => {
        let filtered = plans;
        if (activeTab !== 'all') {
            filtered = filtered.filter(plan => plan.status === activeTab);
        }

        // Apply search filters
        if (searchFilters.planName) {
            filtered = filtered.filter(plan => plan.planName.includes(searchFilters.planName));
        }
        if (searchFilters.creator) {
            filtered = filtered.filter(plan => plan.creator.includes(searchFilters.creator));
        }
        if (searchFilters.createTime && searchFilters.createTime.length === 2) {
            const [startDate, endDate] = searchFilters.createTime;
            filtered = filtered.filter(plan => {
                const planCreateTime = dayjs(plan.createTime);
                const isAfterStart = startDate ? planCreateTime.isAfter(startDate) || planCreateTime.isSame(startDate, 'day') : true;
                const isBeforeEnd = endDate ? planCreateTime.isBefore(endDate) || planCreateTime.isSame(endDate, 'day') : true;
                return isAfterStart && isBeforeEnd;
            });
        }
        if (searchFilters.qualityScheme) {
            filtered = filtered.filter(plan => plan.qualityScheme === searchFilters.qualityScheme);
        }

        const total = filtered.length;
        const startIndex = (pagination.current - 1) * pagination.pageSize;
        const endIndex = startIndex + pagination.pageSize;
        const currentData = filtered.slice(startIndex, endIndex);
        const totalPages = Math.ceil(total / pagination.pageSize);

        return { data: currentData, total, totalPages };
    }, [activeTab, plans, pagination.current, pagination.pageSize, searchFilters]);

    const handleCreate = () => {
        setEditingPlan(undefined);
        setDrawerOpen(true);
    };
    
    const handleEdit = (plan: any) => {
        setEditingPlan(plan);
        setDrawerOpen(true);
    };

    const handleDelete = (plan: any) => {
        setPlans(plans.filter(p => p.key !== plan.key));
        message.success(`计划 "${plan.planName}" 已删除`);
    };

    const handleFormSubmit = (values: any) => {
        // Convert dayjs objects to string format for consistency in data
        const formattedValues = {
            ...values,
            executionTime: values.executionTime ? values.executionTime.format('HH:mm:ss') : undefined,
        };

        if (values.planId) {
            setPlans(plans.map(p => p.key === values.planId ? { ...p, ...formattedValues } : p));
            message.success(`计划 "${values.planName}" 已更新`);
        } else {
            const newPlan = {
                ...formattedValues,
                key: String(Date.now()),
                status: '活动中',
                creator: '当前用户',
                createTime: dayjs().format('YYYY-MM-DD HH:mm'),
                lastRunStatus: '未运行',
            };
            setPlans([newPlan, ...plans]);
            message.success(`计划 "${values.planName}" 已创建`);
        }
        setDrawerOpen(false);
    };

    // 处理筛选条件变化
    const handleFiltersChange = (newFilters: Record<string, any>) => {
        setSearchFilters(newFilters);
        setPagination(prev => ({ ...prev, current: 1 }));
    };

    // 处理查询
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, current: 1 }));
    };

    // 处理重置
    const handleReset = () => {
        setSearchFilters({
            planName: '',
            creator: '',
            createTime: [],
            qualityScheme: '',
        });
        setPagination(prev => ({ ...prev, current: 1 }));
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="质检计划管理"
                subtitle="创建和管理周期性执行的自动质检计划。"
                icon={Calendar}
                badge={{ text: "计划管理", color: "green" }}
                actions={[
                    {
                        label: "新建计划",
                        icon: Plus,
                        onClick: handleCreate,
                        variant: "primary"
                    }
                ]}
            />
            <main className="p-6 md:p-10">
                 <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    <UnifiedSearchFilter
                         fields={filterFields}
                         filters={searchFilters}
                         onFiltersChange={handleFiltersChange}
                         onSearch={handleSearch}
                         onReset={handleReset}
                     />

                    {/* Tab切换 */} 
                    <div className="mt-6">
                        <div className="border-b border-gray-200">
                            <div className="flex">
                                {[ { key: 'all', label: '全部', count: plans.length },
                                  { key: '活动中', label: '活动中', count: plans.filter(p => p.status === '活动中').length },
                                  { key: '已暂停', label: '已暂停', count: plans.filter(p => p.status === '已暂停').length },
                                  { key: '已过期', label: '已过期', count: plans.filter(p => p.status === '已过期').length },
                                ].map(tab => (
                                    <button
                                        key={tab.key}
                                        onClick={() => {
                                            setActiveTab(tab.key as any);
                                            setPagination(prev => ({ ...prev, current: 1 }));
                                        }}
                                        className={`px-6 py-3 font-medium text-sm border-b-2 ${activeTab === tab.key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
                                    >
                                        {tab.label} ({tab.count})
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                    
                    <div className="overflow-x-auto mt-4">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-4 py-3">序号</th>
                                    <th scope="col" className="px-6 py-3">计划名称</th>
                                    <th scope="col" className="px-6 py-3">计划状态</th>
                                    <th scope="col" className="px-6 py-3">执行周期</th>
                                    <th scope="col" className="px-6 py-3">上次运行时间</th>
                                    <th scope="col" className="px-6 py-3">上次运行状态</th>
                                    <th scope="col" className="px-6 py-3">质检方案</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">创建时间</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredAndPaginatedPlans.data.map((plan, index) => (
                                    <tr key={plan.key} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-4 py-4 text-gray-700">
                                            {(pagination.current - 1) * pagination.pageSize + index + 1}
                                        </td>
                                        <td className="px-6 py-4">
                                            <div>
                                                <a className="font-medium text-blue-600 hover:text-blue-700">{plan.planName}</a>
                                                <div className="text-xs text-gray-500">{plan.planDescription}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                plan.status === '活动中' ? 'bg-green-100 text-green-800' :
                                                plan.status === '已暂停' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                                            }`}>
                                              {plan.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4">
                                            {`${frequencyMap[plan.frequency] || ''} ${plan.executionTime ? dayjs(plan.executionTime, 'HH:mm:ss').format('HH:mm') : ''}`}
                                        </td>
                                        <td className="px-6 py-4">{plan.lastRunTime || '-'}</td>
                                        <td className="px-6 py-4">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                plan.lastRunStatus === '成功' ? 'bg-green-100 text-green-800' :
                                                plan.lastRunStatus === '失败' ? 'bg-red-100 text-red-800' :
                                                plan.lastRunStatus === '执行中' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                                            }`}>
                                              {plan.lastRunStatus}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4">{qualitySchemeMap[plan.qualityScheme] || plan.qualityScheme}</td>
                                        <td className="px-6 py-4">{plan.creator}</td>
                                        <td className="px-6 py-4">{plan.createTime}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onClick={() => handleEdit(plan)} className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors" title="编辑">
                                                <Edit className="w-4 h-4" />
                                            </button>
                                            <button className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors mx-1" title={plan.status === '活动中' ? '暂停' : '启动'}>
                                                {plan.status === '活动中' ? <PauseCircle className="w-4 h-4" /> : <PlayCircle className="w-4 h-4" />}
                                            </button>
                                            <button onClick={() => navigate(`/final-design/task-list?planId=${plan.key}`)} className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors" title="查看执行历史">
                                                <Eye className="w-4 h-4" />
                                            </button>
                                            <button onClick={() => handleDelete(plan)} className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors" title="删除">
                                                <Trash2 className="w-4 h-4" />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredAndPaginatedPlans.data.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的计划。</p>
                        </div>
                    )}
                    <UnifiedPagination
                         current={pagination.current}
                         pageSize={pagination.pageSize}
                         total={filteredAndPaginatedPlans.total}
                         onChange={(page) => setPagination(prev => ({ ...prev, current: page }))}
                     />
                </motion.div>
            </main>
            {isDrawerOpen && (
                 <FinalCreatePlanPage
                    onClose={() => setDrawerOpen(false)}
                    onSubmit={handleFormSubmit}
                    planId={editingPlan?.key}
                    initialValues={editingPlan ? { ...editingPlan, executionTime: dayjs(editingPlan.executionTime, 'HH:mm:ss') } : undefined} // Convert time string back to Dayjs object
                />
            )}
        </div>
    );
};

export default FinalPlanListPage;