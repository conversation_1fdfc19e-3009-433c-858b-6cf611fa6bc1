
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 语音检查 > 能量检测**

产品详情 | ❤️ 我的收藏

# **能量检测**

更新时间: 2025-01-24 18:08:27

本文介绍能量检测如何进行配置。

> **② 说明** 该检测类型仅适用于音频质检。

*   **功能介绍**：录音文件转写成文本后，每句话会有一个语音能量等级。能量等级根本上是用于衡量声音音量的大小，是对声音分贝值的加工；具体等级计算是根据一句话中的多个采样点的分贝值，计算该句的分贝平均值，再除 10 取整，就会得到该句的语音能量等级。例如，某句话的平均分贝值为 53dB，那么该句话的语音能量等级就是 5。能量等级取值范围是 1-10，该算子通过对语音能量等级进行检测分析，从而判断客服/客户在通话过程中是否有大的情绪波动。
*   **配置方法**：设置检测方式：
    *   **能量范围检测**：检测根据适用角色及检测范围筛选出的对话中，能量值“大于/小于”所设置的能量范围的句子。
    *   **相邻句能量波动**：检测根据适用角色及检测范围筛选出的对话中，每相邻两句话的语音能量变化，当后一句与前一句的能量差大于或等于于设定值时，后一句话算作命中。能量差大于或等于于【1-9】（一般设置 1）。
    *   **最大能量跨度**：根据适用角色及检测范围筛选出的所有对话，最大语音能量与最小语音能量的差值，即为最大能量跨度。范围为【1-9】，一般为 3
*   **使用示例**：

**场景一：客服的能量范围检测语音能量等级是否大于 4。**

**条件配置**
| | |
| :--- | :--- |
| **检测内容** | 以下条件全部满足 |
| **检测条件**<br>**查看教程** | **a ^ 能量检测** |
| | **检测角色**：客服 |
| | **检测范围**：全文 |
| | 录音文件转写成文本后，每句话会有一个语音能量等级，等级取值范围是1-10。该算子通过对语音能量等级进行检测分析，从而判断 客服/客户 在通话过程中是否有大的情绪波动。（该检测类型仅适用于音频质检） |
| | **检查方式**：能量范围检测 |
| | **检查逻辑**：语音能量等级 [大于] [4] (可输入范围1-10) |
| | **+ 新增条件** |

**评分配置**
| | |
| :--- | :--- |
| **规则评分** | (开关按钮，处于关闭状态) |

**场景二：客服的相邻句能量波动的能量差大于或等于 1。**

**条件配置**
| | |
| :--- | :--- |
| **检测内容** | 以下条件全部满足 |
| **检测条件**<br>**查看教程** | **a ^ 能量检测** |
| | **检测角色**：客服 |
| | **检测范围**：全文 |
| | 录音文件转写成文本后，每句话会有一个语音能量等级，等级取值范围是1-10。该算子通过对语音能量等级进行检测分析，从而判断 客服/客户 在通话过程中是否有大的情绪波动。（该检测类型仅适用于音频质检） |
| | **检查方式**：相邻句能量波动 |
| | **检查逻辑**：能量差大于或等于 [1] (可输入范围1-9) |
| | **+ 新增条件** |

**评分配置**
| | |
| :--- | :--- |
| **规则评分** | (开关按钮，处于关闭状态) |

**场景三：检测客服角色的某个范围的对话能量最大值与最小值的差值为 3**

**条件配置**
| | |
| :--- | :--- |
| **检测内容** | 以下条件全部满足 |
| **检测条件**<br>**查看教程** | **a ^ 能量检测** |
| | **检测角色**：客服 |
| | **检测范围**：全文 |
| | 录音文件转写成文本后，每句话会有一个语音能量等级，等级取值范围是1-10。该算子通过对语音能量等级进行检测分析，从而判断 客服/客户 在通话过程中是否有大的情绪波动。（该检测类型仅适用于音频质检） |
| | **检查方式**：最大能量跨度 |
| | **检查逻辑**：能量差大于或等于 [3] (可输入范围1-9) |
| | **+ 新增条件** |

**评分配置**
| | |
| :--- | :--- |
| **规则评分** | (开关按钮，处于关闭状态) |