import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PageHeader } from '../../PageHeader';
import { Search, Settings, BarChart3, Sprout, HardHat, UserCheck, Check, CornerDownRight, ShieldCheck, BookCopy, Users, User, Headset } from 'lucide-react';

const designPrinciples = [
  {
    title: '面向角色',
    description: '不同角色（质检员、业务主管）的核心关注点不同，菜单设计应让他们能最快找到自己的高频功能区。',
    icon: UserCheck,
  },
  {
    title: '任务导向',
    description: '菜单结构遵循"配置 -> 执行 -> 复核 -> 分析 -> 赋能"的业务逻辑闭环，引导用户高效完成工作流。',
    icon: Check,
  },
  {
    title: '聚合与降噪',
    description: '将关联性强的功能聚合在同一顶级菜单下，避免菜单层级过深或顶级菜单过多，保持界面清爽。',
    icon: BookCopy,
  },
  {
    title: '清晰命名',
    description: '菜单名称直观、易懂，与产品需求文档(PRD)中的核心概念保持一致，降低用户理解成本。',
    icon: ShieldCheck,
  },
];

const roleBasedDashboards = {
    '质检主管': {
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: '为管理者提供团队概览和风险洞察的入口。',
      children: [
        { name: '团队服务质量概览', description: '展示团队平均分、合格率、违规率等核心效能指标。' },
        { name: '高风险预警', description: '识别并上报情绪激动、严重违规等高风险会话。' },
        { name: '待办事项', description: '聚合所有待处理任务，如复核任务抽检、申诉复核，一键进入工作台。' },
      ],
    },
    '质检员': {
      icon: User,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: '为质检员提供高效处理日常任务的快捷入口。',
      children: [
        { name: '我的待办任务', description: '展示所有待复核、待抽检的任务列表，是主要的工作入口。' },
        { name: '个人复核统计', description: '展示个人的复核数量、准确率、申诉率等工作指标。' },
        { name: '最新系统公告', description: '查看最新的规则变更、系统更新等通知。' },
      ],
    },
    '一线坐席': {
      icon: Headset,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: '为一线坐席提供查看个人质检结果和发起申诉的通道。',
      children: [
        { name: '我的质检报告', description: '查看个人历史质检得分、被扣分录音和具体违规项。' },
        { name: '发起申诉', description: '对有异议的质检结果发起申诉，并跟踪处理进度。' },
        { name: '优秀案例学习', description: '查看被评为优秀服务案例的录音，用于学习提升。' },
      ],
    }
};

const navTree = [
  {
    icon: Search,
    title: '质检工作台 (QA Workbench)',
    description: '质检员和复核人员的核心工作区域，聚焦于"处理具体任务"。',
    children: [
        { name: '我的复核任务', description: '系统或主管分配的质检复核列表，是质检员的核心工作区。' },
        { name: '实时预警中心', description: '需要人工介入的实时预警列表，如客户情绪激动、严重违规等。' },
        { name: '申诉处理', description: '处理一线客服对质检结果提出的申诉，形成管理闭环。' },
    ],
  },
  {
    icon: Settings,
    title: '质检管理 (QA Management)',
    description: '业务主管和质检管理者的配置与管理中心，聚焦于"定义规则和管理过程"。',
    children: [
        { name: '质检任务管理', description: '创建、监控和管理离线的批量质检任务。' },
        { name: '质检方案配置', description: '组合多个质检规则，形成可用于任务的完整评分方案。' },
        { name: '质检规则库', description: '创建和管理所有独立的质检规则，是质检方案的基础。' },
        { name: '知识库管理', description: '管理业务词库、标准话术等，为规则配置提供支持。' },
        { name: '复核策略配置', description: '定义质检结果的自动分配和流转规则。' },
    ],
  },
  {
    icon: BarChart3,
    title: '数据洞察 (Data Insights)',
    description: '为各级管理者提供数据分析与决策支持，聚焦于"发现问题与趋势"。',
    children: [
        { name: '全景数据看板', description: '从服务质量、团队效能、客户声音等多维度分析质检数据。' },
        { name: '报告中心', description: '创建、订阅和分发定期的质检分析报告。' },
        { name: '质检明细查询', description: '按多种条件检索具体的通话、会话记录及其质检结果。' },
        { name: '多维对比分析', description: '对不同团队、不同时间周期的质检表现进行对比。' },
    ],
  },
  {
    icon: Sprout,
    title: '员工成长 (Employee Growth)',
    description: '连接质检结果与员工赋能，聚焦于"辅导与提升"。',
    children: [
        { name: '个人成长档案', description: '一线客服查看自己的质检历史、得分趋势和能力雷达图。' },
        { name: '团队成长中心', description: '主管查看团队成员的成长档案，进行辅导和管理。' },
        { name: '智能辅导', description: '系统基于质检结果，自动为员工生成个性化的改进建议。' },
        { name: '培训管理', description: '基于质检发现的短板，创建和指派培训任务。' },
    ],
  },
  {
    icon: HardHat,
    title: '系统管理 (System Administration)',
    description: 'IT管理员和系统负责人的后台管理模块。',
    children: [
        { name: '用户管理', description: '管理系统内的所有用户账号。' },
        { name: '角色与权限', description: '定义不同角色（如质检员、主管）的功能和数据权限。' },
        { name: '数据接入管理', description: '配置和管理来自不同业务系统的数据源。' },
        { name: '系统配置', description: '配置通知、基础参数等全局性设置。' },
        { name: '操作审计日志', description: '记录系统内的所有关键操作，用于安全审计。' },
    ],
  },
];

export const SideMenuDesignPage: React.FC = () => {
  const [activeRole, setActiveRole] = useState<keyof typeof roleBasedDashboards>('质检主管');
  
  const dashboardData = roleBasedDashboards[activeRole];

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="导航菜单设计蓝图"
        description="一套面向角色、任务导向的导航系统，旨在提升质检工作的效率与体验。"
        badge="系统设计"
        tag="规划"
      />
      <main className="p-6 md:p-10 max-w-7xl mx-auto">

        {/* Design Principles */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">核心设计原则</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {designPrinciples.map(principle => (
              <div key={principle.title} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 text-center">
                <div className="flex justify-center items-center mb-4">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <principle.icon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">{principle.title}</h3>
                <p className="text-sm text-gray-600">{principle.description}</p>
              </div>
            ))}
          </div>
        </motion.section>

        {/* Navigation Tree */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">导航菜单树形结构</h2>
          </div>
          

          <div className="bg-white rounded-xl shadow-lg border border-gray-200/80 p-6 md:p-8">
            <div className="space-y-6">
              {/* Dynamic Dashboard Section */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeRole}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-start">
                      <dashboardData.icon className={`w-7 h-7 ${dashboardData.color} mr-4 mt-1 flex-shrink-0`} />
                      <div>
                        <h3 className="text-xl font-bold text-gray-800">首页 (Dashboard)</h3>
                        <p className="text-sm text-gray-500 mt-1">{dashboardData.description}</p>
                      </div>
                    </div>
                    {/* Role switcher */}
                    <div className="flex p-1 bg-gray-200 rounded-lg">
                      {Object.keys(roleBasedDashboards).map(role => (
                        <button
                          key={role}
                          onClick={() => setActiveRole(role as keyof typeof roleBasedDashboards)}
                          className={`px-4 py-2 text-sm font-semibold rounded-md transition-colors ${
                            activeRole === role ? 'bg-white text-gray-800 shadow' : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {role} 视角
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4 pl-6 md:pl-10 border-l-2 border-gray-200 ml-3">
                    <div className="space-y-4">
                      {dashboardData.children.map(child => (
                        <div key={child.name} className="flex items-start">
                          <CornerDownRight className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <span className="text-gray-800 font-medium">{child.name}</span>
                            <p className="text-sm text-gray-500 mt-0.5">{child.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>

              <hr className="border-gray-200" />

              {navTree.map(category => (
                <div key={category.title}>
                  {/* Category Root */}
                  <div className="flex items-start">
                    <category.icon className="w-6 h-6 text-gray-700 mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-xl font-bold text-gray-800">{category.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">{category.description}</p>
                    </div>
                  </div>
                  {/* Children */}
                  <div className="mt-4 pl-6 md:pl-10 border-l-2 border-gray-200 ml-3">
                    <div className="space-y-4">
                      {category.children.map(child => (
                        <div key={child.name} className="flex items-start">
                          <CornerDownRight className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <span className="text-gray-800 font-medium">{child.name}</span>
                            <p className="text-sm text-gray-500 mt-0.5">{child.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.section>

      </main>
    </div>
  );
};

export default SideMenuDesignPage; 