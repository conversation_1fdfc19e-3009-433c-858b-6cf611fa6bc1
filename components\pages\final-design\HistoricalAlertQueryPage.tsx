import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Alert, Tag, Tooltip } from 'antd';
import { History, Search, ChevronsUpDown, Check, Download, AlertTriangle, <PERSON>Che<PERSON>, Timer, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import { MetricCard } from './common/MetricCard';
import { AlertDetailDrawer, AlertDetail, FollowUpTaskInfo } from './components/AlertDetailDrawer';


interface HistoricalAlert {
    id: string;
    triggerTime: string;
    ruleName: string;
    level: 'critical' | 'high' | 'medium' | 'low';
    agent: { name: string; team: string };
    callId: string;
    disposition: 'ignored' | 'task_created';
    handler: string;
    handleTime: string;
    taskStatus?: 'completed' | 'pending' | 'overdue';
}

const mockData: HistoricalAlert[] = [
    { id: 'HA-001', triggerTime: '2024-06-25 10:30:15', ruleName: '客户提及"投诉"', level: 'critical', agent: { name: '张三', team: 'A组' }, callId: 'CALL-20240625-001', disposition: 'task_created', handler: '王主管', handleTime: '2024-06-25 10:32:00', taskStatus: 'completed' },
    { id: 'HA-002', triggerTime: '2024-06-24 14:05:40', ruleName: '客户情绪激动检测', level: 'high', agent: { name: '李四', team: 'B组' }, callId: 'CALL-20240624-002', disposition: 'ignored', handler: '陈班长', handleTime: '2024-06-24 14:06:10' },
    { id: 'HA-003', triggerTime: '2024-06-23 09:12:33', ruleName: '通话静音超时', level: 'medium', agent: { name: '王五', team: 'A组' }, callId: 'CALL-20240623-003', disposition: 'task_created', handler: '王主管', handleTime: '2024-06-23 09:15:00', taskStatus: 'pending' },
    { id: 'HA-004', triggerTime: '2024-06-22 18:45:01', ruleName: '坐席未主动核身', level: 'low', agent: { name: '赵六', team: 'C组' }, callId: 'CALL-20240622-004', disposition: 'ignored', handler: '陈班长', handleTime: '2024-06-22 18:45:30' },
    { id: 'HA-005', triggerTime: '2024-06-21 11:20:55', ruleName: '客户提及"投诉"', level: 'high', agent: { name: '孙七', team: 'D组' }, callId: 'CALL-20240621-005', disposition: 'task_created', handler: '王主管', handleTime: '2024-06-21 11:23:00', taskStatus: 'overdue' },
];

const levelMap = {
    critical: { text: '严重', color: 'red' },
    high: { text: '高', color: 'orange' },
    medium: { text: '中', color: 'yellow' },
    low: { text: '低', color: 'blue' },
};

const dispositionMap = {
    ignored: { text: '被忽略 (已读)', color: 'default' },
    task_created: { text: '已创建跟进任务', color: 'purple' },
};

const taskStatusMap = {
    completed: { text: '已完成', color: 'green' },
    pending: { text: '处理中', color: 'blue' },
    overdue: { text: '已逾期', color: 'red' },
};

const PAGE_SIZE = 10;

const HistoricalAlertQueryPage: React.FC = () => {
    const [activeTab, setActiveTab] = useState<'ignored' | 'completed' | 'pending' | 'overdue'>('ignored');
    const [filters, setFilters] = useState<Record<string, any>>({});
    const [currentPage, setCurrentPage] = useState(1);
    const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
    const [selectedAlert, setSelectedAlert] = useState<AlertDetail | null>(null);

    const handleSearch = () => {
        // In a real app, this would trigger a data refetch
        setCurrentPage(1);
    };

    const handleReset = () => {
        setFilters({});
        setCurrentPage(1);
    };

    const handleTabChange = (tab: 'ignored' | 'completed' | 'pending' | 'overdue') => {
        setActiveTab(tab);
        setCurrentPage(1);
    };

    const handleViewDetails = (record: HistoricalAlert) => {
        // Map HistoricalAlert to AlertDetail, creating more detailed mock data as needed
        const alertDetail: AlertDetail = {
            ...record,
            agent: { ...record.agent, id: `AGENT-${record.agent.name}`},
            customer: { phone: '138****1234' }, // Mock customer phone
            ruleDescription: `这是关于 "${record.ruleName}" 规则的详细描述和触发条件说明。`,
            contextSnippet: [
                { speaker: 'customer', text: '...之前几次电话都没解决我的问题。', timestamp: 15 },
                { speaker: 'agent', text: '非常抱歉给您带来不便，王女士，我们这次一定...', timestamp: 25 },
                { speaker: 'customer', text: '你们每次都这么说！我就是要投诉！', timestamp: 35, isHighlight: true },
                { speaker: 'agent', text: '好的女士，我理解您的心情，请您放心，您的问题我已记录...', timestamp: 48 },
                { speaker: 'customer', text: '记录有什么用？要给我一个明确的解决方案！', timestamp: 55 },
            ],
            disposition: record.disposition, // Already in HistoricalAlert
            taskInfo: record.taskStatus ? {
                id: `T-${record.id}`,
                title: `跟进任务 for ${record.ruleName}`,
                status: record.taskStatus,
                assignee: record.handler,
                dueDate: '2024-07-01'
            } : undefined,
        };
        setSelectedAlert(alertDetail);
        setIsDetailDrawerVisible(true);
    };

    const filterFields: FilterField[] = [
        { key: 'timeRange', label: '时间范围', type: 'dateRange' },
        { key: 'ruleName', label: '触发规则', type: 'text', placeholder: '按规则名称搜索' },
        { key: 'level', label: '预警等级', type: 'select', options: Object.entries(levelMap).map(([k, v]) => ({ value: k, label: v.text })) },
        { key: 'agent', label: '坐席/班组', type: 'text', placeholder: '按人员或班组搜索' },
        { key: 'context', label: '关键词搜索', type: 'text', placeholder: '在预警内容中搜索' },
    ];

    const filteredData = useMemo(() => {
        return mockData.filter(alert => {
            // 按tab过滤状态
            if (activeTab === 'ignored' && alert.disposition !== 'ignored') return false;
            if (activeTab === 'completed' && alert.taskStatus !== 'completed') return false;
            if (activeTab === 'pending' && alert.taskStatus !== 'pending') return false;
            if (activeTab === 'overdue' && alert.taskStatus !== 'overdue') return false;

            // 其他筛选逻辑
            if (filters.ruleName && !alert.ruleName.includes(filters.ruleName)) return false;
            if (filters.level && alert.level !== filters.level) return false;
            if (filters.agent && !`${alert.agent.name} ${alert.agent.team}`.includes(filters.agent)) return false;
            
            return true;
        });
    }, [activeTab, filters]);

    const paginatedData = useMemo(() => {
        // Dummy filtering logic
        const startIndex = (currentPage - 1) * PAGE_SIZE;
        return filteredData.slice(startIndex, startIndex + PAGE_SIZE);
    }, [filteredData, currentPage]);

    const ignoredCount = useMemo(() => mockData.filter(alert => alert.disposition === 'ignored').length, []);
    const completedCount = useMemo(() => mockData.filter(alert => alert.taskStatus === 'completed').length, []);
    const pendingCount = useMemo(() => mockData.filter(alert => alert.taskStatus === 'pending').length, []);
    const overdueCount = useMemo(() => mockData.filter(alert => alert.taskStatus === 'overdue').length, []);

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="历史预警查询"
                subtitle="查询和分析所有已归档的历史预警记录"
                icon={History}
                actions={[{
                    label: '导出数据',
                    variant: 'outline',
                    icon: Download,
                    onClick: () => alert('导出功能待实现'),
                }]}
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    <UnifiedSearchFilter fields={filterFields} filters={filters} onFiltersChange={setFilters} onSearch={handleSearch} onReset={handleReset} />
                    
                    <div className="mt-6">
                        <div className="border-b border-gray-200">
                            <div className="flex">
                                <button
                                    onClick={() => handleTabChange('ignored')}
                                    className={`px-6 py-3 font-medium text-sm border-b-2 ${
                                        activeTab === 'ignored'
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    被忽略 ({ignoredCount})
                                </button>
                                <button
                                    onClick={() => handleTabChange('pending')}
                                    className={`px-6 py-3 font-medium text-sm border-b-2 ${
                                        activeTab === 'pending'
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    处理中 ({pendingCount})
                                </button>
                                <button
                                    onClick={() => handleTabChange('overdue')}
                                    className={`px-6 py-3 font-medium text-sm border-b-2 ${
                                        activeTab === 'overdue'
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    已逾期 ({overdueCount})
                                </button>
                                <button
                                    onClick={() => handleTabChange('completed')}
                                    className={`px-6 py-3 font-medium text-sm border-b-2 ${
                                        activeTab === 'completed'
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    已完成 ({completedCount})
                                </button>
                            </div>
                        </div>

                        <div className="overflow-x-auto mt-4">
                            <table className="w-full text-sm text-left text-gray-600">
                                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-3">序号</th>
                                        <th className="px-4 py-3">预警ID</th>
                                        <th className="px-6 py-3">触发时间</th>
                                        <th className="px-6 py-3">触发规则</th>
                                        <th className="px-6 py-3">预警等级</th>
                                        <th className="px-6 py-3">关联坐席/班组</th>
                                        {activeTab !== 'ignored' && (
                                            <th className="px-6 py-3">处理人</th>
                                        )}
                                        <th className="px-6 py-3 text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedData.map((alert, index) => (
                                        <tr key={alert.id} className="bg-white border-b hover:bg-gray-50">
                                            <td className="px-4 py-4 text-gray-700">
                                                {(currentPage - 1) * PAGE_SIZE + index + 1}
                                            </td>
                                            <td className="px-4 py-4 font-medium text-gray-800">{alert.id}</td>
                                            <td className="px-6 py-4">{alert.triggerTime}</td>
                                            <td className="px-6 py-4">{alert.ruleName}</td>
                                            <td className="px-6 py-4">
                                                <Tag color={levelMap[alert.level].color}>{levelMap[alert.level].text}</Tag>
                                            </td>
                                            <td className="px-6 py-4">{`${alert.agent.name} / ${alert.agent.team}`}</td>
                                            {activeTab !== 'ignored' && (
                                                <td className="px-6 py-4">{alert.handler}</td>
                                            )}
                                            <td className="px-6 py-4 text-center">
                                                <Tooltip title="查看详情">
                                                    <button onClick={() => handleViewDetails(alert)} className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors inline-block">
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                </Tooltip>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        <UnifiedPagination
                            current={currentPage}
                            total={filteredData.length}
                            pageSize={PAGE_SIZE}
                            onChange={setCurrentPage}
                        />
                    </div>
                </motion.div>
            </main>

            <AlertDetailDrawer
                alert={selectedAlert}
                visible={isDetailDrawerVisible}
                onClose={() => setIsDetailDrawerVisible(false)}
                isReadOnly={true}
            />
        </div>
    );
};

export default HistoricalAlertQueryPage; 