/**
 * 统一的图表主题配置
 * 为所有图表提供一致的颜色、样式和动画配置
 */

// 主要颜色调色板
export const chartColors = {
  primary: {
    blue: '#3b82f6',
    indigo: '#6366f1',
    purple: '#8b5cf6',
    pink: '#ec4899',
  },
  secondary: {
    emerald: '#10b981',
    teal: '#14b8a6',
    cyan: '#06b6d4',
    sky: '#0ea5e9',
  },
  accent: {
    orange: '#f97316',
    amber: '#f59e0b',
    yellow: '#eab308',
    lime: '#84cc16',
  },
  neutral: {
    slate: '#64748b',
    gray: '#6b7280',
    zinc: '#71717a',
    stone: '#78716c',
  },
  status: {
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  }
};

// 渐变色配置
export const chartGradients = {
  blue: ['#3b82f6', '#1d4ed8'],
  emerald: ['#10b981', '#047857'],
  purple: ['#8b5cf6', '#7c3aed'],
  orange: ['#f97316', '#ea580c'],
  pink: ['#ec4899', '#db2777'],
  teal: ['#14b8a6', '#0f766e'],
  indigo: ['#6366f1', '#4f46e5'],
  amber: ['#f59e0b', '#d97706'],
};

// 图表通用样式配置
export const chartTheme = {
  // 网格线样式
  grid: {
    stroke: '#f1f5f9',
    strokeDasharray: '3 3',
    strokeWidth: 1,
  },
  
  // 坐标轴样式
  axis: {
    tick: {
      fontSize: 12,
      fill: '#64748b',
      fontWeight: 500,
    },
    tickLine: {
      stroke: '#e2e8f0',
      strokeWidth: 1,
    },
    axisLine: {
      stroke: '#e2e8f0',
      strokeWidth: 1,
    },
    label: {
      fontSize: 14,
      fill: '#374151',
      fontWeight: 600,
    },
  },
  
  // 工具提示样式
  tooltip: {
    contentStyle: {
      backgroundColor: 'rgba(255, 255, 255, 0.98)',
      border: '1px solid #e2e8f0',
      borderRadius: '12px',
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      backdropFilter: 'blur(12px)',
      padding: '12px 16px',
      fontSize: '14px',
      fontWeight: 500,
      color: '#374151',
    },
    cursor: {
      stroke: '#94a3b8',
      strokeWidth: 1,
      strokeDasharray: '5 5',
    },
  },
  
  // 图例样式
  legend: {
    wrapperStyle: {
      paddingTop: '24px',
      fontSize: '14px',
      fontWeight: 500,
      color: '#374151',
    },
    iconType: 'circle' as const,
  },
  
  // 动画配置
  animation: {
    duration: 800,
    easing: 'ease-out',
    delay: 0,
  },
  
  // 响应式断点
  responsive: {
    mobile: 480,
    tablet: 768,
    desktop: 1024,
  },
};

// 预定义的颜色组合
export const colorSchemes = {
  // 主要业务数据
  business: [
    chartColors.primary.blue,
    chartColors.secondary.emerald,
    chartColors.accent.orange,
    chartColors.primary.purple,
    chartColors.secondary.teal,
    chartColors.accent.amber,
  ],
  
  // 状态指示
  status: [
    chartColors.status.success,
    chartColors.status.warning,
    chartColors.status.error,
    chartColors.status.info,
  ],
  
  // 渐变色组合
  gradient: [
    'url(#blueGradient)',
    'url(#emeraldGradient)',
    'url(#purpleGradient)',
    'url(#orangeGradient)',
    'url(#pinkGradient)',
    'url(#tealGradient)',
  ],
  
  // 单色调色板（用于热力图等）
  monochrome: {
    blue: ['#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa', '#3b82f6', '#2563eb', '#1d4ed8'],
    emerald: ['#d1fae5', '#a7f3d0', '#6ee7b7', '#34d399', '#10b981', '#059669', '#047857'],
    purple: ['#e9d5ff', '#d8b4fe', '#c4b5fd', '#a78bfa', '#8b5cf6', '#7c3aed', '#6d28d9'],
    orange: ['#fed7aa', '#fdba74', '#fb923c', '#f97316', '#ea580c', '#dc2626', '#b91c1c'],
  },
};

// 图表类型特定配置
export const chartTypeConfigs = {
  bar: {
    radius: [4, 4, 0, 0],
    maxBarSize: 60,
    gap: 4,
  },
  line: {
    strokeWidth: 3,
    dot: {
      r: 5,
      strokeWidth: 2,
      fill: '#fff',
    },
    activeDot: {
      r: 7,
      strokeWidth: 2,
      fill: '#fff',
    },
  },
  area: {
    strokeWidth: 2,
    fillOpacity: 0.6,
    dot: {
      r: 4,
      strokeWidth: 2,
      fill: '#fff',
    },
  },
  pie: {
    innerRadius: 0,
    outerRadius: 80,
    paddingAngle: 2,
    cornerRadius: 4,
  },
  donut: {
    innerRadius: 40,
    outerRadius: 80,
    paddingAngle: 2,
    cornerRadius: 4,
  },
};

// 导出默认主题
export const defaultChartTheme = {
  colors: chartColors,
  gradients: chartGradients,
  theme: chartTheme,
  schemes: colorSchemes,
  configs: chartTypeConfigs,
};

export default defaultChartTheme;
