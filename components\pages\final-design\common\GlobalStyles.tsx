import { useEffect } from 'react';
import { METRIC_HELP_TOOLTIP_STYLES } from './MetricHelpButton';

/**
 * 全局样式定义
 */
const GLOBAL_STYLES = `
  /* 统一的指标帮助提示样式 */
  ${METRIC_HELP_TOOLTIP_STYLES}

  /* 统一的卡片悬停效果 */
  .unified-card-hover {
    transition: all 0.2s ease-in-out;
  }

  .unified-card-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* 统一的页面容器样式 */
  .unified-page-container {
    min-height: 100vh;
    background-color: #f9fafb;
  }

  .unified-content-area {
    padding: 1.5rem;
  }

  /* 统一的网格间距 */
  .unified-metric-grid {
    display: grid;
    gap: 1.5rem;
  }

  .unified-metric-grid-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .unified-metric-grid-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .unified-metric-grid-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .unified-metric-grid-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  /* 响应式设计 */
  @media (min-width: 768px) {
    .unified-metric-grid-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .unified-metric-grid-3 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .unified-metric-grid-4 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .unified-metric-grid-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
    
    .unified-metric-grid-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  /* 统一的内容分组间距 */
  .unified-section-spacing {
    margin-bottom: 1.5rem;
  }

  .unified-section-spacing:last-child {
    margin-bottom: 0;
  }

  /* 统一的通知优先级样式 */
  .notification-priority-high {
    border-left: 4px solid #ef4444;
    background-color: #fef2f2;
  }

  .notification-priority-medium {
    border-left: 4px solid #f59e0b;
    background-color: #fffbeb;
  }

  .notification-priority-low {
    border-left: 4px solid #10b981;
    background-color: #f0fdf4;
  }

  /* 统一的图表容器样式 */
  .unified-chart-container {
    width: 100%;
    height: 320px;
  }

  /* 统一的排名徽章样式 */
  .rank-badge-gold {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #d97706;
    font-weight: 700;
  }

  .rank-badge-silver {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #ea580c;
    font-weight: 700;
  }

  .rank-badge-normal {
    color: #6b7280;
  }
`;

/**
 * 全局样式注入组件
 * 在应用中注入统一的样式规范
 */
export const GlobalStyles: React.FC = () => {
  useEffect(() => {
    // 检查是否已经注入过样式
    const existingStyle = document.getElementById('unified-global-styles');
    if (!existingStyle) {
      const styleElement = document.createElement('style');
      styleElement.id = 'unified-global-styles';
      styleElement.textContent = GLOBAL_STYLES;
      document.head.appendChild(styleElement);
    }

    // 组件卸载时清理样式
    return () => {
      const styleElement = document.getElementById('unified-global-styles');
      if (styleElement) {
        document.head.removeChild(styleElement);
      }
    };
  }, []);

  return null; // 这个组件不渲染任何内容
};

export default GlobalStyles; 