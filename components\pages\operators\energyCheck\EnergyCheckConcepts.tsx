import React from 'react';

/**
 * 能量检测核心概念组件
 * @constructor
 */
const EnergyCheckConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">核心概念</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          音频质检专属
        </span>
      </div>

      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                录音文件转写成文本后，每句话会附带一个语音能量等级（1-10）。能量等级用于衡量音量大小，是对分贝值的加工处理。该算子通过分析语音能量等级，从而判断客服或客户在通话过程中是否有大的情绪波动。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">识别客户激动或愤怒情绪</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">监控客服语气是否过激</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">检测通话中是否存在吼叫</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">分析通话情绪波动趋势</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                  <li><strong>无需转文字</strong>：直接分析音频物理属性，速度快、成本低。</li>
                  <li><strong>情绪感知</strong>：能量值的变化可以间接反映情绪波动，辅助判断服务态度。</li>
                  <li><strong>场景适应性强</strong>：可用于检测多种声音事件，如拍桌子、吼叫等。</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 检测原理 */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🔧</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">检测原理</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                算子将音频信号按帧处理，计算每一帧的能量值。然后根据不同的检测方式进行判断：1) <strong>能量范围</strong>：判断句子的平均能量值是否在指定区间内；2) <strong>相邻句能量波动</strong>：比较相邻两句的平均能量差值是否超过阈值；3) <strong>最大能量跨度</strong>：计算一句内的最大能量值与最小能量值的差值。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnergyCheckConcepts; 