import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface UnifiedPaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  className?: string;
}

const UnifiedPagination: React.FC<UnifiedPaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  showTotal = true,
  className = ''
}) => {
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (current - 1) * pageSize + 1;
  const endIndex = Math.min(current * pageSize, total);

  const getPaginationItems = (totalPages: number, currentPage: number) => {
    const items = [];
    const maxVisible = 7; // 最多显示7个页码
    
    if (totalPages <= maxVisible) {
      // 如果总页数小于等于最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        items.push({ type: 'page', page: i });
      }
    } else {
      // 复杂分页逻辑
      if (currentPage <= 4) {
        // 当前页在前面
        for (let i = 1; i <= 5; i++) {
          items.push({ type: 'page', page: i });
        }
        items.push({ type: 'ellipsis' });
        items.push({ type: 'page', page: totalPages });
      } else if (currentPage >= totalPages - 3) {
        // 当前页在后面
        items.push({ type: 'page', page: 1 });
        items.push({ type: 'ellipsis' });
        for (let i = totalPages - 4; i <= totalPages; i++) {
          items.push({ type: 'page', page: i });
        }
      } else {
        // 当前页在中间
        items.push({ type: 'page', page: 1 });
        items.push({ type: 'ellipsis' });
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          items.push({ type: 'page', page: i });
        }
        items.push({ type: 'ellipsis' });
        items.push({ type: 'page', page: totalPages });
      }
    }
    
    return items;
  };

  if (total === 0) {
    return null;
  }

  return (
    <div className={`px-6 py-4 border-t border-gray-200 bg-gray-50 ${className}`}>
      <div className="flex items-center justify-between">
        {/* 左侧信息 */}
        <div className="flex items-center text-sm text-gray-600">
          {showTotal && (
            <>
              <span className="mr-2">共 {total} 条记录</span>
              <span className="text-gray-400">|</span>
              <span className="ml-2">
                第 {startIndex}-{endIndex} 条
              </span>
            </>
          )}
        </div>

        {/* 右侧分页控件 */}
        <div className="flex items-center space-x-1">
          {/* 上一页按钮 */}
          <button
            onClick={() => onChange(Math.max(1, current - 1))}
            disabled={current === 1}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            上一页
          </button>

          {/* 页码按钮 */}
          <div className="flex items-center">
            {getPaginationItems(totalPages, current).map((item: { type: string, page?: number }, index: number) => {
              if (item.type === 'page') {
                return (
                  <button
                    key={index}
                    onClick={() => onChange(item.page as number)}
                    className={`px-3 py-2 text-sm font-medium border-t border-b ${
                      current === item.page
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    {item.page}
                  </button>
                );
              } else {
                return (
                  <span
                    key={index}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300"
                  >
                    ...
                  </span>
                );
              }
            })}
          </div>

          {/* 下一页按钮 */}
          <button
            onClick={() => onChange(Math.min(totalPages, current + 1))}
            disabled={current === totalPages}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500"
          >
            下一页
            <ChevronRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default UnifiedPagination;