import React from 'react';
import type { SentenceCountConfigType, TestResultType } from './SentenceCountCheckPage';

/**
 * @typedef {object} SentenceCountTesterProps
 * @property {SentenceCountConfigType} config
 * @property {string} testText
 * @property {React.Dispatch<React.SetStateAction<string>>} setTestText
 * @property {TestResultType | null} testResult
 * @property {React.Dispatch<React.SetStateAction<TestResultType | null>>} setTestResult
 */
interface SentenceCountTesterProps {
  config: SentenceCountConfigType;
  testText: string;
  setTestText: React.Dispatch<React.SetStateAction<string>>;
  testResult: TestResultType | null;
  setTestResult: React.Dispatch<React.SetStateAction<TestResultType | null>>;
}

/**
 * 对话语句数检测测试组件
 * @param {SentenceCountTesterProps} props
 * @returns {React.ReactElement}
 */
const SentenceCountTester: React.FC<SentenceCountTesterProps> = ({
  config,
  testText,
  setTestText,
  testResult,
  setTestResult,
}) => {
  const runTest = () => {
    const sentences = testText.split('\n').filter(s => s.trim());
    let targetSentences: string[] = [];

    // 根据检测角色筛选句子
    if (config.detectionRole === 'agent') {
      targetSentences = sentences.filter(s => s.startsWith('客服：'));
    } else if (config.detectionRole === 'customer') {
      targetSentences = sentences.filter(s => s.startsWith('客户：'));
    } else {
      targetSentences = sentences;
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    const actualCount = targetSentences.length;
    let matched = false;

    switch (config.operator) {
      case 'greater':
        matched = actualCount > config.count;
        break;
      case 'less':
        matched = actualCount < config.count;
        break;
      case 'equal':
        matched = actualCount === config.count;
        break;
    }

    const operatorText = {
      greater: '大于',
      less: '小于',
      equal: '等于',
    };

    setTestResult({
      matched,
      actualCount,
      details: `实际句数 ${actualCount}，要求${operatorText[config.operator]} ${config.count} 句。`,
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          即时验证
        </span>
      </div>

      <div className="space-y-6">
        <textarea
          rows={8}
          className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 resize-y"
          value={testText}
          onChange={(e) => setTestText(e.target.value)}
          placeholder="请输入需要测试的对话文本，每行一句话..."
        />

        <button
          onClick={runTest}
          disabled={!testText.trim()}
          className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300"
        >
          执行测试
        </button>

        {testResult && (
          <div className={`rounded-xl border-2 p-6 ${testResult.matched ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
            <div className="flex items-center space-x-4">
              <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${testResult.matched ? 'bg-green-100' : 'bg-red-100'}`}>
                <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                  {testResult.matched ? '✓' : '✗'}
                </span>
              </div>
              <div>
                <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                  {testResult.matched ? '规则命中' : '规则未命中'}
                </div>
                <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                  {testResult.details}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SentenceCountTester; 