### **6. 申诉分析报表 - 详细设计**

---

**版本:** 1.0
**设计者:** AI Senior Engineer
**日期:** 2024-06-30

---

#### **一、 核心目的与面向用户**

*   **面向用户:** 质检主管、质检经理。
*   **核心目的:** 
    1.  全面监控申诉流程的健康状况，包括申诉数量、处理效率和结果。
    2.  通过分析申诉数据，保障质检工作的公平性与一致性。
    3.  从申诉的焦点问题中，反向挖掘潜在的规则定义不清、质检标准不一或坐席培训不到位等深层次管理问题。

---

#### **二、 报表布局与组件设计**

采用标准的四区域布局：**A区（筛选器）**、**B区（核心指标）**、**C区（可视化分析）**、**D区（明细列表）**。

![申诉分析报表布局](https://i.imgur.com/example.png) <!-- 预留图 -->

---

#### **三、 各区域详细说明**

##### **A区：筛选器**

此区域提供全局的数据筛选能力，所有后续区域的数据均会根据此处的选择进行联动刷新。

*   **组件1：时间范围选择器 (DatePicker.RangePicker)**
    *   **功能:** 用户可以选择预设的时间段（如"近7天"、"上月"、"本季度"）或自定义起止日期。
    *   **默认值:** "上月"。

*   **组件2：申诉处理人选择器 (Select)**
    *   **功能:** 按处理申诉的质检员或主管进行筛选。支持多选。
    *   **默认值:** "全部"。

*   **组件3：质检员选择器 (Select)**
    *   **功能:** 按被申诉的质检员进行筛选。支持多选。
    *   **默认值:** "全部"。

*   **组件4：班组选择器 (Select)**
    *   **功能:** 按发起申诉的坐席所属班组进行筛选。支持多选。
    *   **默认值:** "全部"。

##### **B区：申诉数据概览**

此区域宏观展示申诉流程的核心效能指标。

*   **指标卡1：总申诉率**
    *   **计算:** (发起申诉的记录数 / 范围内的总质检记录数) * 100%。
    *   **目的:** 衡量坐席对质检结果的整体认可度。

*   **指标卡2：申诉成功率**
    *   **计算:** (申诉成功的记录数 / 总申诉记录数) * 100%。
    *   **目的:** 衡量申诉的有效性和质检误判的可能性。

*   **指标卡3：平均处理时长**
    *   **计算:** (处理所有申诉任务的总时长 / 总申诉任务数)。
    *   **目的:** 监控申诉处理的效率。单位可以是"小时"或"天"。

*   **指标卡4：待处理申诉**
    *   **计算:** 当前状态为"待处理"的申诉任务总数。
    *   **目的:** 实时监控积压情况。

##### **C区：可视化分析**

此区域通过多维图表深入分析申诉的模式、原因和相关方表现。

*   **图表1：申诉趋势分析**
    *   **图表类型:** 组合图 (Composed Chart)，包含柱状图和折线图。
    *   **目的:** 按时间展示申诉数量与成功率的变化关系。
    *   **X轴:** 时间（按日/周聚合）。
    *   **Y轴1 (柱状):** `申诉总数`、`申诉成功数`。
    *   **Y轴2 (折线):** `申诉成功率`。
    *   **交互:** 鼠标悬浮可查看具体数值。

*   **图表2：申诉原因分析**
    *   **图表类型:** 词云图 (Word Cloud)。
    *   **目的:** 对坐席填写的"申诉理由"文本进行分析，快速识别出争议的关键词和焦点问题（如"标准理解不一致"、"客户原话"、"系统误判"等）。
    *   **交互:** 词语大小代表频率高低。

*   **图表3：申诉处理人通过率对比**
    *   **图表类型:** 水平分组条形图 (Horizontal Grouped Bar Chart)。
    *   **目的:** 对比不同处理人在处理申诉时的判断尺度。
    *   **Y轴:** 申诉处理人姓名。
    *   **X轴:** 百分比。
    *   **数据系列:**
        1.  **维持原判率 (%)**
        2.  **申诉成功率 (%)**
    *   **分析:** 如果某处理人的申诉成功率显著高于或低于其他人，可能意味着其判断标准存在差异。

*   **图表4：质检员被申诉率排行**
    *   **图表类型:** 水平分组条形图。
    *   **目的:** 识别出被申诉频率或被申诉成功率异常的质检员。
    *   **Y轴:** 质检员姓名。
    *   **X轴:** 百分比。
    *   **数据系列:**
        1.  **被申诉率 (%)**: (该质检员被申诉的次数 / 该质检员的总质检量)
        2.  **（被申诉后）申诉成功率 (%)**: (针对该质检员的申诉中，成功的比例)
    *   **分析:** 高"被申诉率"+ 高"申诉成功率"的组合，是需要重点关注的信号。

##### **D区：明细列表**

*   **列表1：申诉记录明细**
    *   **目的:** 提供所有申诉记录的原始数据，供主管进行查询、追溯和审核。
    *   **表格列:**
        *   `通话ID`: 链接到通话详情页。
        *   `坐席姓名` / `班组`
        *   `质检员姓名`
        *   `申诉理由` (摘要)
        *   `申诉处理人`
        *   `处理结果`: (申诉成功, 维持原判, 申诉驳回)
        *   `分数变更`: (如 `85 -> 90`)
        *   `处理时长`
        *   `申诉日期`
        *   `操作`: 提供"查看详情"按钮，链接到该申诉的处理工单页面。
    *   **功能:** 支持按各列排序和筛选。 