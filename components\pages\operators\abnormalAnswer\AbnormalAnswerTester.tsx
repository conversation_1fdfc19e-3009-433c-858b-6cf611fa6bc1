import React from 'react';

/**
 * 非正常接听测试接口
 */
interface AbnormalAnswerTesterProps {
  config: {
    operator: '>' | '<' | '>=' | '<=' | '=';
    duration: number;
    role: 'any' | 'customer' | 'agent';
  };
  testText: string;
  setTestText: (text: string) => void;
  testResult: {
    matched: boolean;
    firstSentenceTime: number | null;
    firstSentenceRole: string;
    details: string;
  } | null;
  setTestResult: (result: any) => void;
  runTest: () => void;
}

/**
 * 非正常接听测试组件
 * @constructor
 */
const AbnormalAnswerTester: React.FC<AbnormalAnswerTesterProps> = ({
  testText,
  setTestText,
  testResult,
  setTestResult,
  runTest,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">🧪 实时测试</h2>
        <button
          onClick={() => {
            setTestText('[00:03] 客服：您好，请问有什么可以帮您？\n[00:05] 客户：你好，我想咨询个问题。');
            setTestResult(null);
          }}
          className="text-sm text-gray-500 hover:text-gray-700 underline"
        >
          重置示例
        </button>
      </div>

      <div className="space-y-6">
        <textarea
          rows={8}
          className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          value={testText}
          onChange={(e) => setTestText(e.target.value)}
          placeholder="请输入带时间戳的对话文本，每行一句...&#10;示例:&#10;[00:03] 客服：您好..."
        />
        <button
          onClick={runTest}
          disabled={!testText.trim()}
          className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300"
        >
          执行测试
        </button>

        {testResult && (
          <div className="mt-8 space-y-6">
            <div className={`p-6 rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-4">
                <div className={`text-2xl font-bold ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                  {testResult.matched ? '✓ 规则命中' : '✗ 规则未命中'}
                </div>
              </div>
              <div className={`mt-2 text-sm ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                {testResult.details}
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">检测详情</h4>
              <p className="text-sm text-gray-600">
                首句时间：{testResult.firstSentenceTime !== null ? `${testResult.firstSentenceTime}秒` : '未检测到'}
              </p>
              <p className="text-sm text-gray-600">
                首句角色：{testResult.firstSentenceRole || '未检测到'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AbnormalAnswerTester; 