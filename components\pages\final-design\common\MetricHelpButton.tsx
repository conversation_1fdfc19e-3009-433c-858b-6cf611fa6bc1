import React from 'react';
import { Tooltip } from 'antd';
import { HelpCircle } from 'lucide-react';

/**
 * 指标描述接口
 */
export interface MetricDescription {
  title: string;
  description: string;
  calculation: string;
}

/**
 * 指标帮助按钮属性接口
 */
interface MetricHelpButtonProps {
  /** 指标描述信息 */
  metric: MetricDescription;
  /** Tooltip 显示位置 */
  placement?: 'top' | 'topLeft' | 'topRight' | 'bottom' | 'bottomLeft' | 'bottomRight';
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 统一的指标帮助按钮组件
 * 用于在指标卡片中显示帮助信息的Tooltip
 */
export const MetricHelpButton: React.FC<MetricHelpButtonProps> = ({
  metric,
  placement = 'top',
  className = ''
}) => {
  const tooltipContent = (
    <div className="max-w-xs">
      <div className="font-semibold text-white mb-2">{metric.title}</div>
      <div className="text-gray-200 mb-3">{metric.description}</div>
      <div className="text-gray-300 text-sm">
        <strong>计算方式：</strong>
        <br />
        {metric.calculation}
      </div>
    </div>
  );

  return (
    <Tooltip 
      title={tooltipContent}
      placement={placement}
      overlayClassName="unified-metric-help-tooltip"
    >
      <HelpCircle className={`w-4 h-4 text-gray-400 hover:text-blue-500 cursor-help transition-colors ${className}`} />
    </Tooltip>
  );
};

export default MetricHelpButton;

// 统一的样式定义
export const METRIC_HELP_TOOLTIP_STYLES = `
  .unified-metric-help-tooltip .ant-tooltip-inner {
    background-color: #1f2937 !important;
    border-radius: 8px !important;
    padding: 12px !important;
    max-width: 320px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  }

  .unified-metric-help-tooltip .ant-tooltip-arrow::before {
    background-color: #1f2937 !important;
  }
`; 