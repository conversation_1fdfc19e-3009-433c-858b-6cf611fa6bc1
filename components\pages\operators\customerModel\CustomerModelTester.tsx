import React from 'react';
import { CustomerModelConfigType, TestResultType, DetectionType } from '../CustomerModelPage';

/**
 * @typedef {object} Props
 * @property {CustomerModelConfigType} config
 * @property {string} testText
 * @property {(text: string) => void} setTestText
 * @property {TestResultType | null} testResult
 * @property {(result: TestResultType | null) => void} setTestResult
 */
interface Props {
  config: CustomerModelConfigType;
  testText: string;
  setTestText: (text: string) => void;
  testResult: TestResultType | null;
  setTestResult: (result: TestResultType | null) => void;
}

const modelKeywords: Record<DetectionType, string[]> = {
  '投诉': ['投诉', '举报'],
  '升级问题': ['领导', '负责人', '升级'],
  '质疑服务': ['态度', '不专业', '什么玩意'],
  '制造舆情': ['曝光', '315', '记者', '报警'],
  '表扬': ['感谢', '太好了', '点赞', '服务真好'],
  '辱骂': ['滚', '混蛋', '垃圾'],
  '愤怒': ['生气', '不爽', '不满意', '气死我了'],
};

/**
 * 客户模型检测实时测试组件
 * @param {Props} props
 * @returns {React.ReactElement}
 */
export const CustomerModelTester: React.FC<Props> = ({ config, testText, setTestText, testResult, setTestResult }) => {
  const runTest = () => {
    if (!config.detectionType) {
      setTestResult({
        matched: false,
        matchedType: null,
        details: '测试失败：请先在"配置演示"中选择一个检测类型。',
      });
      return;
    }

    const customerSentences = testText
      .split('\n')
      .filter(line => line.startsWith('客户：'))
      .join(' ');

    const keywords = modelKeywords[config.detectionType];
    const isMatched = keywords.some(kw => customerSentences.includes(kw));

    setTestResult({
      matched: isMatched,
      matchedType: isMatched ? config.detectionType : null,
      details: isMatched 
        ? `规则命中 - 模型识别到客户表达了"${config.detectionType}"的意图。`
        : '规则未命中 - 未能从客户的对话中识别到相关意图。',
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <button
          onClick={() => {
            setTestText('客户：我对你们的服务非常不满意，我要投诉！\n客服：您好，请问发生了什么事？');
            setTestResult(null);
          }}
          className="text-sm text-gray-500 hover:text-gray-700 underline"
        >
          重置示例
        </button>
      </div>

      <div className="space-y-4">
        <textarea
          rows={6}
          className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          value={testText}
          onChange={(e) => setTestText(e.target.value)}
          placeholder="请输入需要测试的对话文本..."
        />
        <button
          onClick={runTest}
          disabled={!testText.trim()}
          className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300"
        >
          执行测试
        </button>
      </div>

      {testResult && (
        <div className="mt-6 border-t pt-4">
          <h3 className="text-lg font-semibold mb-2">测试结果</h3>
          <div className={`p-4 rounded-md ${testResult.matched ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
            <p className="font-bold">{testResult.matched ? '✅ 规则命中' : '❌ 规则未命中'}</p>
            <p className="text-sm">{testResult.details}</p>
          </div>
        </div>
      )}
    </div>
  );
}; 