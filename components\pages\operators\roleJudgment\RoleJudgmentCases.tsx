import React from 'react';

/**
 * 角色判断案例接口
 */
interface RoleJudgmentCasesProps {
  cases: {
    name: string;
    description: string;
    config: any;
    expectedResult: string;
  }[];
  loadCase: (caseConfig: any) => void;
}

/**
 * 角色判断案例库组件
 * @param cases 案例列表
 * @param loadCase 加载案例的函数
 * @constructor
 */
const RoleJudgmentCases: React.FC<RoleJudgmentCasesProps> = ({ cases, loadCase }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
          <span>📂</span>
          <span>案例库</span>
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          参考示例
        </span>
      </div>
      <div className="space-y-4 max-h-[600px] overflow-y-auto">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200 hover:border-blue-300 transition-colors">
            <div className="flex justify-between items-start">
              <div className="space-y-1">
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500">{c.description}</p>
                <p className="text-xs text-blue-500">预期结果：{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c.config)}
                className="flex-shrink-0 text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RoleJudgmentCases; 