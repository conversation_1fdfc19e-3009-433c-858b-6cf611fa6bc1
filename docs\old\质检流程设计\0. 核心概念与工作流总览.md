# 0. 核心概念与工作流总览

## 一、引言

本文档旨在为本智能质检系统的核心概念与工作流程提供一个统一、精确且最终的定义。它是对"实时预警"、"质检方案"、"复核策略"和"复核任务"之间关系的全面梳理，是所有后续详细设计和团队沟通的基石。

我们的目标是构建一个**由AI驱动、以复核为核心**的高效质检闭环，彻底取代传统的、低效的随机抽样模式。

## 二、四大核心概念的最终定义

| 概念 | 英文 | 一句话定位 | 比喻 | 核心目标 |
| :--- | :--- | :--- | :--- | :--- |
| **实时分析与预警** | Real-time Analysis & Alerting | **事中干预的"哨兵"** | ICU心电监护仪 | **干预 (Intervention)** |
| **质检方案** | QA Scheme | **AI与人共同遵守的"评分标准"** | 医院的《诊断手册》 | **评价 (Evaluation)** |
| **复核策略** | Review Strategy | **决定"哪些需要专家会诊"的"筛选规则"** | 主任医师的查房标准 | **筛选 (Selection)** |
| **复核任务** | Review Task | **需要人工处理的"具体病历"** | 一份待处理的会诊单 | **确认 (Confirmation)** |

### 1. 实时分析与预警 (Real-time Analysis)
- **是什么**：一个在通话**进行中**，对语音/文本流进行快速、浅层分析的引擎。
- **做什么**：它不追求全面，只负责匹配预设的高风险规则（如客户投诉、坐席辱骂），一旦命中，立即发出**警报(Alert)**。
- **为什么**：其唯一目的是进行**事中干预**，实时辅助坐席、提醒主管，在风险造成更大损失前将其扼制在萌芽状态。

### 2. 质检方案 (QA Scheme)
- **是什么**：一套定义了**如何评价**一次通话质量的完整规则集合。它包含了所有的评分项、评分标准、关键词、静音阈值等。
- **做什么**：
    -   它是**事后AI全量质检引擎**运行的"大脑"，AI会依据方案为100%的录音打上标签和分数。
    -   它也是**人工复核时**，质检员打分的依据和准绳。
- **为什么**：它是保证AI与人评价标准一致性的"法律"，是整个质检体系稳定性的基石。

### 3. 复核策略 (Review Strategy)
- **是什么**：一套用于**筛选**的规则，它作用于**AI全量质检后的数据资产**之上。
- **做什么**：它替代了传统的"随机抽样计划"，通过智能规则（如：筛选所有AI评分低于60分的、或所有命中了"客户投诉"标签的通话），精准地从海量数据中找出最值得人工审核的会话。
- **为什么**：它的目标是将宝贵的人力资源，从"大海捞针"转变为对"AI预筛出的高价值样本"进行精准打击，实现效率最大化。它的产出是**复核任务**。

### 4. 复核任务 (Review Task)
- **是什么**：一个需要人工处理的、具体的**工作项 (To-do Item)**。
- **做什么**：质检员在"我的复核任务"列表中处理这些任务，听录音、看文本，并依据统一的**质检方案**，给出最终的评分和结论。
- **为什么**：它是连接AI分析和人工智慧的桥梁，是整个闭环中，人类专家发挥最终决策作用的环节。

## 三、系统总工作流程图 (The Big Picture)

这四大概念共同构成了一个从干预、分析、筛选到最终确认的完整闭环。

```mermaid
graph TD
    subgraph "A. 通话生命周期"
        A1["实时会话 Live Conversation"]
    end
    
    subgraph "B. AI处理层 (Machine)"
        A1 -- "语音/文本流" --> B1{"实时分析引擎<br/>(事中干预)"}
        A1 -- "通话结束" --> A2["完整录音"]
        A2 -- "100%进入队列" --> B2{"AI全量质检引擎<br/>(事后评价)"}
    end

    subgraph "C. 规则与策略定义层 (Human Setup)"
        C1["质检方案 QA Scheme<br/>(评价标准)"]
        C2["复核策略 Review Strategy<br/>(筛选规则)"]
    end
    
    B2 -- "调用" --> C1
    B2 -- "生成" --> D1[("AI分析数据库<br/>100%覆盖的数据资产")]

    subgraph "D. 人工处理层 (Human Action)"
        B1 -- "触发高危事件" --> D2{"生成<br/>高优先级复核任务"}
        D1 -- "被筛选" --> C2
        C2 -- "生成" --> D3{"生成<br/>常规复核任务"}
        D2 & D3 --> D4[("统一复核任务池<br/>Unified Review Pool")]
        D4 -- "分配/领取" --> D5["人工复核<br/>(对照'质检方案'进行确认)"]
        D5 -- "产出" --> D6["最终质检报告"]
    end
    
    style C1 fill:#fff2cc,stroke:#ffd966
    style C2 fill:#d9ead3,stroke:#93c47d
```

### 流程解读：
1.  **通话开始**：`实时分析引擎` 开始工作，它的目标是**干预**。
2.  **通话结束**：`AI全量质检引擎` 接管，它调用 `质检方案` 作为评价标准，对100%的录音进行深度分析，其目标是**评价**，产出是 `AI分析数据库`。
3.  **任务生成**：
    -   在通话中，`实时分析引擎` 可能直接触发一个**高优先级复核任务**。
    -   通话后，`复核策略` 会对 `AI分析数据库` 进行智能**筛选**，生成**常规复核任务**。
4.  **人工处理**：所有任务汇入 `统一复核任务池`，等待质检员进行最终的**确认**，产出 `最终质检报告`。

## 四、概念的演进：从"计划"到"策略"

为了构建更智能的系统，我们对传统概念进行了升级：

-   **`质检计划` (QA Plan) 演进为 `复核策略` (Review Strategy)**
    -   **旧的"计划"**：核心是**随机抽样**（如：每周抽5%）。它盲目、低效，且无法保证覆盖关键问题。
    -   **新的"策略"**：核心是**智能筛选**（如：筛选所有AI判定辱骂的会话）。它基于AI对全量数据的分析，精准、高效，将人力聚焦于最有价值的样本。

-   **`质检任务` (QA Task) 明确为 `复核任务` (Review Task)**
    -   我们统一使用"复核任务"是为了在系统内部强调一个核心思想：**所有的人工审核，都是对AI初步分析结果的复核与确认**，而不是从零开始的、探索性的检查。这定义了人机协作的边界与关系。

## 五、结论

通过精确定义 **实时分析（干预）**、**质检方案（标准）**、**复核策略（筛选）** 和 **复核任务（确认）**，我们构建了一套逻辑清晰、职责分明、可扩展性强的智能质检工作流。这个流程不仅提升了质检的效率和准确性，更重要的是，它将人力从重复性的劳动中解放出来，使其真正专注于专家级的判断与决策。 