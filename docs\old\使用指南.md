## 使用指南

阿里云智能质检是一款[SaaS](https://www.aliyun.com/getting-started/what-is/what-is-saas)产品，网页登录；支持对**文本、录音**文件发起**实时、离线**质检。

接下来的内容，将基于**质检员**视角介绍如何创建完整的质检任务。

## 质检规则配置

首先，登录产品后台，将业务质检要求配置成质检规则，再组装成质检方案。产品全面提供语音检测和语义检测，规则检测和AI检测等15+检测算子，比如关键词检测、静音检测、情绪检测，以满足不同业务场景。举个例子：

某金融企业的销售员在服务过程中会出现过度承诺的情况，如：无风险、稳赚不赔、全额赔付、绝对安全等等。质检员可以在产品中，配置一条”敏感词检测“的规则，并添加相关关键词。系统基于配置好的规则，可以检测销售员是否违规，并定位到具体的违规内容。

基于达摩院先进的语音识别和自然语言理解技术，以及算法模型优化的效果，质检规则准确率和召回率可达90%+。另外，产品内置6个质检方案模板，包含电商、金融、地产、运营商、能源热力行业以及通用模板，含200+质检规则，以帮助企业快速冷启动。

[具体配置点此查看](https://help.aliyun.com/zh/sca/user-guide/quality-inspection-rules/)

## 质检任务配置

其次，将待测语料传输至系统发起质检任务。系统支持3种传输方式，包括数据集上传、对接阿里云呼叫中心、自有系统API上传。

-   **数据集上传**：将电脑的本地数据上传至后台一键发起质检任务，该方式适用于前期少量测试。[配置详情](https://help.aliyun.com/zh/sca/user-guide/dataset-quality-inspection-new-sca/)
    
-   **对接阿里云呼叫中心**：智能质检和阿里云云呼叫中心产品已做好数据打通，一键勾选即可推送语音流。[配置详情](https://help.aliyun.com/zh/sca/user-guide/quality-inspection-of-call-canter-new-sca/)
    
-   **自有系统API上传**：产品提供标准的API接口以对接客户自有系统的数据，该方式需要**开发人员**介入。[配置详情](https://help.aliyun.com/zh/sca/developer-reference/api-qualitycheck-2019-01-15-overview)。另外，系统已经为开发者封装了常见编程语言的SDK，开发者可通过[下载SDK](https://next.api.aliyun.com/api-tools/sdk/Qualitycheck?version=2019-01-15)直接调用本产品OpenAPI而无需关心技术细节。
    

## 质检结果复核

完成质检任务后，可手动/自动分配给质检员做复核校验；相比于手动分配 ，自动分配更加高效便捷。举个例子：

质检组长希望当质检文件满足呼入、通话时长3分钟、A客服、命中B规则这四个条件时，就推送给C质检员。只需要在后台的**复核管理**里，新建规则即可。除此之外，针对筛选条件，系统也支持创建自定义字段。

另外，产品提供管理员、质检员、坐席，三种角色的不同产品功能，支持一线客服和销售人员发起申诉，实现”质检-复核-申诉“业务闭环使用体验。

[具体配置点此查看](https://help.aliyun.com/zh/sca/user-guide/review-management-new-sca)

## 分析结果大盘

企业需要定期跟踪质检数据，如质检总会话数、违规数、违规规则以全面监控客服服务质量，有效掌控质检动态。

![image](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/0332370571/p976711.png)

智能质检的后台截图

产品提供**质检概况、服务质量分析、复核统计、申诉统计**四大统计模块，以查看详情数据。另外，后台数据支持免费下载，企业可筛选所需数据生成自定义报表做业务分析。

[具体配置点此查看](https://help.aliyun.com/zh/sca/user-guide/effect-statistics/)