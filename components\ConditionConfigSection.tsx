
import React from 'react';
import type { Condition } from '../types';
import { ConditionBlock } from './ConditionBlock';

interface ConditionConfigSectionProps {
  conditions: Condition[];
  onConditionChange: <K extends keyof Condition>(index: number, field: K, value: Condition[K]) => void;
  onAddKeyword: (conditionIndex: number, keyword: string) => void;
  onRemoveKeyword: (conditionIndex: number, keywordToRemove: string) => void;
  onRemoveCondition: (index: number) => void;
  onToggleCollapse: (index: number) => void;
}

export const ConditionConfigSection: React.FC<ConditionConfigSectionProps> = ({
  conditions,
  onConditionChange,
  onAddKeyword,
  onRemoveKeyword,
  onRemoveCondition,
  onToggleCollapse,
}) => {
  return (
    <div className="space-y-4">
      {conditions.map((condition, index) => (
        <ConditionBlock
          key={condition.id}
          condition={condition}
          conditionIndex={index}
          onChange={onConditionChange}
          onAddKeyword={onAddKeyword}
          onRemoveKeyword={onRemoveKeyword}
          onRemove={() => onRemoveCondition(index)}
          onToggleCollapse={() => onToggleCollapse(index)}
          isEven={index % 2 === 0}
        />
      ))}
    </div>
  );
};
