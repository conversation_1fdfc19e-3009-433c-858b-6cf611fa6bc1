import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
    Users,
    AlertTriangle,
    Download,
    RefreshCw,
    MessageSquareX,
    Eye,
    Clock,
    Percent,
    Calculator,
    Shield,
    Flag,
    User,
    Crown,
    ArrowUp,
    ArrowDown,
    Minus,
    TrendingUp,
    BarChart3,
    Search,
    ChevronDown
} from 'lucide-react';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { EnhancedKPICard } from './common/EnhancedKPICard';
import { EnhancedChartContainer } from './common/EnhancedChartContainer';
import { EnhancedRankingCard } from './common/EnhancedRankingCard';
import { EnhancedFilterSection } from './common/EnhancedFilterSection';
import { EnhancedSectionHeader } from './common/EnhancedSectionHeader';
import {
    ComposedChart,
    Line,
    Bar,
    XAxis,
    YA<PERSON>s,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    Legend,
    ResponsiveContainer,
    <PERSON><PERSON><PERSON>,
    Cell
} from 'recharts';



interface AppealTrendData {
    period: string;
    appealCount: number;
    appealSuccessRate: number;
}

interface HighAppealSuccessRule {
    ruleName: string;
    ruleType: string;
    appealCount: number;
    appealSuccessCount: number;
    appealSuccessRate: number;
    severity: 'low' | 'medium' | 'high';
    rank: number;
}

interface HighAppealRateTeam {
    teamName: string;
    totalQualityChecks: number;
    appealCount: number;
    appealRate: number;
    appealSuccessCount: number;
    appealSuccessRate: number;
    memberCount: number;
    rank: number;
}

interface HighAppealRateAgent {
    agentName: string;
    teamName: string;
    totalQualityChecks: number;
    appealCount: number;
    appealRate: number;
    appealSuccessCount: number;
    appealSuccessRate: number;
    rank: number;
}



/**
 * 申诉趋势分析图表组件
 */
const AppealTrendChart: React.FC<{
    data: AppealTrendData[];
}> = ({ data }) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-300"
        >
            <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl">
                    <TrendingUp className="w-5 h-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-slate-900">申诉趋势分析</h3>
            </div>
            
            <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={data}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis 
                            dataKey="period" 
                            tick={{ fontSize: 12 }}
                            tickLine={{ stroke: '#e0e0e0' }}
                        />
                        <YAxis 
                            yAxisId="left"
                            tick={{ fontSize: 12 }}
                            tickLine={{ stroke: '#e0e0e0' }}
                        />
                        <YAxis 
                            yAxisId="right" 
                            orientation="right"
                            tick={{ fontSize: 12 }}
                            tickLine={{ stroke: '#e0e0e0' }}
                            domain={[0, 100]}
                        />
                        <Tooltip
                            contentStyle={{
                                backgroundColor: 'white',
                                border: '1px solid #e0e0e0',
                                borderRadius: '8px',
                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                            formatter={(value: any, name: string) => {
                                if (name === '申诉数量') return [`${value}件`, name];
                                if (name === '申诉成功率') return [`${value}%`, name];
                                return [value, name];
                            }}
                        />
                        <Legend />
                        <Bar 
                            yAxisId="left"
                            dataKey="appealCount" 
                            fill="#3b82f6" 
                            name="申诉数量"
                            radius={[2, 2, 0, 0]}
                        />
                        <Line 
                            yAxisId="right"
                            type="monotone" 
                            dataKey="appealSuccessRate" 
                            stroke="#ef4444" 
                            strokeWidth={3}
                            name="申诉成功率"
                            dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                        />
                    </ComposedChart>
                </ResponsiveContainer>
            </div>
        </motion.div>
    );
};

/**
 * 高申诉成功率规则排行组件
 */
const HighAppealSuccessRuleRanking: React.FC<{
    data: HighAppealSuccessRule[];
}> = ({ data }) => {
    const getSeverityBadge = (severity: 'low' | 'medium' | 'high') => {
        const styles = {
            low: 'bg-green-100 text-green-800',
            medium: 'bg-yellow-100 text-yellow-800',
            high: 'bg-red-100 text-red-800'
        };
        const labels = {
            low: '轻度违规',
            medium: '中度违规',
            high: '严重违规'
        };
        
        return (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[severity]}`}>
                {labels[severity]}
            </span>
        );
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-300"
        >
            <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gradient-to-br from-red-100 to-rose-100 rounded-xl">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
                <div>
                    <h3 className="text-lg font-semibold text-slate-900">高申诉成功率规则排行</h3>
                    <p className="text-sm text-slate-600">指向AI最不准确或定义最模糊的规则</p>
                </div>
            </div>
            
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead>
                        <tr className="border-b border-slate-200">
                            <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">排名</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">规则名称</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">规则类型</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉次数</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉成功次数</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉成功率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {data.map((rule, index) => (
                            <tr key={index} className="border-b border-slate-100 hover:bg-slate-50/80 transition-colors">
                                <td className="py-3 px-4">
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold shadow-sm ${
                                        rule.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white' :
                                        rule.rank === 2 ? 'bg-gradient-to-br from-slate-400 to-slate-500 text-white' :
                                        rule.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500 text-white' :
                                        'bg-gradient-to-br from-slate-300 to-slate-400 text-white'
                                    }`}>
                                        {rule.rank}
                                    </div>
                                </td>
                                <td className="py-3 px-4">
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm font-medium text-slate-900">{rule.ruleName}</span>
                                        {getSeverityBadge(rule.severity)}
                                    </div>
                                </td>
                                <td className="py-3 px-4 text-sm text-slate-700">{rule.ruleType}</td>
                                <td className="py-3 px-4 text-sm text-slate-700">{rule.appealCount}</td>
                                <td className="py-3 px-4 text-sm text-slate-700">{rule.appealSuccessCount}</td>
                                <td className="py-3 px-4">
                                    <span className="text-sm font-medium text-red-600">{rule.appealSuccessRate}%</span>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </motion.div>
    );
};

/**
 * 高申诉率团队/坐席排行组件
 */
const HighAppealRateRanking: React.FC<{
    teamData: HighAppealRateTeam[];
    agentData: HighAppealRateAgent[];
}> = ({ teamData, agentData }) => {
    const [activeTab, setActiveTab] = useState<'team' | 'agent'>('team');

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-300"
        >
            <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gradient-to-br from-orange-100 to-amber-100 rounded-xl">
                    <Users className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                    <h3 className="text-lg font-semibold text-slate-900">高申诉率团队/坐席排行</h3>
                    <p className="text-sm text-slate-600">反映团队文化或对质检结果的接受度</p>
                </div>
            </div>

            {/* Tab切换 */}
            <div className="flex items-center bg-slate-100 rounded-lg p-1 mb-6">
                <button
                    onClick={() => setActiveTab('team')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                        activeTab === 'team'
                            ? 'bg-white text-blue-600 shadow-sm transform scale-105'
                            : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                    }`}
                >
                    团队排行
                </button>
                <button
                    onClick={() => setActiveTab('agent')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                        activeTab === 'agent'
                            ? 'bg-white text-blue-600 shadow-sm transform scale-105'
                            : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                    }`}
                >
                    坐席排行
                </button>
            </div>
            
            <div className="overflow-x-auto">
                {activeTab === 'team' ? (
                    <table className="w-full">
                        <thead>
                            <tr className="border-b border-slate-200">
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">排名</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">团队名称</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">总质检数</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉次数</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉率</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉成功率</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">团队人数</th>
                            </tr>
                        </thead>
                        <tbody>
                            {teamData.map((team, index) => (
                                <tr key={index} className="border-b border-slate-100 hover:bg-slate-50/80 transition-colors">
                                    <td className="py-3 px-4">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold shadow-sm ${
                                            team.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white' :
                                            team.rank === 2 ? 'bg-gradient-to-br from-slate-400 to-slate-500 text-white' :
                                            team.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500 text-white' :
                                            'bg-gradient-to-br from-slate-300 to-slate-400 text-white'
                                        }`}>
                                            {team.rank}
                                        </div>
                                    </td>
                                    <td className="py-3 px-4 text-sm font-medium text-slate-900">{team.teamName}</td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{team.totalQualityChecks.toLocaleString()}</td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{team.appealCount}</td>
                                    <td className="py-3 px-4">
                                        <span className="text-sm font-medium text-red-600">{team.appealRate}%</span>
                                    </td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{team.appealSuccessRate}%</td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{team.memberCount}人</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                ) : (
                    <table className="w-full">
                        <thead>
                            <tr className="border-b border-slate-200">
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">排名</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">坐席姓名</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">所属团队</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">总质检数</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉次数</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉率</th>
                                <th className="text-left py-3 px-4 text-sm font-medium text-slate-600">申诉成功率</th>
                            </tr>
                        </thead>
                        <tbody>
                            {agentData.map((agent, index) => (
                                <tr key={index} className="border-b border-slate-100 hover:bg-slate-50/80 transition-colors">
                                    <td className="py-3 px-4">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold shadow-sm ${
                                            agent.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white' :
                                            agent.rank === 2 ? 'bg-gradient-to-br from-slate-400 to-slate-500 text-white' :
                                            agent.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500 text-white' :
                                            'bg-gradient-to-br from-slate-300 to-slate-400 text-white'
                                        }`}>
                                            {agent.rank}
                                        </div>
                                    </td>
                                    <td className="py-3 px-4 text-sm font-medium text-slate-900">{agent.agentName}</td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{agent.teamName}</td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{agent.totalQualityChecks.toLocaleString()}</td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{agent.appealCount}</td>
                                    <td className="py-3 px-4">
                                        <span className="text-sm font-medium text-red-600">{agent.appealRate}%</span>
                                    </td>
                                    <td className="py-3 px-4 text-sm text-slate-700">{agent.appealSuccessRate}%</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
        </motion.div>
    );
};

/**
 * 坐席申诉洞察报告页面
 */
export const FinalAppealInsightReportPage: React.FC = () => {
    const [timeRange, setTimeRange] = useState('本月');
    const [selectedTeam, setSelectedTeam] = useState('全部班组');
    const [selectedAgent, setSelectedAgent] = useState('全部坐席');

    // 坐席申诉洞察报告页面设计说明 (业务角度)
    const designGuideContent = (
        <div className="space-y-4 text-slate-700 text-sm leading-relaxed">
            <h2 className="text-xl font-bold text-slate-800">页面：坐席申诉洞察报告 (FinalAppealInsightReportPage.tsx) - 业务设计说明</h2>

            <h3 className="text-lg font-semibold text-slate-700 mt-4">1. 核心定位与目标</h3>
            <p>
                坐席申诉洞察报告页面是一个专注于<b>坐席申诉数据</b>的专题分析报告。
                其核心目标是从坐席的"反对票"中<b>反向挖掘和洞察问题</b>。它不仅仅是一个统计申诉情况的报表，
                更是一个检验<b>质检公平性、规则合理性和AI准确性</b>的强大工具。
                高申诉率和高申诉成功率往往是系统或管理中存在问题的明确信号。
            </p>

            <h3 className="text-lg font-semibold text-slate-700 mt-4">2. 主要功能模块与内容</h3>

            <h4 className="text-md font-semibold text-slate-700 mt-3">a. 页面顶部区域</h4>
            <p>此区域提供页面的核心概览信息和全局操作入口：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>标题</b>: "坐席申诉洞察报告"，明确页面的主题和功能。</li>
                <li><b>副标题</b>: "监控申诉情况，保障质检公平性，并从申诉数据中反向挖掘有争议的AI规则或存在标准差异的复核环节"，阐述页面提供的核心业务价值。</li>
                <li><b>角色标识</b>: 使用带叉号的对话框图标 (<code className="bg-gray-100 px-1 py-0.5 rounded text-red-500">MessageSquareX</code>)，直观地表示对质检结果的异议和分析。</li>
                <li><b>核心操作</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>刷新数据</b>: 允许管理者获取最新的申诉数据，确保信息的实时性。</li>
                        <li><b>导出报表</b>: 提供将当前报表数据导出为可离线分析的格式，方便后续深入分析或汇报。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">b. 统一搜索筛选器</h4>
            <p>此模块提供灵活的数据筛选功能，允许管理者聚焦特定业务维度进行分析：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>目的</b>: 对申诉数据进行多维度筛选，以进行更深入的分析，定位问题的具体范围。</li>
                <li><b>筛选字段及业务含义</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>时间范围</b>: 定义报告的统计周期，便于观察申诉数据在不同时间段的变化和趋势。</li>
                        <li><b>班组/团队</b>: 可以查看特定班组或团队的申诉情况，识别是否存在团队层面的共性问题。</li>
                        <li><b>坐席</b>: 可以查看单个坐席的申诉记录和申诉率，用于个人绩效评估和针对性辅导。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">c. 申诉概览KPI指标卡片</h4>
            <p>此模块以直观的卡片形式展示了申诉工作的整体情况，旨在提供核心申诉数据的概览。所有指标的数据均为系统自动聚合，反映申诉环节的业务表现。指标内容、业务含义及计算实现方式如下：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>总申诉数</b>:
                    <p>业务含义：在选定时间范围内，坐席发起的申诉总数量。</p>
                    <p>计算/实现：统计选定时间周期内，系统接收到的所有申诉记录的总和。</p>
                </li>
                <li><b>申诉率</b>:
                    <p>业务含义：申诉数量占总质检量的比例，反映了坐席对质检结果的整体信服度。</p>
                    <p>计算/实现：<code>(总申诉数 / 总质检通话数) × 100%</code>。</p>
                </li>
                <li><b>申诉成功率</b>:
                    <p>业务含义：<b>核心诊断指标</b>，申诉成功数量占总申诉数量的比例。如果成功率过高，强烈暗示AI规则或人工复核标准可能存在严重问题。</p>
                    <p>计算/实现：<code>(申诉成功的通话数量 / 总申诉数) × 100%</code>。</p>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">d. 申诉趋势分析（组合图表）</h4>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>图表标题</b>: "申诉趋势分析"。</li>
                <li><b>内容</b>: 使用组合图表，在一个图表中同时展示两条趋势线：
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>申诉数量（柱状图）</b>: 展示每日或每周的申诉数量，直观反映申诉事件的发生频率。</li>
                        <li><b>申诉成功率（折线图）</b>: 展示同期的申诉成功率趋势，反映申诉判决的标准稳定性。</li>
                    </ul>
                </li>
                <li><b>业务价值与实现</b>: 管理者可以分析申诉量和成功率的波动关系。例如，如果某周申诉量和成功率同时飙升，这可能与新上线的一条有争议的规则、AI模型更新或特定事件有关，从而快速定位潜在问题。数据来源于系统按时间维度对申诉数量和申诉成功率的聚合统计。</li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">e. 问题定位分析（左右双卡片布局）</h4>
            <p>这是本报告最具洞察力的部分，直接回答了"为什么申诉"和"谁在申诉"这两个核心问题，旨在帮助管理者精准定位问题根源。</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>左侧卡片 - 高申诉成功率规则排行</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>标题</b>: "高申诉成功率规则排行"。</li>
                        <li><b>副标题</b>: "指向AI最不准确或定义最模糊的规则"，精准概括了其核心诊断价值。</li>
                        <li><b>内容</b>: 以列表形式，列出那些<b>被申诉并且最终申诉成功次数最多</b>的规则。每一行包含规则名称、规则类型、申诉次数、申诉成功次数和申诉成功率。</li>
                        <li><b>业务价值与实现</b>: 这直接定位了系统中"最不靠谱"的规则。排名靠前的规则是需要立即被审核、优化甚至禁用的首要目标，是改进AI质检规则的直接依据。数据来源于申诉成功规则的命中次数统计，并按成功率排序。</li>
                    </ul>
                </li>
                <li><b>右侧卡片 - 高申诉率团队/坐席排行（带 Tab 切换）</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>标题</b>: "高申诉率团队/坐席排行"。</li>
                        <li><b>副标题</b>: "反映团队文化或对质检结果的接受度"。</li>
                        <li><b>内容</b>: 这是一个带 Tab 切换的排名榜，分为"团队排行"和"坐席排行"。
                            <ul className="list-[square] pl-6 space-y-1">
                                <li><b>团队排行</b>: 按"申诉率"（申诉次数/团队总质检数）对团队进行排名。用于识别整体上对质检结果异议较多的团队。</li>
                                <li><b>坐席排行</b>: 按"申诉率"对个人进行排名。用于识别申诉行为较为频繁的个体坐席。</li>
                            </ul>
                        </li>
                        <li><b>业务价值与实现</b>:
                            <ul className="list-[circle] pl-6 space-y-1">
                                <li>高申诉率的团队可能存在普遍的异议文化，或者该团队的业务场景与通用质检标准存在冲突，需要班组长介入沟通和指导。</li>
                                <li>高申诉率的个人可能需要特别关注，分析其申诉是基于客观事实（质检错误）还是习惯性行为，以便进行针对性的辅导或培训。</li>
                            </ul>
                            数据来源于团队/坐席维度的质检总数、申诉总数和申诉成功数的聚合统计。</li>
                    </ul>
                </li>
            </ul>

            <h3 className="text-lg font-semibold text-slate-700 mt-4">3. 核心交互与操作</h3>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>反向审查</b>: 整个报告的核心逻辑是<b>从"结果"（申诉）反推"原因"（规则问题、管理问题）</b>。这是一种非常高效的问题发现机制，能够主动暴露质检体系中的薄弱环节。</li>
                <li><b>聚焦"痛点"</b>: 报告的设计直指问题核心。"高申诉成功率规则"直接暴露了系统层面（AI规则或复核标准）的短板，"高申诉率排行"则揭示了管理层面（团队文化、个人认知）的潜在问题。</li>
                <li><b>驱动系统和管理优化</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>优化系统</b>: 根据"高申诉成功率规则排行"，质检主管可以快速定位到那些有争议、不准确或定义模糊的AI质检规则。他们可以进一步导航到<b>质检规则管理</b>页面，对这些规则进行修改、补充说明，或者考虑使用更准确、更具鲁棒性的算子（如大模型）进行重构，从而提升AI质检的整体准确性。</li>
                        <li><b>优化管理</b>: 根据"高申诉率团队/坐席排行"，管理者可以与相关团队的班组长或坐席本人进行沟通，深入了解申诉背后的具体原因。这有助于进行针对性的培训、标准解读或情景分析，以达成对质检标准的共识，减少不必要的申诉，提升团队对质检结果的接受度。</li>
                    </ul>
                </li>
            </ul>
        </div>
    );

    // 模拟申诉概览数据
    const appealOverviewData = useMemo(() => ({
        totalAppeals: 156,
        appealRate: 4.2,
        appealSuccessRate: 28.8
    }), []);

    // 模拟申诉趋势数据
    const appealTrendData: AppealTrendData[] = useMemo(() => [
        { period: '第1周', appealCount: 32, appealSuccessRate: 25.0 },
        { period: '第2周', appealCount: 28, appealSuccessRate: 28.6 },
        { period: '第3周', appealCount: 45, appealSuccessRate: 31.1 },
        { period: '第4周', appealCount: 51, appealSuccessRate: 29.4 }
    ], []);

    // 模拟高申诉成功率规则数据
    const highAppealSuccessRuleData: HighAppealSuccessRule[] = useMemo(() => [
        {
            ruleName: '客户不满未道歉',
            ruleType: '服务规范',
            appealCount: 45,
            appealSuccessCount: 31,
            appealSuccessRate: 68.9,
            severity: 'high',
            rank: 1
        },
        {
            ruleName: '未确认客户需求',
            ruleType: '服务质量',
            appealCount: 31,
            appealSuccessCount: 19,
            appealSuccessRate: 61.3,
            severity: 'medium',
            rank: 2
        },
        {
            ruleName: '语速过快',
            ruleType: '服务规范',
            appealCount: 23,
            appealSuccessCount: 13,
            appealSuccessRate: 56.5,
            severity: 'low',
            rank: 3
        },
        {
            ruleName: '通话静音超时',
            ruleType: '服务质量',
            appealCount: 18,
            appealSuccessCount: 9,
            appealSuccessRate: 50.0,
            severity: 'medium',
            rank: 4
        },
        {
            ruleName: '未主动核身',
            ruleType: '合规风险',
            appealCount: 15,
            appealSuccessCount: 6,
            appealSuccessRate: 40.0,
            severity: 'high',
            rank: 5
        }
    ], []);

    // 模拟高申诉率团队数据
    const highAppealRateTeamData: HighAppealRateTeam[] = useMemo(() => [
        {
            teamName: 'E班组',
            totalQualityChecks: 1234,
            appealCount: 89,
            appealRate: 7.2,
            appealSuccessCount: 28,
            appealSuccessRate: 31.5,
            memberCount: 14,
            rank: 1
        },
        {
            teamName: 'D班组',
            totalQualityChecks: 1156,
            appealCount: 72,
            appealRate: 6.2,
            appealSuccessCount: 19,
            appealSuccessRate: 26.4,
            memberCount: 13,
            rank: 2
        },
        {
            teamName: 'C班组',
            totalQualityChecks: 1089,
            appealCount: 58,
            appealRate: 5.3,
            appealSuccessCount: 17,
            appealSuccessRate: 29.3,
            memberCount: 11,
            rank: 3
        },
        {
            teamName: 'B班组',
            totalQualityChecks: 1298,
            appealCount: 45,
            appealRate: 3.5,
            appealSuccessCount: 12,
            appealSuccessRate: 26.7,
            memberCount: 15,
            rank: 4
        },
        {
            teamName: 'A班组',
            totalQualityChecks: 1345,
            appealCount: 32,
            appealRate: 2.4,
            appealSuccessCount: 8,
            appealSuccessRate: 25.0,
            memberCount: 12,
            rank: 5
        }
    ], []);

    // 模拟高申诉率坐席数据
    const highAppealRateAgentData: HighAppealRateAgent[] = useMemo(() => [
        {
            agentName: '孙八',
            teamName: 'E班组',
            totalQualityChecks: 89,
            appealCount: 23,
            appealRate: 25.8,
            appealSuccessCount: 8,
            appealSuccessRate: 34.8,
            rank: 1
        },
        {
            agentName: '周九',
            teamName: 'E班组',
            totalQualityChecks: 92,
            appealCount: 21,
            appealRate: 22.8,
            appealSuccessCount: 6,
            appealSuccessRate: 28.6,
            rank: 2
        },
        {
            agentName: '吴十',
            teamName: 'D班组',
            totalQualityChecks: 87,
            appealCount: 18,
            appealRate: 20.7,
            appealSuccessCount: 5,
            appealSuccessRate: 27.8,
            rank: 3
        },
        {
            agentName: '郑十一',
            teamName: 'D班组',
            totalQualityChecks: 94,
            appealCount: 17,
            appealRate: 18.1,
            appealSuccessCount: 4,
            appealSuccessRate: 23.5,
            rank: 4
        },
        {
            agentName: '王十二',
            teamName: 'C班组',
            totalQualityChecks: 91,
            appealCount: 15,
            appealRate: 16.5,
            appealSuccessCount: 5,
            appealSuccessRate: 33.3,
            rank: 5
        }
    ], []);

    // Filter fields configuration
    const filterFields = [
        {
            key: 'timeRange',
            label: '时间范围',
            type: 'select' as const,
            value: timeRange,
            options: [
                { value: '今日', label: '今日' },
                { value: '本周', label: '本周' },
                { value: '本月', label: '本月' },
                { value: '自定义', label: '自定义' }
            ],
            onChange: setTimeRange
        },
        {
            key: 'selectedTeam',
            label: '班组/团队',
            type: 'select' as const,
            value: selectedTeam,
            options: [
                { value: '全部班组', label: '全部班组' },
                { value: 'A班组', label: 'A班组' },
                { value: 'B班组', label: 'B班组' },
                { value: 'C班组', label: 'C班组' },
                { value: 'D班组', label: 'D班组' },
                { value: 'E班组', label: 'E班组' }
            ],
            onChange: setSelectedTeam
        },
        {
            key: 'selectedAgent',
            label: '坐席',
            type: 'select' as const,
            value: selectedAgent,
            options: [
                { value: '全部坐席', label: '全部坐席' },
                { value: '张三', label: '张三' },
                { value: '李四', label: '李四' },
                { value: '王五', label: '王五' },
                { value: '赵六', label: '赵六' }
            ],
            onChange: setSelectedAgent
        }
    ];

    // Enhanced KPI data
    const kpiData = [
        {
            title: "总申诉数",
            value: appealOverviewData.totalAppeals,
            unit: "件",
            trend: 'up' as const,
            trendValue: "+12.3%",
            icon: MessageSquareX,
            gradient: 'bg-gradient-to-br from-blue-500 to-blue-600',
            description: "本周期内收到的申诉总数量"
        },
        {
            title: "申诉率",
            value: appealOverviewData.appealRate,
            unit: "%",
            trend: 'down' as const,
            trendValue: "-0.8%",
            icon: Percent,
            gradient: 'bg-gradient-to-br from-orange-500 to-orange-600',
            description: "申诉数量占总质检数量的比例"
        },
        {
            title: "申诉成功率",
            value: appealOverviewData.appealSuccessRate,
            unit: "%",
            trend: 'up' as const,
            trendValue: "+3.2%",
            icon: Shield,
            gradient: 'bg-gradient-to-br from-red-500 to-red-600',
            description: "申诉成功数量占总申诉数量的比例"
        }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
            <UnifiedPageHeader
                title="坐席申诉洞察报告"
                subtitle="深度分析坐席申诉数据，识别质检公平性问题和系统改进机会，为管理决策提供数据支撑"
                icon={MessageSquareX}
                badge={{ text: "申诉分析", color: "yellow" }}
                actions={[
                    {
                        label: "刷新数据",
                        icon: RefreshCw,
                        variant: "secondary",
                        onClick: () => console.log('刷新数据')
                    },
                    {
                        label: "导出报表",
                        icon: Download,
                        variant: "primary",
                        onClick: () => console.log('导出报表')
                    }
                ]}
                showDesignGuide={true}
                designGuideContent={designGuideContent}
            />

            <main className="p-6 space-y-8">
                {/* Enhanced Filter Section */}
                <EnhancedFilterSection
                    fields={filterFields}
                    onSearch={() => console.log('搜索')}
                    onReset={() => {
                        setTimeRange('本月');
                        setSelectedTeam('全部班组');
                        setSelectedAgent('全部坐席');
                    }}
                />

                {/* Enhanced KPI Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {kpiData.map((kpi, index) => (
                        <EnhancedKPICard
                            key={index}
                            delay={index * 0.1}
                            {...kpi}
                        />
                    ))}
                </div>

                {/* 申诉趋势分析 */}
                <div className="mb-6">
                    <AppealTrendChart data={appealTrendData} />
                </div>

                {/* 问题定位分析 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <HighAppealSuccessRuleRanking data={highAppealSuccessRuleData} />
                    <HighAppealRateRanking 
                        teamData={highAppealRateTeamData} 
                        agentData={highAppealRateAgentData} 
                    />
                </div>
            </main>
        </div>
    );
};

export default FinalAppealInsightReportPage;