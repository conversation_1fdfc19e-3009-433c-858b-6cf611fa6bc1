import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { X, Search, UserCheck, User } from 'lucide-react';

export interface User {
  id: string;
  name: string;
  department?: string;
}

// Mock user data for demonstration
const allUsers: User[] = [
  { id: 'u001', name: '张三', department: '贷后支持一组' },
  { id: 'u002', name: '李四', department: '贷后支持二组' },
  { id: 'u003', name: '王五', department: '贷后支持三组' },
  { id: 'u004', name: '赵六', department: '贷后支持四组' },
  { id: 'u005', name: '孙七', department: '贷后支持五组' },
  { id: 'u006', name: '周八', department: '贷后支持六组' },
  { id: 'u007', name: '吴九', department: '贷后支持七组' },
  { id: 'u008', name: '郑十', department: '贷后支持八组' },
  { id: 'u009', name: '王主管', department: '贷后支持九组' },
  { id: 'u010', name: '李经理', department: '贷后支持十组' },
  { id: 'u011', name: '陈顾问', department: '贷后支持十一组' },
  { id: 'u012', name: '丁专员', department: '贷后支持十二组' },
];

interface UserSelectionModalProps {
  onClose: () => void;
  onSelect: (selectedUsers: User[]) => void;
  initialSelectedUsers?: User[];
  multiSelect: boolean;
}

export const UserSelectionModal: React.FC<UserSelectionModalProps> = ({ onClose, onSelect, initialSelectedUsers = [], multiSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<User[]>(initialSelectedUsers);

  const filteredUsers = useMemo(() => {
    return allUsers.filter(user => user.name.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [searchTerm]);

  const handleToggleUser = (user: User) => {
    setSelectedUsers(prev => {
      if (multiSelect) {
        const isSelected = prev.some(u => u.id === user.id);
        return isSelected ? prev.filter(u => u.id !== user.id) : [...prev, user];
      } else {
        return [user];
      }
    });
  };

  const handleConfirm = () => {
    onSelect(selectedUsers);
  };

  return (
    <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-xl shadow-2xl w-full max-w-md flex flex-col"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold">选择人员</h3>
          <button onClick={onClose} className="p-1.5 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"><X className="w-5 h-5"/></button>
        </div>

        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="搜索人员..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="flex-grow p-4 overflow-y-auto" style={{ maxHeight: '40vh' }}>
          <div className="space-y-2">
            {filteredUsers.map(user => {
              const isSelected = selectedUsers.some(u => u.id === user.id);
              return (
                <div
                  key={user.id}
                  onClick={() => handleToggleUser(user)}
                  className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                    isSelected ? 'bg-blue-100 border-blue-300' : 'hover:bg-gray-100 border-transparent'
                  } border`}
                >
                  <div className="flex items-center">
                    <User className="w-5 h-5 mr-3 text-gray-600"/>
                    <span className="font-medium text-gray-800">{user.name}</span>
                  </div>
                  {isSelected && <UserCheck className="w-5 h-5 text-blue-600"/>}
                </div>
              );
            })}
             {filteredUsers.length === 0 && (
                <div className="text-center py-6 text-gray-500">
                    <p>未找到匹配的人员。</p>
                </div>
            )}
          </div>
        </div>

        <div className="flex-shrink-0 px-4 py-4 border-t bg-gray-50 flex justify-end gap-3 rounded-b-xl">
            <button onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
            <button onClick={handleConfirm} className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">确认</button>
        </div>
      </motion.div>
    </motion.div>
  );
}; 