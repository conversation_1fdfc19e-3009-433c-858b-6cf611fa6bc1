import React, { useState } from 'react';
import EntityCheckConfig, { type EntityCheckConfig as ConfigType, type EntityCondition } from './entityCheck/EntityCheckConfig';
import EntityCheckConcepts from './entityCheck/EntityCheckConcepts';
import EntityCheckTester from './entityCheck/EntityCheckTester';
import EntityCheckTips from './entityCheck/EntityCheckTips';
import EntityCheckCases from './entityCheck/EntityCheckCases';

/**
 * 测试结果接口
 */
interface TestResult {
  matched: boolean;
  detectedEntities: Array<{
    type: string;
    value: string;
    sentence: string;
    position: number;
  }>;
  matchedConditions: Array<{
    condition: EntityCondition;
    matched: boolean;
    detectedValue?: string;
    reason: string;
  }>;
  matchedSentences: string[];
  details: string;
}

/**
 * 实体识别器
 */
class EntityRecognizer {
  /**
   * 识别日期实体
   */
  static recognizeDates(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /\d{4}年\d{1,2}月\d{1,2}[日号]/g,
      /\d{4}-\d{1,2}-\d{1,2}/g,
      /\d{1,2}月\d{1,2}[日号]/g,
      /(今天|明天|昨天|后天|前天)/g,
      /(周[一二三四五六日]|星期[一二三四五六日天])/g,
      /(春节|国庆节|中秋节|端午节|清明节|元旦|劳动节)/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别时间实体
   */
  static recognizeTimes(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /\d{1,2}[点时]\d{0,2}[分钟]?/g,
      /\d{1,2}:\d{2}/g,
      /(上午|下午|晚上|凌晨)\d{1,2}[点时]/g,
      /(早上|中午|傍晚|深夜)/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别省份实体
   */
  static recognizeProvinces(text: string): Array<{ value: string; position: number }> {
    const provinces = [
      '北京', '上海', '天津', '重庆', '河北', '山西', '辽宁', '吉林', '黑龙江',
      '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南',
      '广东', '海南', '四川', '贵州', '云南', '陕西', '甘肃', '青海', '台湾',
      '内蒙古', '广西', '西藏', '宁夏', '新疆', '香港', '澳门'
    ];
    
    const pattern = new RegExp(`(${provinces.join('|')})`, 'g');
    return this.extractEntities(text, [pattern]);
  }

  /**
   * 识别城市实体
   */
  static recognizeCities(text: string): Array<{ value: string; position: number }> {
    const cities = [
      '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
      '西安市', '郑州市', '济南市', '青岛市', '大连市', '宁波市', '厦门市', '福州市',
      '长沙市', '合肥市', '石家庄市', '太原市', '沈阳市', '长春市', '哈尔滨市',
      '南昌市', '贵阳市', '昆明市', '兰州市', '银川市', '西宁市', '乌鲁木齐市'
    ];
    
    const pattern = new RegExp(`(${cities.join('|')})`, 'g');
    return this.extractEntities(text, [pattern]);
  }

  /**
   * 识别区县实体
   */
  static recognizeDistricts(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /[一-龟]{1,3}[区县]/g,
      /(朝阳区|海淀区|西城区|东城区|丰台区|石景山区|通州区|昌平区|大兴区|房山区|门头沟区|平谷区|密云区|延庆区)/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别身份证号码
   */
  static recognizeIdCards(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /\d{17}[\dXx]/g,
      /\d{15}/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别人名实体
   */
  static recognizePersonNames(text: string): Array<{ value: string; position: number }> {
    const surnames = [
      '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高',
      '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
    ];
    
    const pattern = new RegExp(`(${surnames.join('|')})[一-龟]{1,3}`, 'g');
    return this.extractEntities(text, [pattern]);
  }

  /**
   * 识别邮箱实体
   */
  static recognizeEmails(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 通用实体提取方法
   */
  private static extractEntities(text: string, patterns: RegExp[]): Array<{ value: string; position: number }> {
    const entities: Array<{ value: string; position: number }> = [];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        entities.push({
          value: match[0],
          position: match.index
        });
      }
    });
    
    return entities;
  }

  /**
   * 识别所有实体
   */
  static recognizeAll(text: string): Record<string, Array<{ value: string; position: number }>> {
    return {
      date: this.recognizeDates(text),
      time: this.recognizeTimes(text),
      province: this.recognizeProvinces(text),
      city: this.recognizeCities(text),
      district: this.recognizeDistricts(text),
      idCard: this.recognizeIdCards(text),
      personName: this.recognizePersonNames(text),
      email: this.recognizeEmails(text)
    };
  }
}

/**
 * 信息实体检查详细演示页面
 * 提供信息实体检查算子的完整学习体验
 */
const EntityCheckPage: React.FC = () => {
  // 模拟随路参数（通话元数据）
  const mockMetadata = {
    '客服姓名': '王经理',
    '客服ID': 'CS001',
    '技能组名称': '信用卡部',
    '呼叫类型': '呼入',
    '业务线': '信用卡',
    '自定义数据1': '上海',
    '自定义数据2': '明天',
    '自定义数据3': '110101199001011234'
  };
  // 配置状态
  const [config, setConfig] = useState<ConfigType>({
    detectionRole: 'all',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 5,
    entityConditions: [{
      entityType: 'personName',
      operator: 'equals',
      expectedValue: '客服姓名',
      fieldType: 'system'
    }]
  });

  // 测试文本
  const [testText, setTestText] = useState(
    '客户：王经理您好，我想咨询一下业务。\n客服：好的，我是王经理，请问需要什么帮助？\n客户：谢谢王经理的服务。'
  );

  // 测试结果
  const [testResult, setTestResult] = useState<TestResult | null>(null);

  /**
   * 执行测试
   */
  const runTest = () => {
    const sentences = testText.split('\n').filter(s => s.trim());
    let targetSentences: string[] = [];

    // 根据检测角色筛选句子
    if (config.detectionRole === 'agent') {
      targetSentences = sentences.filter(s => s.startsWith('客服：'));
    } else if (config.detectionRole === 'customer') {
      targetSentences = sentences.filter(s => s.startsWith('客户：'));
    } else {
      targetSentences = sentences;
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    const combinedText = targetSentences.join(' ');
    const allEntities = EntityRecognizer.recognizeAll(combinedText);
    
    // 检测实体
    const detectedEntities: Array<{
      type: string;
      value: string;
      sentence: string;
      position: number;
    }> = [];

    Object.entries(allEntities).forEach(([type, entities]) => {
      entities.forEach(entity => {
        // 找到包含该实体的句子
        const containingSentence = targetSentences.find(s => s.includes(entity.value)) || '';
        detectedEntities.push({
          type,
          value: entity.value,
          sentence: containingSentence,
          position: entity.position
        });
      });
    });

    // 检查条件匹配
    const matchedConditions = config.entityConditions.map(condition => {
      const typeEntities = detectedEntities.filter(e => e.type === condition.entityType);
      
      for (const entity of typeEntities) {
        const matched = checkCondition(entity.value, condition);
        if (matched.matched) {
          return {
            condition,
            matched: true,
            detectedValue: entity.value,
            reason: matched.reason
          };
        }
      }
      
      return {
        condition,
        matched: false,
        reason: `未检测到${getEntityTypeName(condition.entityType)}实体`
      };
    });

    const overallMatched = config.entityConditions.length > 0 && 
      matchedConditions.every(mc => mc.matched);

    const matchedSentences = detectedEntities
      .map(e => e.sentence)
      .filter((s, i, arr) => arr.indexOf(s) === i && s.trim());

    setTestResult({
      matched: overallMatched,
      detectedEntities,
      matchedConditions,
      matchedSentences,
      details: overallMatched 
        ? `规则已命中 - 检测到${matchedConditions.filter(mc => mc.matched).length}个匹配条件`
        : `规则未命中 - ${matchedConditions.filter(mc => !mc.matched).length}个条件不满足`
    });
  };

  /**
   * 检查单个条件
   */
  const checkCondition = (entityValue: string, condition: EntityCondition): { matched: boolean; reason: string } => {
    const { operator, expectedValue } = condition;
    
    if (!expectedValue.trim()) {
      return { matched: false, reason: '必须选择字段进行比较' };
    }

    // 获取随路参数值
    const metadataValue = mockMetadata[expectedValue as keyof typeof mockMetadata];
    if (metadataValue === undefined) {
      return { matched: false, reason: `随路参数"${expectedValue}"不存在` };
    }

    switch (operator) {
      case 'equals':
        return {
          matched: entityValue === metadataValue,
          reason: entityValue === metadataValue 
            ? `实体"${entityValue}"与随路参数"${metadataValue}"完全匹配` 
            : `实体"${entityValue}"与随路参数"${metadataValue}"不匹配`
        };
      case 'notEquals':
        return {
          matched: entityValue !== metadataValue,
          reason: entityValue !== metadataValue 
            ? `实体"${entityValue}"与随路参数"${metadataValue}"不匹配（符合要求）` 
            : `实体"${entityValue}"与随路参数"${metadataValue}"匹配（不符合要求）`
        };
      case 'contains':
        return {
          matched: entityValue.includes(metadataValue),
          reason: entityValue.includes(metadataValue) 
            ? `实体"${entityValue}"包含随路参数"${metadataValue}"` 
            : `实体"${entityValue}"不包含随路参数"${metadataValue}"`
        };
      case 'notContains':
        return {
          matched: !entityValue.includes(metadataValue),
          reason: !entityValue.includes(metadataValue) 
            ? `实体"${entityValue}"不包含随路参数"${metadataValue}"（符合要求）` 
            : `实体"${entityValue}"包含随路参数"${metadataValue}"（不符合要求）`
        };
      default:
        return { matched: false, reason: '未知运算符' };
    }
  };

  /**
   * 获取实体类型中文名
   */
  const getEntityTypeName = (type: string): string => {
    const nameMap: Record<string, string> = {
      date: '日期',
      time: '时间',
      province: '省份',
      city: '城市',
      district: '区县',
      idCard: '身份证号码',
      personName: '人名',
      email: '邮箱'
    };
    return nameMap[type] || type;
  };

  /**
   * 加载案例
   */
  const loadCase = (caseConfig: ConfigType, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🏷️ 信息实体检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测对话中的实体信息，并与通话的随路参数进行比较验证，核验对话内容与业务记录的一致性
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                ⚡ 中级功能
              </span>
              <span className="text-sm text-gray-500">
                算子类型：文字检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 随路参数展示 */}
      <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg shadow-sm border border-amber-200 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-2xl">📋</span>
          <h2 className="text-xl font-semibold text-gray-900">模拟随路参数</h2>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
            通话元数据
          </span>
        </div>
        
        <div className="bg-white rounded-lg border border-amber-200 p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            {Object.entries(mockMetadata).map(([key, value]) => (
              <div key={key} className="flex flex-col">
                <span className="text-gray-600 font-medium">{key}</span>
                <span className="text-gray-800 bg-gray-50 px-2 py-1 rounded mt-1">{value}</span>
              </div>
            ))}
          </div>
          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-sm text-amber-800">
              💡 <strong>说明</strong>：信息实体检查会将对话中识别出的实体与上述随路参数进行比较。
              例如，当客户提到"王经理"时，系统会检查这个人名与"客服姓名"字段的值是否匹配。
            </p>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <EntityCheckConcepts />

            {/* 配置演示 */}
            <EntityCheckConfig config={config} onChange={setConfig} />

            {/* 实时测试 */}
            <EntityCheckTester
              config={config}
              testText={testText}
              onTextChange={setTestText}
              onTest={runTest}
              onClearResult={() => setTestResult(null)}
              testResult={testResult}
            />

            {/* 测试结果展示 */}
            {testResult && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                    <span>📊</span>
                    <span>测试结果</span>
                  </h3>
                  <button
                    onClick={() => setTestResult(null)}
                    className="text-sm text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                {/* 主要结果卡片 */}
                <div className={`relative overflow-hidden rounded-xl border-2 ${
                  testResult.matched 
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                    : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
                }`}>
                  <div className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                        testResult.matched ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                          {testResult.matched ? '✓' : '✗'}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                          {testResult.matched ? '规则命中' : '规则未命中'}
                        </div>
                        <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                          {testResult.details}
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                          testResult.matched 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {testResult.matched ? '✅ 通过' : '❌ 未通过'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 详细结果 */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 检测到的实体 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-blue-500">🏷️</span>
                      <h4 className="font-semibold text-gray-800">检测到的实体</h4>
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                        {testResult.detectedEntities.length} 个
                      </span>
                    </div>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {testResult.detectedEntities.length > 0 ? (
                        testResult.detectedEntities.map((entity, i) => (
                          <div key={i} className="flex items-center justify-between py-1 px-2 bg-gray-50 rounded">
                            <div>
                              <span className="text-sm font-medium text-gray-700">{entity.value}</span>
                              <span className="text-xs text-gray-500 ml-2">({getEntityTypeName(entity.type)})</span>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-gray-500 text-center py-4">未检测到任何实体</div>
                      )}
                    </div>
                  </div>

                  {/* 条件匹配情况 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-purple-500">📋</span>
                      <h4 className="font-semibold text-gray-800">条件匹配</h4>
                      <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                        {testResult.matchedConditions.filter(mc => mc.matched).length}/{testResult.matchedConditions.length}
                      </span>
                    </div>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {testResult.matchedConditions.map((matchedCondition, i) => (
                        <div key={i} className="flex items-center justify-between py-1">
                          <div>
                            <span className="text-sm text-gray-700">
                              {getEntityTypeName(matchedCondition.condition.entityType)}
                            </span>
                            {matchedCondition.detectedValue && (
                              <span className="text-xs text-gray-500 ml-1">({matchedCondition.detectedValue})</span>
                            )}
                          </div>
                          <span className={`text-xs px-2 py-1 rounded ${
                            matchedCondition.matched
                              ? 'bg-green-100 text-green-700'
                              : 'bg-red-100 text-red-700'
                          }`}>
                            {matchedCondition.matched ? '✓ 匹配' : '✗ 不匹配'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 配置摘要 */}
                <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="text-gray-500">⚙️</span>
                    <h4 className="font-semibold text-gray-800">当前配置</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-gray-600">检测角色:</span>
                        <span className="font-medium">{config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">检测范围:</span>
                        <span className="font-medium">{config.detectionScope === 'full' ? '全文' : `第${config.rangeStart}-${config.rangeEnd}句`}</span>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-gray-600">实体条件:</span>
                        <span className="font-medium">{config.entityConditions.length} 个</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">检测实体:</span>
                        <span className="font-medium">{testResult.detectedEntities.length} 个</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <EntityCheckCases onLoadCase={loadCase} />
            
            {/* 使用提示 */}
            <EntityCheckTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>理解核心功能</strong>：掌握实体识别与随路参数比较的工作原理和流程</p>
                <p>• <strong>字段选择技巧</strong>：学会选择合适的系统字段或自定义字段进行比较</p>
                <p>• <strong>应用场景分析</strong>：理解身份验证、信息核验、敏感信息防护等实际应用</p>
                <p>• <strong>逻辑运算应用</strong>：根据业务需求选择合适的比较运算符（=、!=、包含、不包含）</p>
                <p>• <strong>案例实践学习</strong>：通过真实业务案例理解功能的实际价值和使用方法</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntityCheckPage; 