import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, RotateCcw, CheckCircle, XCircle } from 'lucide-react';
import { Dimension } from './LargeModelCheckConfig';

interface LargeModelCheckTesterProps {
  text: string;
  onTextChange: (newText: string) => void;
  dimensions: Dimension[];
  onTest: (text: string) => TestResult;
}

export interface TestResult {
  hit: boolean;
  hitDimensions: string[];
}

const LargeModelCheckTester: React.FC<LargeModelCheckTesterProps> = ({ text, onTextChange, dimensions, onTest }) => {
  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleTest = () => {
    if (!text.trim() || dimensions.length === 0) {
        setResult({ hit: false, hitDimensions: [] });
        return;
    };
    setIsLoading(true);
    setResult(null);
    // Simulate API call
    setTimeout(() => {
      const testResult = onTest(text);
      setResult(testResult);
      setIsLoading(false);
    }, 800);
  };

  const handleReset = () => {
    onTextChange(``); // Clear text via parent
    setResult(null);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="p-6 bg-white rounded-lg border border-gray-200"
    >
      <h3 className="text-xl font-semibold text-gray-800 mb-4">实时测试</h3>
      <div className="flex flex-col space-y-4">
        <textarea
          value={text}
          onChange={(e) => onTextChange(e.target.value)}
          rows={10}
          className="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 transition"
          placeholder="请输入或粘贴对话文本，或从右侧加载案例..."
        />
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={handleTest}
              disabled={isLoading}
              className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300 disabled:cursor-not-allowed"
            >
              <Play className={`w-5 h-5 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? '测试中...' : '执行测试'}
            </button>
            <button
              onClick={handleReset}
              className="flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <RotateCcw className="w-5 h-5 mr-2" />
              清空
            </button>
          </div>
          <span className="text-sm text-gray-500">{text.length} 字</span>
        </div>
      </div>

      <AnimatePresence>
        {result && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6 p-4 rounded-lg"
          >
            <h4 className="text-lg font-semibold mb-2">测试结果</h4>
            {result.hit ? (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center text-green-800">
                  <CheckCircle className="w-6 h-6 mr-2" />
                  <span className="font-bold">命中规则</span>
                </div>
                <p className="mt-2 text-sm text-green-700">命中了以下维度：</p>
                <ul className="mt-2 list-disc list-inside text-sm text-green-900 space-y-1">
                  {result.hitDimensions.map((dim, index) => (
                    <li key={index}>
                      <span className="font-semibold">{dim}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center text-red-800">
                  <XCircle className="w-6 h-6 mr-2" />
                  <span className="font-bold">未命中规则</span>
                </div>
                <p className="mt-2 text-sm text-red-700">
                  根据当前配置，未在文本中检测到任何命中的维度。
                </p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default LargeModelCheckTester;
