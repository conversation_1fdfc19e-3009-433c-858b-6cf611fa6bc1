import React from 'react';
import { Link, useLocation } from 'react-router-dom';

/**
 * 顶部导航栏组件
 * 显示系统名称和主要导航链接
 */
const Navigation: React.FC = () => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-full mx-auto px-6">
        <div className="flex justify-between items-center h-16">
          {/* Logo和系统名称 */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">质</span>
              </div>
              <span className="text-xl font-semibold text-gray-900">
                智能客服质检系统
              </span>
            </Link>
          </div>



          {/* 右侧工具栏 */}
          <div className="flex items-center space-x-4">
            {/* <span className="text-sm text-gray-500">
              演示版本 v1.0
            </span> */}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation; 