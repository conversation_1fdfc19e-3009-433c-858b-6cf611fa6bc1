import React, { useState } from 'react';
import type { EntityCheckConfig, EntityCondition } from './EntityCheckConfig';

/**
 * 测试结果接口
 */
interface TestResult {
  matched: boolean;
  detectedEntities: Array<{
    type: string;
    value: string;
    sentence: string;
    position: number;
  }>;
  matchedConditions: Array<{
    condition: EntityCondition;
    matched: boolean;
    detectedValue?: string;
    reason: string;
  }>;
  matchedSentences: string[];
  details: string;
}

interface Props {
  config: EntityCheckConfig;
  testText: string;
  onTextChange: (text: string) => void;
  onTest: () => void;
  onClearResult: () => void;
  testResult: TestResult | null;
}

/**
 * 实体识别器
 */
class EntityRecognizer {
  /**
   * 识别日期实体
   */
  static recognizeDates(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /\d{4}年\d{1,2}月\d{1,2}[日号]/g,
      /\d{4}-\d{1,2}-\d{1,2}/g,
      /\d{1,2}月\d{1,2}[日号]/g,
      /(今天|明天|昨天|后天|前天)/g,
      /(周[一二三四五六日]|星期[一二三四五六日天])/g,
      /(春节|国庆节|中秋节|端午节|清明节|元旦|劳动节)/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别时间实体
   */
  static recognizeTimes(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /\d{1,2}[点时]\d{0,2}[分钟]?/g,
      /\d{1,2}:\d{2}/g,
      /(上午|下午|晚上|凌晨)\d{1,2}[点时]/g,
      /(早上|中午|傍晚|深夜)/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别省份实体
   */
  static recognizeProvinces(text: string): Array<{ value: string; position: number }> {
    const provinces = [
      '北京', '上海', '天津', '重庆', '河北', '山西', '辽宁', '吉林', '黑龙江',
      '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南',
      '广东', '海南', '四川', '贵州', '云南', '陕西', '甘肃', '青海', '台湾',
      '内蒙古', '广西', '西藏', '宁夏', '新疆', '香港', '澳门'
    ];
    
    const pattern = new RegExp(`(${provinces.join('|')})`, 'g');
    return this.extractEntities(text, [pattern]);
  }

  /**
   * 识别城市实体
   */
  static recognizeCities(text: string): Array<{ value: string; position: number }> {
    const cities = [
      '北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市',
      '西安市', '郑州市', '济南市', '青岛市', '大连市', '宁波市', '厦门市', '福州市',
      '长沙市', '合肥市', '石家庄市', '太原市', '沈阳市', '长春市', '哈尔滨市',
      '南昌市', '贵阳市', '昆明市', '兰州市', '银川市', '西宁市', '乌鲁木齐市'
    ];
    
    const pattern = new RegExp(`(${cities.join('|')})`, 'g');
    return this.extractEntities(text, [pattern]);
  }

  /**
   * 识别区县实体
   */
  static recognizeDistricts(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /[一-龟]{1,3}[区县]/g,
      /(朝阳区|海淀区|西城区|东城区|丰台区|石景山区|通州区|昌平区|大兴区|房山区|门头沟区|平谷区|密云区|延庆区)/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别身份证号码
   */
  static recognizeIdCards(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /\d{17}[\dXx]/g,
      /\d{15}/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 识别人名实体
   */
  static recognizePersonNames(text: string): Array<{ value: string; position: number }> {
    const surnames = [
      '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高',
      '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
    ];
    
    const pattern = new RegExp(`(${surnames.join('|')})[一-龟]{1,3}`, 'g');
    return this.extractEntities(text, [pattern]);
  }

  /**
   * 识别邮箱实体
   */
  static recognizeEmails(text: string): Array<{ value: string; position: number }> {
    const patterns = [
      /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
    ];
    
    return this.extractEntities(text, patterns);
  }

  /**
   * 通用实体提取方法
   */
  private static extractEntities(text: string, patterns: RegExp[]): Array<{ value: string; position: number }> {
    const entities: Array<{ value: string; position: number }> = [];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        entities.push({
          value: match[0],
          position: match.index
        });
      }
    });
    
    return entities;
  }

  /**
   * 识别所有实体
   */
  static recognizeAll(text: string): Record<string, Array<{ value: string; position: number }>> {
    return {
      date: this.recognizeDates(text),
      time: this.recognizeTimes(text),
      province: this.recognizeProvinces(text),
      city: this.recognizeCities(text),
      district: this.recognizeDistricts(text),
      idCard: this.recognizeIdCards(text),
      personName: this.recognizePersonNames(text),
      email: this.recognizeEmails(text)
    };
  }
}

/**
 * 信息实体检查测试器组件
 */
const EntityCheckTester: React.FC<Props> = ({
  config,
  testText,
  onTextChange,
  onTest,
  onClearResult,
  testResult
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  /**
   * 执行实体检查测试
   */
  const executeTest = async () => {
    setIsProcessing(true);
    
    try {
      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const sentences = testText.split('\n').filter(s => s.trim());
      let targetSentences: string[] = [];

      // 根据检测角色筛选句子
      if (config.detectionRole === 'agent') {
        targetSentences = sentences.filter(s => s.startsWith('客服：'));
      } else if (config.detectionRole === 'customer') {
        targetSentences = sentences.filter(s => s.startsWith('客户：'));
      } else {
        targetSentences = sentences;
      }

      // 根据检测范围筛选
      if (config.detectionScope === 'range') {
        targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
      }

      const combinedText = targetSentences.join(' ');
      const allEntities = EntityRecognizer.recognizeAll(combinedText);
      
      // 检测实体
      const detectedEntities: Array<{
        type: string;
        value: string;
        sentence: string;
        position: number;
      }> = [];

      Object.entries(allEntities).forEach(([type, entities]) => {
        entities.forEach(entity => {
          // 找到包含该实体的句子
          const containingSentence = targetSentences.find(s => s.includes(entity.value)) || '';
          detectedEntities.push({
            type,
            value: entity.value,
            sentence: containingSentence,
            position: entity.position
          });
        });
      });

      // 检查条件匹配
      const matchedConditions = config.entityConditions.map(condition => {
        const typeEntities = detectedEntities.filter(e => e.type === condition.entityType);
        
        for (const entity of typeEntities) {
          const matched = checkCondition(entity.value, condition);
          if (matched.matched) {
            return {
              condition,
              matched: true,
              detectedValue: entity.value,
              reason: matched.reason
            };
          }
        }
        
        return {
          condition,
          matched: false,
          reason: `未检测到${getEntityTypeName(condition.entityType)}实体`
        };
      });

            onTest();
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * 检查单个条件
   */
  const checkCondition = (entityValue: string, condition: EntityCondition): { matched: boolean; reason: string } => {
    const { operator, expectedValue } = condition;
    
    if (!expectedValue.trim()) {
      return { matched: false, reason: '必须选择字段进行比较' };
    }

    // 模拟随路参数（实际应用中这些数据来自通话元数据）
    const mockMetadata: Record<string, string> = {
      '客服姓名': '王经理',
      '客服ID': 'CS001',
      '技能组': '售后服务组',
      '业务线': '保险业务',
      '客户城市': '上海',
      '预约日期': '2024-01-15',
      '自定义字段1': '贵宾客户',
      '自定义字段2': '投诉处理',
      '自定义字段3': '续保业务'
    };

    // 获取随路参数值
    const metadataValue = mockMetadata[expectedValue];
    if (metadataValue === undefined) {
      return { matched: false, reason: `随路参数"${expectedValue}"不存在` };
    }

    switch (operator) {
      case 'equals':
        return {
          matched: entityValue === metadataValue,
          reason: entityValue === metadataValue 
            ? `实体"${entityValue}"与随路参数"${metadataValue}"完全匹配` 
            : `实体"${entityValue}"与随路参数"${metadataValue}"不匹配`
        };
      case 'notEquals':
        return {
          matched: entityValue !== metadataValue,
          reason: entityValue !== metadataValue 
            ? `实体"${entityValue}"与随路参数"${metadataValue}"不匹配（符合要求）` 
            : `实体"${entityValue}"与随路参数"${metadataValue}"匹配（不符合要求）`
        };
      case 'contains':
        return {
          matched: entityValue.includes(metadataValue),
          reason: entityValue.includes(metadataValue) 
            ? `实体"${entityValue}"包含随路参数"${metadataValue}"` 
            : `实体"${entityValue}"不包含随路参数"${metadataValue}"`
        };
      case 'notContains':
        return {
          matched: !entityValue.includes(metadataValue),
          reason: !entityValue.includes(metadataValue) 
            ? `实体"${entityValue}"不包含随路参数"${metadataValue}"（符合要求）` 
            : `实体"${entityValue}"包含随路参数"${metadataValue}"（不符合要求）`
        };
      default:
        return { matched: false, reason: '未知运算符' };
    }
  };

  /**
   * 获取实体类型中文名
   */
  const getEntityTypeName = (type: string): string => {
    const nameMap: Record<string, string> = {
      date: '日期',
      time: '时间',
      province: '省份',
      city: '城市',
      district: '区县',
      idCard: '身份证号码',
      personName: '人名',
      email: '邮箱'
    };
    return nameMap[type] || type;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">🧪 实时测试</h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              onTextChange('客服：您好，我是张小明，联系电话是123456789。\n客户：你好，我的身份证号是110101199001011234。\n客服：好的，请问您的地址是？\n客户：我住在北京市朝阳区，邮箱是*****************。');
              onClearResult();
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* 测试文本输入区 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
              📝 测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {testText.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              id="test-text"
              rows={10}
              className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
              value={testText}
              onChange={(e) => onTextChange(e.target.value)}
              placeholder={`请输入需要测试的对话文本，每行一句话...

示例：
客服：您好，我是张小明，联系电话是123456789
客户：你好，我的身份证号是110101199001011234
客服：好的，请问您的地址是？
客户：我住在北京市朝阳区，邮箱是*****************`}
            />
            {testText.trim() && (
              <button
                onClick={() => {
                  onTextChange('');
                  onClearResult();
                }}
                className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="清空文本"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-4">
            <span>💡 提示：每行代表一句对话，使用"客服："或"客户："开头</span>
          </div>
        </div>

        {/* 快速测试按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={executeTest}
            disabled={!testText.trim() || config.entityConditions.length === 0 || isProcessing}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              {isProcessing ? (
                <>
                  <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>检测中...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>执行测试</span>
                </>
              )}
            </span>
          </button>
          {testResult && (
            <button
              onClick={onClearResult}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>

        {/* 配置提示 */}
        {config.entityConditions.length === 0 && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L5.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-sm text-yellow-800 font-medium">请先在配置区域添加至少一个实体条件</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EntityCheckTester; 