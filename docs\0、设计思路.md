系统角色
	坐席员
		系统的基础用户，其通话被质检。主要权限是查看自己的质检成绩和报告，并发起申诉。
	班组长
		管理一个坐席团队。主要权限是查看团队和组员的质检成绩。
	复核员
		质检工作的核心执行者。主要权限是执行复核任务，对需要复核机审记录进行复核。
	质检主管
		质检工作的管理者。主要权限是制定质检规则，生成质检计划和任务，处理坐席员的申诉。
导航菜单设计
	首页
		坐席员视角
			绩效数据
				平均分数
					在特定时间段内，坐席员所有被质检录音的平均得分。
					平均分数 = 周期内所有质检项得分总和 / 周期内已完成的质检总次数
				团队排名
					坐席员在所属团队中，基于某项关键指标（通常是“平均分数”）的相对位置。
					将团队内所有成员的平均分进行降序排列后，确定该坐席员所处的位置。
				质检合格率
					在特定时间段内，质检结果为“合格”的次数占总质检次数的百分比。
					质检合格率 = ( 周期内质检合格的次数 / 周期内已完成的质检总次数 ) × 100%
				申诉次数
					在特定时间段内，坐席员对质检结果发起的申诉总次数。
					统计周期内发起的申诉流程数量。
				申诉成功率
					在特定时间段内，申诉成功（即质检结果被修改）的次数占总申诉次数的百分比。
					申诉成功率 = ( 周期内申诉成功的次数 / 周期内发起的总申诉次数 ) × 100%
				一次性通过率
					在特定时间段内，首次质检即合格，且后续未发起申诉或申诉未成功的次数占总质检次数的百分比。
					一次性通过率 = ( 首次质检即合格的次数 / 周期内已完成的质检总次数 ) × 100%
				分数趋势
					展示坐席员在连续的时间维度上（如按天、周、月）平均分数的变化情况。
					基于历史“平均分数”数据生成的时间序列图表（如折线图）。
				严重错误项
					在质检标准中被定义为“严重”等级的，一旦触犯会导致质检直接不合格的规则项。
					通过统计在质检周期内，每个具体的“严重错误项”被触发的次数，然后按次数高低进行排名展示。
				高频失分项
					 在质检周期内，坐席员最常被扣分的质检规则项。
					通过汇总所有质检记录中的扣分项，并统计每个扣分项出现的频率或总扣分值，按高低排序后得出结果。
			通知/公告
			近期成绩列表
		班组长视角
			绩效数据
				团队平均质检得分
					在特定时间段内，团队内所有成员的质检得分的平均值。
					团队平均质检得分 = 周期内团队所有成员的质检得分总和 / 周期内团队已完成的质检总次数
				团队质检合格率
					在特定时间段内，团队质检结果为“合格”的次数占团队总质检次数的百分比。
					团队质检合格率 = ( 周期内团队质检合格的总次数 / 周期内团队已完成的质检总次数 ) × 100%
				团队申诉率
					在特定时间段内，团队成员发起的申诉次数占团队总质检次数的百分比。
					团队申诉率 = ( 周期内团队发起的总申诉次数 / 周期内团队已完成的质检总次数 ) × 100%
				团队申诉成功率
					在特定时间段内，团队申诉成功的次数占团队总申诉次数的百分比。
					团队申诉成功率 = ( 周期内团队申诉成功的总次数 / 周期内团队发起的总申诉次数 ) × 100%
				团队成员成绩排名
					将团队内所有成员按照某项关键指标（如“平均分数”）进行排序，展示成员的绩效表现。
				团队高频失分项
					在特定时间段内，整个团队最常被扣分的质检规则项。
					汇总周期内团队所有质检记录中的扣分项，统计每个扣分项出现的总频率或总扣分值，按高低排序后得出结果。
				团队严重错误项
					在质检标准中被定义为“严重”等级的规则项在整个团队中被触发的情况。
					统计在质检周期内，团队成员触发各个“严重错误项”的总次数，然后按次数高低进行排名。
				整体服务质量趋势
					展示团队整体服务质量在连续时间维度上（如按天、周、月）的变化情况。
					基于历史“团队平均质检得分”数据生成的时间序列图表（如折线图或柱状图）
			通知/公告
			团队近期成绩列表
		复核员视角
			绩效数据
				复核总量
					 在特定时间段内，复核员完成的质检/复核任务的总数量。
					统计周期内所有已完成的复核任务数量。
				待复核量
					 当前分配给该复核员，但尚未完成的复核任务数量。
					计算当前指派给该复核员且状态为“待处理”或“进行中”的任务总数。
				日均复核量
					在特定时间段内，复核员平均每个工作日完成的复核任务数量。
					日均复核量 = 周期内复核总量 / 周期内的工作天数
				AI结果纠错数
					在复核过程中，复核员修正（或推翻）AI自动质检结果的次数。这个指标衡量了人工复核的价值。
					统计在周期内，复核员对AI预处理的质检项（如分数、标签等）进行了修改的案例总数。
				工作量趋势
					展示复核员在连续的时间维度上（如按天、周、月）完成复核任务量的变化情况。
					基于历史“复核总量”数据生成的时间序列图表（如折线图）。它通过按时间单位（天/周/月）统计已完成的复核任务数
			通知/公告
			待复核任务列表（部分/快捷入口）
		质检主管视角
			绩效指标
				总质检量
					在特定时间段内，整个质检体系完成的质检任务总数。
					汇总周期内所有已完成的质检记录数量。
				总复核量
					 在特定时间段内，质检团队（复核员）完成的人工审核或复议任务的总数。
					汇总周期内所有由复核员完成的复核任务数量。
				质检平均分
					在特定时间段内，所有被质检的会话的平均得分。
					质检平均分 = 周期内所有质检项的得分总和 / 周期内已完成的质检总次数
				质检合格率
					在特定时间段内，质检结果达到“合格”标准的任务占总质检任务的百分比。
					质检合格率 = ( 周期内质检合格的总次数 / 周期内已完成的质检总次数 ) × 100%
				成绩趋势分析
					展示关键绩效指标（如质检平均分、合格率）在连续时间维度上的变化趋势。
					通过按天、周、月等时间单位计算上述关键指标，并以折线图等形式展现。
				团队成绩排名
					根据关键绩效指标（通常是“质检平均分”），对所有团队进行排序。
					分别计算每个团队的“质检平均分”，然后依据分值高低进行降序排名。
				坐席成绩排名
					根据关键绩效指标（如“质检平均分”），对所有坐席员进行排序。
					分别计算每个坐席员的“平均分数”，然后依据分值高低进行降序排名。
				严重错误项
					在整个组织范围内，统计在质检中被判为“严重错误”的规则项及其发生频率。
					分类汇总在周期内所有质检记录中触发的“严重错误项”，按发生次数进行排名。
				高频失分项
					在整个组织范围内，统计最常导致坐席被扣分的质检规则项。
					 分类汇总在周期内所有质检记录中的具体扣分项，按出现频率或累计扣分值进行排名。
				申诉率
					坐席员对质检结果发起申诉的次数占总质检次数的百分比。
					申诉率 = ( 周期内发起的总申诉次数 / 周期内已完成的质检总次数 ) × 100%
				申诉成功率
					申诉成功（即质检结果被更改）的次数占总申诉次数的百分比。
					申诉成功率 = ( 周期内申诉成功的总次数 / 周期内发起的总申诉次数 ) × 100%
				AI质检准确率
					衡量AI自动质检结果的可靠性，即AI的判断与人工复核最终结论的一致性。
					AI质检准确率 = ( AI质检结果与人工复核结果一致的次数 / 所有被人工复核的AI质检总次数 )×100%
			通知/公告
			待处理申诉列表（部分/快捷入口）
	质检工作台
		我的复核任务
			任务列表
				查询项
					记录编号（文本）
					所属任务（文本）
					被检坐席（文本、姓名或工号）
					所属班组（文本）
					触发复核原因（复核策略，下拉）
					AI初检得分（数字区间）
					最终得分（数字区间）
						只在已完成列表上显示
					质检结果（下拉）
						合格/不合格
					分配时间（日期时间区间）
					操作
						查询
						重置
				列表
					待处理/已完成（TAB页签）
					序号
					记录编号
						QM2507020001
					所属任务
					被检坐席
						显示姓名/工号
					所属班组
					通话时长
					触发复核原因
						可以是多个
					AI初检得分
					最终得分
						只在已完成列表上显示
					质检结果
						合格/不合格
							合格分数线计算
					分配时间
					处理时间
						只在已完成列表上显示
					操作
						待处理
							处理
						已完成
							查看
					分页
			处理明细页
				任务信息
					记录编号
					所属任务
					质检方案
					基准总分
					合格分数线
					分配时间
				通话信息
					被检坐席
						显示姓名/工号
					所属班组
					客户号码
					通话开始时间
					通话时长
				触发复核原因
				质检成绩
					以卡片形式展示最终得分，并提供一个“分数演进”的可视化流程，清晰展示从“AI初检”到“人工复核”的分数变化路径。
				录音播放
					提供一个交互式录音播放器，包含播放/暂停、以及可点击/拖动的进度条，并显示当前和总时长。
				转录文本
					按角色（坐席/客户）和时间戳展示完整的对话流。当前高亮的句子与音频播放进度同步。
					支持点击句子跳转到对应的音频时间点。
					可标记对话中的关键事件（如：负面情绪、关键词、静音时段）。
				评分详情
					以列表形式展示所有质检项。每个项目清晰标明是否“命中”，并显示扣分/得分。
					在复核模式下，评分可被实时修改，总分随之更新。
					每个命中项都可点击并联动音频和转录文本，跳转到问题发生的时间点。
				复核意见
				操作
					提交复核结果
					取消
		申诉处理
			任务列表
				查询项
					记录编号（文本）
					所属任务（文本）
					申请坐席（文本、姓名或工号）
					所属班组（文本）
					AI初检得分（数字区间）
					申请时间（日期时间区间）
					申诉结果（下拉）
						只在已完成列表上显示
					处理时间（日期时间区间）
						只在已完成列表上显示
					最终得分（数字区间）
						只在已完成列表上显示
					质检结果（下拉）
						合格/不合格
					操作
						查询
						重置
				列表
					待处理/已完成（TAB页签）
					序号
					记录编号
					所属任务
					申请坐席
						显示姓名/工号
					所属班组
					通话时长
					AI初检得分
					人工复核得分
						如没经过复核，显示N/A
					最终得分
						只在已完成列表上显示
					质检结果
						合格/不合格
							合格分数线计算
					申请理由
					申请时间
					申请结果
						成功/失败
							只在已完成列表上显示
					处理时间
						只在已完成列表上显示
					操作
						待处理
							处理
						已完成
							查看
					分页
			处理明细页
				任务信息
					记录编号
					所属任务
					质检方案
					基准总分
					合格分数线
					申请时间
				通话信息
					申请坐席
						显示姓名/工号
					所属班组
					客户号码
					通话开始时间
					通话时长
				质检成绩
					以卡片形式展示最终得分，并提供一个“分数演进”的可视化流程，清晰展示从“AI初检”到“人工复核”再到“申诉”的分数变化路径。该流程直观地显示了在每个关键环节（AI初检、人工复核、申诉裁定）的分数，并高亮最终生效的分数来源，帮助理解分数变动的全过程。
				处理时间轴
					以时间线的形式，清晰地展示从“AI初检”到“人工复核”、“坐席申诉”，再到“申诉处理完成”的完整流程和时间点。每个节点都包含关键人物和时间信息，确保处理过程的透明和可追溯。
				录音播放
					提供一个交互式录音播放器，包含播放/暂停、以及可点击/拖动的进度条，并显示当前和总时长。
				转录文本
					按角色（坐席/客户）和时间戳展示完整的对话流。当前高亮的句子与音频播放进度同步。
					支持点击句子跳转到对应的音频时间点。
					可标记对话中的关键事件（如：负面情绪、关键词、静音时段）。
				评分详情
					以列表形式展示所有质检项。每个项目清晰标明是否“命中”，并显示扣分/得分。
					每个命中项都可点击并联动音频和转录文本，跳转到问题发生的时间点。
				复核详情
					复核人
					复核时间
					复核意见
				申诉详情
					只在已完成明细上显示
						处理结果
						处理人
						处理时间
						处理意见
				坐席申诉理由
				处理意见
				操作
					提交处理结果
					取消
		质检成绩管理
			成绩列表
				查询项
					记录编号（文本）
					所属任务（文本）
					坐席（文本、姓名或工号）
						班组长角色可见
					所属班组（文本）
						质检主管角色可见
					申诉状态（下拉）
					申诉结果（下拉）
					最终得分（数字区间）
					质检结果（下拉）
						合格/不合格
					通话开始时间（日期时间区间）
					客户号码（文本）
					操作
						查询
						重置
				列表
					序号
					记录编号
					所属任务
					坐席
						显示姓名/工号
							班组长角色可见
					所属班组
						质检主管角色可见
					客户号码
					通话开始时间
					通话时长
					最终得分
						旁边有一个小icon，鼠标悬浮或点击后，展示出AI: 90, 复核: 80, 申诉: 85 (采纳)这样的演进路径
					质检结果
						合格/不合格
							合格分数线计算
					申诉信息
						可申诉：显示"可申诉"状态标签和"申诉有效期"。
						申诉中：显示"申诉中"状态标签和"申诉时间"。
						已处理：显示"已处理"状态标签、"申诉结果"（成功/失败）和"处理时间"。
						已过期：仅显示"已过期"状态标签。
					操作
						查看
				数据权限
					坐席看自己的数据
					班组长看自己团队的数据
					质检主管看所有团队的数据
			成绩明细页
				任务信息
					记录编号
					所属任务
					质检方案
					基准总分
					合格分数线
					申诉有效期
					申诉状态
				通话信息
					坐席
						显示姓名/工号
					所属班组
					客户号码
					通话开始时间
					通话时长
				质检成绩
					以卡片形式展示最终得分，并提供一个“分数演进”的可视化流程，清晰展示从“AI初检”到“人工复核”再到“申诉”的分数变化路径。该流程直观地显示了在每个关键环节（AI初检、人工复核、申诉裁定）的分数，并高亮最终生效的分数来源，帮助理解分数变动的全过程。
				处理时间轴
					以时间线的形式，清晰地展示从“AI初检”到“人工复核”、“坐席申诉”，再到“申诉处理完成”的完整流程和时间点。每个节点都包含关键人物和时间信息，确保处理过程的透明和可追溯。
				录音播放
					提供一个交互式录音播放器，包含播放/暂停、以及可点击/拖动的进度条，并显示当前和总时长。
				转录文本
					按角色（坐席/客户）和时间戳展示完整的对话流。当前高亮的句子与音频播放进度同步。
					支持点击句子跳转到对应的音频时间点。
					可标记对话中的关键事件（如：负面情绪、关键词、静音时段）。
				评分详情
					以列表形式展示所有质检项。每个项目清晰标明是否“命中”，并显示扣分/得分。
					每个命中项都可点击并联动音频和转录文本，跳转到问题发生的时间点。
				复核详情
					复核人
					复核时间
					复核意见
				申诉详情
					只在已处理状态下显示
						处理结果
						处理人
						处理时间
						处理意见
				操作
					发起申诉
						只在可申诉状态下可用
						只在坐席视角下可用
					取消
		通知中心
			通知类型
				任务提醒
					待复核、待处理申诉
						系统向您分配了 5 条新的复核任务，请尽快处理。
							跳转至 我的复核任务-待处理列表
						您收到一条新的坐席申诉待处理。申诉人：[A班组-张三]，记录编号：[QM2507020003]。
							跳转至 申诉处理-待处理列表
				结果通知
					成绩发布、申诉结果
						您的质检记录 [QM2507020001] 已出分，最终得分 88 分，请查收。
							跳转至该记录的 成绩明细页
						您对记录 [QM2507010095] 提起的申诉已有处理结果：申诉成功。
							跳转至该记录的 成绩明细页
				系统公告
					关于五一假期排班及调休的通知 已发布，请全体员工查看。
						跳转至 公告详情页
			视角
				坐席员
					结果通知
					系统公告
				班组长
					结果通知
					系统公告
				复核员
					任务提醒
					系统公告
				质检主管
					任务提醒
					系统公告
			筛选条件
				通知内容（文本）
				读取状态（下拉）
				通知时间（日期时间区间）
				操作
					搜索
					重置
			操作
				标为已读/全部已读
				删除/清空
			布局
				左侧：通知分类导航
					每个分类旁边实时显示该分类下的 未读通知数量
				右侧：主内容区
					筛选条件
					通知列表
						一键已读/清空
					列表项
						已读/未读标记
						通知类型
						通知内容
						发送时间
						操作
							标记已读/未读
							删除
						分页
	质检管理
		质检规则管理
			规则列表
				查询项
					规则名称（文本）
					状态（下拉）
						启用/禁用
					重要程度（下拉）
						轻度违规/中度违规/严重违规
					规则类型（下拉）
					规则算子（下拉）
						关键词检查/正则表达式/通话静音检查/语速检查/抢话/打断/录音时长检测/非正常挂机/非正常接听/语句数检测/角色判断/能量检测/大模型检查
					人工复核（下拉）
						需要/不需要
				列表
					序号
					规则名称
						包括重要程度和规则描述
					规则类型
					人工复核
					规则算子
						可以显示组成规则的所有算子
					创建人
					最后修改时间
					操作
						编辑
						启用/禁用
						删除
				操作
					新建规则
			新建/编辑页
				表单页
					基本信息
						规则名称（文本）
						规则类型（下拉）
							合规风险/服务规范/服务质量/服务效率/服务红线/信息安全/客户体验/信息提取/销售合规
							支持自定义创建
						重要程度（下拉）
							轻度违规/中度违规/严重违规
						人工复核（下拉）
							需要/不需要
						规则描述（多行文本）
					条件配置
						检测内容（下拉）
							以下条件全部满足
							以下条件任一满足
								当没有条件或只有一个条件时不可用
							以下条件任一不满足
								当没有条件或只有一个条件时不可用
							自定义条件之间的逻辑关系
								选择后出现文本框，提示“请输入以下项的逻辑关系，如 (a&&b)||(a&&c)”
						检测条件
							操作
								添加检查条件
									可以添加多个条件（规则算子）
									点击后弹出规则算子选择菜单
										文字检查
											关键词检查
												
											正则表达式
												
										语音检查
											通话静音检查
												
											语速检查
												
											抢话/打断
												
											录音时长检测
												
											非正常挂机
												
											非正常接听
												
											语句数检测
												
											角色判断
												
											能量检测
												
										模型检查
											大模型检查
												
									选择规则算子后，加入一条该规则算子的参数配置面板
				操作
					保存
					取消
		质检方案管理
			方案列表
				查询项
					方案名称（文本）
					状态（下拉）
						启用/禁用
				列表
					序号
					方案名称
					申诉期限
					总分/合格线
					包含规则数
					规则列表
					创建人
					最后修改时间
					操作
						编辑
						启用/禁用
						删除
				操作
					新建方案
			新建/编辑页
				表单页
					基本信息
						方案名称
						基准总分
						合格分数线
						申诉期限
						方案描述
					规则配置
						操作
							从规则库添加规则
								弹窗
									搜索规则
									规则列表
									操作
										确认添加
										取消
							添加规则后可以设置该规则的加分/减分
								操作
									删除
				操作
					保存
					取消
		质检计划管理
		质检任务管理
		复核策略管理
		质检词库管理
			列表页
				查询项
					词库名称
					状态
				列表
					序号
					词库名称
					词条数量
					创建人
					创建时间
					操作
						编辑
						启用/禁用
						导出
						删除
						查看词条
							弹窗
								列表
									序号
									词条
									创建时间
									更新时间
								操作
									新建
									编辑
									删除
									导入
									导出
			新建/编辑页
				表单页
					词库名称（文本）
					描述（多行文本）
				操作
					保存
					取消
		质检明细查询
			列表页
				查询项
					记录编号
					所属任务
					坐席
					所属班组
					申诉状态
					申诉结果
					最终得分
					质检结果
					通话开始时间
					客户号码
				列表
					序号
					记录编号
					所属任务
					坐席
					所属班组
					客户号码
					通话开始时间
					通话时长
					最终得分
					质检结果
					申诉信息
					操作
			明细页
				任务信息
				通话信息
				质检成绩
				处理时间轴
				录音播放
				转录文本
				评分详情
				复核详情
				申诉详情
	统计分析
		质检运营总览
			 从最高维度宏观监控整体质检工作的健康度、AI运行效率和核心风险，为管理决策提供数据支持。
			**统计维度 (Filters):**
    *   `时间范围` (日/周/月/自定义)
    *   `班组/团队`
    *   `质检方案`
			**报表内容:**
    *   **核心指标卡 (KPI Cards):**
        *   `AI总质检量`: 周期内AI完成初检的通话总量。
        *   `总复核量`: 周期内人工完成复核的总量。
        *   `复核率`: `总复核量` / `AI总质检量`，反映人工审核的投入比例。
        *   `整体平均分`: 所有质检通话的最终得分（复核过的以复核分为准）的平均值。
        *   `整体合格率`: 最终得分达到合格标准的通话占比。
        *   `整体申诉率`: `总申诉数` / `AI总质检量`。
    *   **运营趋势分析 (Trend Analysis):**
        *   `质量与效率趋势图`: 按日/周/月，用双轴图展示`整体平均分`（折线）和`AI总质检量`（柱状图）的变化。
    *   **问题风险聚焦 (Risk Focus):**
        *   `高频失分规则 TOP 10`: 全局范围内，导致坐席失分次数最多的规则。
        *   `严重错误规则 TOP 10`: 全局范围内，被标记为“严重错误”且触发次数最多的规则。
        *   `团队平均分排名`: 各团队的平均分、合格率对比排名（柱状图）。
		服务质量深度分析
			下钻到具体的质检规则维度，分析服务质量的短板，并定位到具体的团队和个人，为精准培训和辅导提供依据。
			**统计维度 (Filters):**
    *   `时间范围`
    *   `质检方案`
    *   `班组/团队`
    *   `坐席`
			**报表内容:**
    *   **规则表现总览 (Rule Performance Overview):**
        *   以表格形式列出所有质检规则，每条规则包含以下指标：`规则名称`、`平均得分率` (`规则得分/规则总分`)、`触发次数`、`申诉次数`、`申诉成功率`。
    *   **规则下钻分析 (Rule Drill-Down):**
        *   点击任一规则，可下钻进入该规则的专题分析页面：
            *   `规则得分趋势图`: 按时间展示该规则平均得分率的变化。
            *   `团队表现排名`: 在该规则上得分最低的团队排名。
            *   `坐席表现排名`: 在该规则上得分最低的坐席排名。
            *   `典型问题录音`: 展示在该规则上失分最严重的通话案例。
    *   **坐席/团队横向对比:**
        *   `成员表现分布图`: 用箱线图或得分分段（如0-60, 60-80, 80-100）直方图，展示团队内成员的分数分布，快速识别高绩效和低绩效坐席。
		复核工作分析报告
			从**工作量、复核结果、AI差异**三个核心维度，全面评估和监控复核团队的工作状态和质量。
			**统计维度 (Filters):**
*   `时间范围`: (日/周/月/自定义) - **核心筛选**
*   `复核员`: (可多选，或选择“全部”) - 用于查看个人或团队整体情况
*   `被检班组/团队`: (可多选) - 用于分析对特定团队的复核情况
*   `触发复核原因`: (可多选，如：低分、关键词触发、随机抽检) - 用于分析不同复核策略的效果
			**1. 复核工作量概览 (Review Workload Overview)**
*   **核心指标卡 (KPI Cards):**
    *   `总复核量`: 周期内完成的复核任务总数。
    *   `待复核量`: 当前分配给所选复核员且未完成的任务数。（实时数据）
    *   `日均复核量`: `总复核量` / `周期内工作天数`。
    *   `复核率`: `总复核量` / `同期AI总质检量`。反映人工审核的覆盖广度。
*   **复核任务完成趋势 (Review Completion Trend):**
    *   **是什么**: 一个柱状图，按天或周展示所选时间范围内，每天/每周完成的复核任务数量。
    *   **为什么**: 直观展示工作量的波动，帮助主管了解任务分配是否均匀，复核员工作负荷是否稳定。
**2. 复核结果分析 (Review Outcome Analysis)**
*   **复核后成绩分布 (Score Distribution after Review):**
    *   **是什么**: 一个直方图，展示复核后最终得分的分布情况（如：0-59分，60-79分，80-89分，90-100分各有多少条）。
    *   **为什么**: 帮助主管快速了解整体的服务质量水平（以人工复核为准）。如果低分段占比较高，说明服务质量存在普遍问题。
*   **复核后合格率分析 (Pass Rate Analysis after Review):**
    *   **是什么**: 一个饼图或指标卡，清晰展示复核后的 `质检合格率`（复核后合格数 / 总复核量） 和 `不合格率`。
    *   **为什么**: 这是最直接的质量衡量标准。可以与全公司的整体合格率对比，看复核样本的质量水平。
*   **复核后高频失分/严重错误项 (Top Problematic Rules after Review):**
    *   **是什么**: 两个列表，分别展示在复核确认的记录中，最常被扣分的规则项和最常触发的严重错误项。
    *   **为什么**: 聚焦于经人工确认的、最核心的服务短板和风险点，比分析所有AI质检记录得出的结果更可靠，是培训和流程优化的首要依据。
**3. AI与人工复核差异洞察 (AI vs. Manual Review Discrepancy Insights)**
*   **AI结果纠错率 (AI Correction Rate):**
    *   **是什么**: 指标卡，计算公式为 `(复核分数与AI初检分数不一致的记录数 / 总复核量) × 100%`。
    *   **为什么**: **这是衡量人工复核价值的核心指标**。高纠错率说明AI质检有较多误判或漏判，人工复核非常必要。
*   **分数调整分析 (Score Adjustment Analysis):**
    *   **是什么**:
        *   `分数调增数`: 复核分数 > AI初检分数的记录总数。
        *   `分数调减数`: 复核分数 < AI初检分数的记录总数。
        *   `平均调整幅度`: 所有复核记录的 `|复核分数 - AI初检分数|` 的平均值。
    *   **为什么**: 帮助理解AI是倾向于“误杀”（分数给低了）还是“放过”（分数给高了），以及偏差的严重程度。
*   **最大差异规则排行 (Top Discrepancy Rules):**
    *   **是什么**: 一个列表，找出在哪些规则上，AI的判断（是否命中）和人工复核的判断最常出现不一致。
    *   **为什么**: **直接定位AI模型最需要优化的地方**。比如，AI频繁命中的“服务热情”规则被人工复核大量取消，说明AI对情绪的判断标准可能过于严苛。
*   **复核员一致性快照 (Reviewer Consistency Snapshot):**
    *   **是什么**: 当筛选维度选择“全部复核员”时，显示一个表格，列出每位复核员的 `复核量`、`平均复核分数` 和 `AI结果纠错率`。
    *   **为什么**: 在不引入复杂指标的情况下，通过对比不同复核员的平均给分和纠错行为，初步判断其工作标准是否存在显著差异。例如，某个复核员的平均分远高于或低于其他人，或其纠错率异常高/低，都值得主管关注。
		坐席申诉洞察报告
			监控申诉情况，保障质检公平性，并从申诉数据中反向挖掘有争议的AI规则或存在标准差异的复核环节。
			**统计维度 (Filters):**
    *   `时间范围`
    *   `班组/团队`
    *   `坐席`
			**报表内容:**
    *   **申诉概览 (Appeal Overview):**
        *   `总申诉数`、`申诉率`、`申诉成功率`。
    *   **申诉趋势分析 (Appeal Trend):**
        *   按周/月展示 `申诉数量` 和 `申诉成功率` 的变化趋势。
    *   **问题定位分析 (Problem-finding Analysis):**
        *   `高申诉成功率规则排行`: 申诉成功次数最多的质检规则排名。**这直接指向了AI最不准确或定义最模糊的规则**。
        *   `高申诉率团队/坐席排行`: 申诉次数和申诉率最高的团队/坐席，可能反映了团队文化或对质检结果的接受度。
	系统管理
		数据源管理
		大模型管理
		ASR管理
		公告管理