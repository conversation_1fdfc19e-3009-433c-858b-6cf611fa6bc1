import React from 'react';
import { ChevronRight } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { Typography, Breadcrumb, Button } from 'antd';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

interface BreadcrumbItem {
  name: string;
  path: string;
}

interface PageHeaderProps {
  title: React.ReactNode;
  description?: string;
  badge?: string;
  tag?: string;
  icon?: React.ElementType;
  actions?: React.ReactNode;
  breadcrumbs?: BreadcrumbItem[];
  subtitle?: React.ReactNode;
  onBack?: () => void;
  className?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({ title, description, badge, tag, icon: Icon, actions, breadcrumbs, subtitle, onBack }) => {
  return (
    <div className="bg-white border-b border-gray-200">
      <div className="max-w-full mx-auto px-6 py-4">
        {breadcrumbs && breadcrumbs.length > 0 && (
          <Breadcrumb className="mb-2">
            <Breadcrumb.Item>
              <Link to="/">
                <HomeOutlined />
              </Link>
            </Breadcrumb.Item>
            {breadcrumbs.map((crumb, index) => (
              <Breadcrumb.Item key={index}>
                {crumb.path ? <Link to={crumb.path}>{crumb.name}</Link> : crumb.name}
              </Breadcrumb.Item>
            ))}
          </Breadcrumb>
        )}
        <div className="flex items-center justify-between">
          <div>
            {onBack && (
              <Button icon={<ArrowLeftOutlined />} type="text" onClick={onBack} className="mr-2" />
            )}
            <Title level={2} className="!mb-1 text-3xl font-bold text-gray-800">
              {Icon && <Icon className="w-6 h-6 mr-3 text-gray-600" />}
              {title}
            </Title>
            {description && (
              <Paragraph className="!mb-0" type="secondary">
                {description}
              </Paragraph>
            )}
          </div>
          <div className="flex items-center space-x-4">
            {tag && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    {tag}
                </span>
            )}
            {badge && (
                <span className="text-sm text-gray-500">
                    算子类型：{badge}
                </span>
            )}
            {actions && <div>{actions}</div>}
          </div>
        </div>
        {subtitle && <Paragraph className="!mb-0" type="secondary">{subtitle}</Paragraph>}
      </div>
    </div>
  );
};
