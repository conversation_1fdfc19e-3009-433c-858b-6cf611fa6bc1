
---

### 页面二十：数据源管理 (DataSourceManagementPage.tsx)

#### 1. 核心定位与目标
该页面是**系统数据接入的统一入口**。其核心目标是让系统管理员能够配置和管理所有外部数据的来源，确保系统能源源不断地、可靠地获取用于质检的原始数据（如通话录音、通话元数据等）。这是整个智能质检流程的**起点**。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "数据源管理"
*   **副标题**: "配置和管理所有与外部系统对接的数据源"
*   **图标**: `Database` (数据库图标)，象征着数据的存储与来源。
*   **徽章**: 显示已配置的数据源总数。
*   **核心操作**: `新建数据源` 按钮，点击后弹出创建数据源的抽屉表单。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 在多个数据源配置中进行快速查找。
*   **筛选字段**: `数据源名称`, `数据源类型`, `连接状态`。

**c. 数据源列表表格 (`Table`)**
*   **布局**: 以表格形式展示所有已配置的数据源。
*   **表格列**:
    *   `序号`
    *   `数据源名称`: 显示用户为该数据源设置的名称，并根据类型显示不同图标（如`HardDrive`为目录，`Server`为API）。
    *   `数据源类型`: 用不同颜色的徽章显示“监控目录”、“API接口”或“数据库直连”。
    *   `连接状态`: **核心监控信息**。用带有图标和颜色区分的徽章显示“正常”、“异常”或“未测试”，让管理员能快速发现有问题的连接。
    *   `创建人`
    *   `创建时间`
    *   `操作`: 提供对单个数据源的管理功能。
        *   **编辑 (`Edit`图标)**: 修改数据源配置。
        *   **测试连接 (`TestTube`图标)**: 手动触发一次连接测试，验证配置是否正确。
        *   **删除 (`Trash2`图标)**: 删除该数据源配置。

**d. 分页组件 (`UnifiedPagination`)**
*   用于浏览和导航数据源列表。

**e. 创建/编辑数据源的抽屉表单 (`Drawer`)**
这是该页面的核心交互部分，是一个分步骤的、非常详细的配置向导。

*   **表单步骤**:
    1.  **第一步：选择数据源类型**
        *   以图文并茂的卡片形式，让用户选择要创建的数据源类型：
            *   **监控目录**: 用于接入通过SFTP、FTP等方式提供的文件（如录音文件和CSV元数据文件）。
            *   **API接口**: 用于通过HTTP/S接口从外部系统拉取数据。
            *   **数据库直连**: 用于直接连接到业务数据库查询数据。

    2.  **第二步：填写配置信息** (根据第一步的选择动态渲染)
        *   `数据源名称`: 用户自定义的名称。
        *   **监控目录配置**:
            *   连接协议 (SFTP/FTP/SMB)
            *   服务器地址、端口
            *   **录音文件路径** 和 **元数据文件路径** (分离设计，非常灵活)
            *   认证方式（用户名/密码 或 密钥文件）
        *   **API接口配置**:
            *   基础URL
            *   录音文件处理策略（同步拉取存储 或 引用外部链接）
            *   认证方式（无认证、API Key、Bearer Token、OAuth 2.0）及相关配置。
        *   **数据库直连配置**:
            *   数据库类型 (MySQL, PostgreSQL等)
            *   服务器地址、端口、数据库名
            *   用户名、密码
            *   **SQL查询模板**: 这是一个非常强大的功能，允许管理员编写自定义SQL来精确地拉取需要质检的数据，并支持 `{{last_execution_time}}` 这样的变量来实现增量同步。

    3.  **第三步：配置字段映射**
        *   **目的**: 将从外部数据源获取的字段（其名称和结构各不相同）映射到系统内部的**标准字段**上。这是实现数据标准化的关键步骤。
        *   **界面**: 以一个表格形式呈现，左侧是系统的标准字段（如“录音唯一ID”、“通话开始时间”、“坐席工号”），右侧是需要用户填写的**外部字段名或路径**。
        *   **路径语法**: 支持点分路径 (`data.user.id`) 和数组索引 (`items[0].name`)，可以处理复杂的嵌套JSON/CSV数据。
        *   **示例值预览**: 在测试连接成功后，系统可以获取一份样本数据，并根据用户填写的路径，自动填充“示例值”一列，让用户可以直观地验证自己的映射路径是否正确。

*   **抽屉底部操作**:
    *   `测试连接`: 在表单填写过程中随时可以测试当前配置是否能成功连接并获取数据。
    *   `保存` 和 `取消`。

#### 3. 核心交互与操作
*   **适配异构数据**: 该页面的核心价值在于其强大的**数据适配能力**。通过支持多种数据源类型和灵活的字段映射，系统可以接入几乎任何来源的客服数据，具备极高的通用性。
*   **标准化**: “字段映射”是实现数据标准化的核心，它将五花八门的外部数据“翻译”成系统能够理解的统一语言，为后续所有质检和分析流程奠定了基础。
*   **可用性保障**: “连接测试”和“样本数据预览”功能极大地提升了配置的易用性和可靠性，管理员可以即时验证配置的正确性，避免了“盲配”带来的问题。

---