import React, { useState, useEffect } from 'react';
import { ParamCondition } from './RecordingParamsCheckConfig';

/**
 * 测试结果的接口定义
 */
export interface TestResult {
  matched: boolean;
  matchedConditions: string[];
  details: string;
}

/**
 * 测试组件Props的接口定义
 */
export interface RecordingParamsCheckTesterProps {
  config: {
    conditions: ParamCondition[];
  };
  testParams: object;
  setTestParams: React.Dispatch<React.SetStateAction<object>>;
  testResult: TestResult | null;
  setTestResult: React.Dispatch<React.SetStateAction<TestResult | null>>;
  runTest: () => void;
}

/**
 * 随录参数检查 - 实时测试组件
 * @param {RecordingParamsCheckTesterProps} props
 * @constructor
 */
export const RecordingParamsCheckTester: React.FC<RecordingParamsCheckTesterProps> = ({
  testParams,
  setTestParams,
  testResult,
  setTestResult,
  runTest
}) => {
  const [testParamsString, setTestParamsString] = useState(JSON.stringify(testParams, null, 2));
  const [error, setError] = useState('');
  
  useEffect(() => {
    setTestParamsString(JSON.stringify(testParams, null, 2));
  }, [testParams]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    setTestParamsString(text);
    try {
      const parsed = JSON.parse(text);
      setTestParams(parsed);
      setError('');
    } catch (err) {
      setError('JSON格式错误');
    }
  };

  const resetSample = () => {
    const sample = { "业务线": "华东", "客户级别": "普通", "tags": "这个客户有投诉历史" };
    setTestParams(sample);
    setTestParamsString(JSON.stringify(sample, null, 2));
    setTestResult(null);
    setError('');
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={resetSample}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <label htmlFor="test-params" className="block text-sm font-medium text-gray-700">
            📝 测试随录参数 (JSON格式)
          </label>
          <div className="relative">
            <textarea
              id="test-params"
              rows={8}
              className={`block w-full px-4 py-3 text-sm font-mono border rounded-lg focus:ring-2 resize-none transition-all duration-200 ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'}`}
              value={testParamsString}
              onChange={handleTextChange}
              placeholder='请输入JSON格式的随录参数...'
            />
             {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={runTest}
            disabled={!!error || !testParamsString.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            执行测试
          </button>
          {testResult && (
            <button
              onClick={() => setTestResult(null)}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>
      </div>

      {testResult && (
        <div className="mt-8 space-y-6">
          <div className="border-t border-gray-200 pt-6">
             <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                      {testResult.matched ? '✓' : '✗'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                      {testResult.matched ? '规则命中' : '规则未命中'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 