import React from 'react';

/**
 * 非正常挂机案例接口
 */
interface AbnormalHangupCasesProps {
  cases: {
    name: string;
    description: string;
    config: any;
    text: string;
    expectedResult: string;
  }[];
  loadCase: (caseConfig: any, caseText: string) => void;
}

/**
 * 非正常挂机案例库组件
 * @param cases 案例列表
 * @param loadCase 加载案例的函数
 * @constructor
 */
const AbnormalHangupCases: React.FC<AbnormalHangupCasesProps> = ({ cases, loadCase }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
          <span>📂</span>
          <span>案例库</span>
        </h2>
      </div>
      <div className="space-y-4 max-h-[600px] overflow-y-auto">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200 hover:border-blue-300">
            <div className="flex justify-between items-start">
              <div className="space-y-1">
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500">{c.description}</p>
                <p className="text-xs text-blue-500">预期结果：{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c.config, c.text)}
                className="flex-shrink-0 text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AbnormalHangupCases; 