import React, { useState } from 'react';
import InterruptConcepts from './interruptCheck/InterruptConcepts';
import InterruptConfig from './interruptCheck/InterruptConfig';
import InterruptTester from './interruptCheck/InterruptTester';
import InterruptTips from './interruptCheck/InterruptTips';

/**
 * 抢话检查主页面组件
 * 整合所有子组件并管理状态
 */
const InterruptCheckPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState({
    detectionRole: 'all' as 'agent' | 'customer' | 'all',
    detectionScope: 'full' as 'full' | 'range',
    rangeStart: 1,
    rangeEnd: 1,
    interruptTime: 1000,
    minWordCount: 4,
    delayTime: 0
  });

  // 测试文本状态
  const [testText, setTestText] = useState('');

  // 预设案例
  const cases = [
    {
      name: '客服抢话检测',
      description: '检测客服是否在客户说话时出现抢话现象',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        interruptTime: 2000,
        minWordCount: 4,
        delayTime: 0
      },
      testText: `[00:01] 客户：您好，我想咨询一下你们的产品
[00:02] 客服：好的，我来为您介绍一下我们的产品特点
[00:03] 客户：我主要想了解
[00:04] 客服：我们的产品有以下几个优势`,
      expectedResult: '应该命中（客服在客户未说完时就开始说话）'
    },
    {
      name: '延时判定检测',
      description: '使用延时判定来过滤短暂的语音重叠',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        interruptTime: 2500,
        minWordCount: 4,
        delayTime: 1000
      },
      testText: `[00:01] 客服：请问您需要什么帮助
[00:02] 客户：我想问一下
[00:03] 客服：抱歉打断您，我们现在有优惠活动`,
      expectedResult: '应该命中（说话重叠超过延时判定阈值）'
    },
    {
      name: '字数限制检测',
      description: '检测是否满足最小字数要求',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        interruptTime: 2000,
        minWordCount: 6,
        delayTime: 0
      },
      testText: `[00:01] 客户：我想了解产品价格
[00:02] 客服：好的
[00:03] 客户：具体是
[00:04] 客服：我们的产品价格是这样的`,
      expectedResult: '不应命中（抢话句子未达到最小字数要求）'
    },
    {
      name: '指定范围检测',
      description: '在对话的特定范围内检测抢话',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'range' as const,
        rangeStart: 2,
        rangeEnd: 4,
        interruptTime: 2000,
        minWordCount: 4,
        delayTime: 0
      },
      testText: `[00:01] 客服：您好，欢迎咨询
[00:02] 客户：我想了解一下
[00:03] 客服：好的，让我来为您介绍
[00:04] 客户：等一下，我还没说完
[00:05] 客服：抱歉，您请说`,
      expectedResult: '应该命中（指定范围内检测到抢话）'
    },
    {
      name: '客户抢话检测',
      description: '检测客户是否在客服说话时出现抢话现象',
      config: {
        detectionRole: 'customer' as const,
        detectionScope: 'full' as const,
        interruptTime: 2000,
        minWordCount: 4,
        delayTime: 0
      },
      testText: `[00:01] 客服：让我为您介绍一下我们的产品特点
[00:02] 客户：好的，我主要关注价格方面
[00:03] 客服：我们的产品价格
[00:04] 客户：这个价格对我来说有点贵`,
      expectedResult: '应该命中（客户在客服未说完时就开始说话）'
    },
    {
      name: '复合条件检测',
      description: '同时使用多个条件进行抢话检测',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'range' as const,
        rangeStart: 1,
        rangeEnd: 5,
        interruptTime: 2500,
        minWordCount: 5,
        delayTime: 500
      },
      testText: `[00:01] 客服：请问您对我们的产品有什么想了解的
[00:02] 客户：我想知道价格
[00:03] 客服：我们目前有两种定价方案
[00:04] 客户：那我选择第一种方案吧
[00:05] 客服：这个方案可能不太适合您的需求`,
      expectedResult: '不应命中（虽有重叠但未超过延时判定阈值）'
    }
  ];

  // 加载案例
  const loadCase = (caseConfig: any, caseText: string) => {
    setConfig({ ...config, ...caseConfig });
    setTestText(caseText);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🎙️ 抢话检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测对话中是否存在抢话现象，支持多种检测模式和判定规则
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                🚀 基础入门
              </span>
              <span className="text-sm text-gray-500">
                算子类型：语音检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            <InterruptConcepts />
            <InterruptConfig config={config} setConfig={setConfig} />
            <InterruptTester config={config} text={testText} setText={setTestText} />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <span>📂</span>
                  <span>案例库</span>
                </h2>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  参考示例
                </span>
              </div>
              <div className="space-y-4">
                {cases.map((c, index) => (
                  <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200 hover:border-blue-300 transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <h4 className="font-semibold text-gray-800">{c.name}</h4>
                        <p className="text-xs text-gray-500">{c.description}</p>
                        <p className="text-xs text-blue-500">预期结果：{c.expectedResult}</p>
                      </div>
                      <button 
                        onClick={() => loadCase(c.config, c.testText)}
                        className="flex-shrink-0 text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
                      >
                        加载
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <InterruptTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>从简单开始</strong>：先使用基础的抢话时间设置熟悉检测效果</p>
                <p>• <strong>理解检测逻辑</strong>：掌握抢话时间、字数限制和延时判定的作用</p>
                <p>• <strong>注意录音要求</strong>：双声道录音效果更好，单声道可能影响检测准确性</p>
                <p>• <strong>参数调优</strong>：根据实际业务场景调整抢话时间阈值（建议2000-3000毫秒）</p>
                <p>• <strong>综合应用</strong>：结合语速检查等其他语音检查功能优化质检方案</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterruptCheckPage;