
---

### 页面九：质检规则管理 (FinalRuleLibraryPage.tsx)

#### 1. 核心定位与目标
该页面是整个质检体系的**原子能力配置中心**。其核心目标是提供一个统一的界面，让质检主管或策略分析师能够创建、管理和维护所有独立的、可复用的**质检规则 (Rule)**。这些规则是构成后续“质检方案”的基础模块，如同乐高积木中的每一块积木。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "质检规则管理"
*   **副标题**: "在这里创建和管理所有独立的质检规则，它们是构成质检方案的基础。"
*   **图标**: `Settings` (设置图标)，代表这是进行配置和定义的页面。
*   **徽章**: 显示关键统计信息，如“规则管理”。
*   **核心操作**:
    *   **创建规则 (`Plus`图标)**: 这是页面的主要入口，点击后会弹出创建规则的表单（`FinalCreateRuleForm.tsx`）。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 帮助用户在可能非常庞大的规则库中快速找到特定的规则。
*   **筛选字段**:
    *   `规则名称`: 按名称进行模糊搜索。
    *   `状态`: 筛选“已启用”或“已禁用”的规则。
    *   `重要程度`: 按“轻度违规”、“中度违规”、“严重违规”筛选。
    *   `规则类型`: 这是一个业务分类，如“合规风险”、“服务规范”等。
    *   `规则算子`: 这是一个技术分类，指规则使用的具体检测技术，如“关键词检查”、“正则表达式检查”、“大模型检查”等。

**c. 规则列表表格 (`Table`)**
*   **布局**: 以表格形式展示规则库中的所有规则。
*   **表格列**:
    *   `序号`
    *   `规则名称`: 显示规则的名称，旁边通常会附带一个表示其**重要程度**的彩色徽章（如红色代表“严重违规”）。下方可能会有对规则的简短描述。
    *   `规则类型`: 显示规则的业务分类。
    *   `规则算子`: **核心信息**，用不同颜色和图标的标签展示构成该规则所使用的具体技术（算子）。例如，一个规则可能同时使用了“关键词检查”和“大模型检查”，这里会并排显示两个标签。这让管理者能快速了解一个规则的技术复杂度。
    *   `创建人`
    *   `最后修改时间`
    *   `操作`: 提供一系列对单条规则的操作按钮。
        *   **编辑 (`Edit`图标)**: 点击后弹出与创建类似的表单，用于修改规则配置。
        *   **启用/禁用 (`Power`/`PowerOff`图标)**: 快速切换规则的激活状态。被禁用的规则在创建“质检方案”时将不可用。
        *   **删除 (`Trash2`图标)**: 永久删除一个规则。

*   **空状态**: 如果没有规则或筛选结果为空，会显示友好的提示信息。

**d. 分页组件 (`UnifiedPagination`)**
*   用于浏览和导航大量的规则。

**e. 创建/编辑规则的抽屉表单 (`FinalCreateRuleForm.tsx` & `FinalCreateRuleProForm.tsx`)**
这是该页面最重要的交互部分，通过一个抽屉式（Drawer）或模态框（Modal）表单来完成。

*   **表单来源**: 系统提供了两种创建方式：
    1.  **标准表单 (`FinalCreateRuleForm.tsx`)**: 通过结构化的表单项来配置规则。
    2.  **Pro版表单 (`FinalCreateRuleProForm.tsx`)**: 引入**自然语言创编**功能，用户可以用日常语言描述规则（例如：“检查客户提到投诉后，坐席是否在30秒内安抚”），系统利用大语言模型（LLM）自动将其解析为结构化的条件。这是一个非常高级和用户友好的功能。

*   **表单内容 (以`FinalCreateRuleForm`为例)**:
    *   **基本信息**:
        *   `规则名称`: 必填。
        *   `规则描述`: 对规则的详细说明。
        *   `规则类型`: 业务分类下拉选择。
        *   `重要程度`: 违规等级下拉选择。
    *   **条件配置**: 这是规则的核心逻辑定义部分。
        *   **逻辑关系**: 可以选择条件之间的关系是“全部满足(AND)”还是“任一满足(OR)”。
        *   **条件列表**: 用户可以动态添加多个“检测条件”。
        *   **单个条件**: 每个条件由**“算子类型”+“配置项”**组成。
            *   用户首先从下拉菜单中选择一个**算子**（如“关键词检查”）。
            *   然后界面会动态渲染出该算子对应的配置项。例如，选择了“关键词检查”，就会出现配置“检测角色”、“检测范围”、“关键词列表”、“分析方式”等的输入框。
            *   这里的设计体现了高度的灵活性和扩展性，未来可以方便地增加新的算子类型。
    *   **操作按钮**: "保存" 和 "取消"。

#### 3. 核心交互与操作
*   **定义与配置**: 这是规则的“生产车间”，用户在这里定义质检的最小判断单元。
*   **生命周期管理**: 用户可以对规则进行创建、编辑、启用/禁用、删除的全生命周期管理。
*   **智能化创建**: 通过`ProForm`，系统将复杂的逻辑配置过程简化为自然语言对话，极大地降低了非技术人员的使用门槛。
*   **模块化与复用**: 在这里创建的每一个规则，都可以在多个不同的“质检方案”中被复用，提高了配置效率。

---