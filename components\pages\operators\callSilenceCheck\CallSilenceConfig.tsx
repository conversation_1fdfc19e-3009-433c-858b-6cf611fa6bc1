import React from 'react';

interface CallSilenceConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  silenceLogic: 'different' | 'any' | 'same';
  timeComparison: 'greater' | 'less' | 'between';
  timeValue: number;
  timeValueEnd: number;
}

interface CallSilenceConfigProps {
  config: CallSilenceConfig;
  setConfig: (config: CallSilenceConfig) => void;
}

/**
 * 通话静音检查配置组件
 * 提供所有配置选项和交互界面
 */
const CallSilenceConfigComponent: React.FC<CallSilenceConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      {/* 配置规则说明 */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📋</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>客服</strong>：检测客服语句的静音情况</li>
                    <li>• <strong>客户</strong>：检测客户语句的静音情况</li>
                    <li>• <strong>所有角色</strong>：检测所有角色的静音情况</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>全文</strong>：检测整个通话的静音情况</li>
                    <li>• <strong>指定范围</strong>：只检测第X~Y句之间的静音</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🔍 检查逻辑</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>不同角色之间</strong>：前后语句角色不同的静音</li>
                    <li>• <strong>不区分角色</strong>：任意位置的静音段</li>
                    <li>• <strong>相同角色</strong>：同角色连续语句间的静音</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">⏱️ 时间比较</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>大于</strong>：静音时长超过设定值</li>
                    <li>• <strong>小于</strong>：静音时长低于设定值</li>
                    <li>• <strong>区间</strong>：静音时长在指定区间内</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 前置条件 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="none">无</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-20 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeStart}
                onChange={(e) => setConfig({ ...config, rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeEnd}
                onChange={(e) => setConfig({ ...config, rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="指定检测的句子范围，例如第1~3句表示只检测前3句对话中的静音"
              >
                ?
              </span>
            </div>
          </div>
        )}

        {/* 检查逻辑 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">检查逻辑</label>
          <div className="flex-1 space-y-4">
            {/* 静音类型选择 */}
            <div className="flex items-center space-x-2">
              <select 
                className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.silenceLogic}
                onChange={(e) => setConfig({ ...config, silenceLogic: e.target.value as any })}
              >
                <option value="different">不同角色之间</option>
                <option value="any">不区分角色</option>
                <option value="same">相同角色</option>
              </select>
              <span className="text-sm text-gray-700">静音段时长</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="静音时长等于后一句开始时间减去前一句结束时间"
              >
                ?
              </span>
            </div>
            
            {/* 时间比较配置 */}
            <div className="flex items-center space-x-2">
              <select 
                className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.timeComparison}
                onChange={(e) => setConfig({ ...config, timeComparison: e.target.value as any })}
              >
                <option value="greater">大于</option>
                <option value="less">小于</option>
                <option value="between">区间</option>
              </select>
              
              {config.timeComparison === 'between' ? (
                <>
                  <input
                    type="number"
                    min="0"
                    step="0.1"
                    className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={config.timeValue}
                    onChange={(e) => setConfig({ ...config, timeValue: parseFloat(e.target.value) || 0 })}
                  />
                  <span className="text-sm text-gray-700">~</span>
                  <input
                    type="number"
                    min="0"
                    step="0.1"
                    className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={config.timeValueEnd}
                    onChange={(e) => setConfig({ ...config, timeValueEnd: parseFloat(e.target.value) || 0 })}
                  />
                  <span className="text-sm text-gray-700">秒</span>
                </>
              ) : (
                <>
                  <input
                    type="number"
                    min="0"
                    step="0.1"
                    className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={config.timeValue}
                    onChange={(e) => setConfig({ ...config, timeValue: parseFloat(e.target.value) || 0 })}
                  />
                  <span className="text-sm text-gray-700">秒</span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallSilenceConfigComponent; 