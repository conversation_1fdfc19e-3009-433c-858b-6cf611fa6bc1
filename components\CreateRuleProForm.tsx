import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Send, Spark<PERSON>, User, Bo<PERSON>, FileText } from 'lucide-react';

interface Message {
    id: number;
    text: string;
    sender: 'user' | 'bot';
}

interface KeywordCheckConfig {
    detectionRole: 'agent' | 'customer' | 'all';
    detectionScope: 'full' | 'range';
    keywords: string[];
    analysisMethod: 'single' | 'multiple';
    detectionType: 'any' | 'all' | 'count' | 'none';
    singleSentence: boolean;
}

interface Condition {
    id: string;
    type: string;
    config?: KeywordCheckConfig;
}

interface CreateRuleProFormProps {
    onClose: () => void;
    onSubmit: (newRule: any) => void;
}

const drawerVariants = {
    hidden: { x: '100%' },
    visible: { x: 0 },
};

const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
}

export const CreateRuleProForm: React.FC<CreateRuleProFormProps> = ({ onClose, onSubmit }) => {
    // Basic Info State
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [importance, setImportance] = useState('轻度违规');
    const [category, setCategory] = useState('');
    const [effectiveTime, setEffectiveTime] = useState('全年期生效');
    const [manualReview, setManualReview] = useState('不需要');
    
    // Chat State
    const [messages, setMessages] = useState<Message[]>([
        { id: 1, text: `你好！请用自然语言描述你想要创建的规则条件。例如："当客户提到'办卡'或'信用卡'，但客服在后续对话中没有提及'工作单位'时，标记为一次违规。"`, sender: 'bot' }
    ]);
    const [inputValue, setInputValue] = useState('');
    const [isGenerating, setIsGenerating] = useState(false);
    const [generatedConditions, setGeneratedConditions] = useState<Condition[]>([]);

    const handleSendMessage = async () => {
        if (!inputValue.trim() || isGenerating) return;

        const newUserMessage: Message = { id: Date.now(), text: inputValue, sender: 'user' };
        setMessages(prev => [...prev, newUserMessage]);
        setInputValue('');
        setIsGenerating(true);

        // Simulate LLM response
        await new Promise(res => setTimeout(res, 1500));

        const newGeneratedConditions: Condition[] = [
            {
                id: 'gen-cond-1',
                type: '关键词检查',
                config: {
                    detectionRole: 'customer',
                    detectionScope: 'full',
                    keywords: ['办卡', '信用卡'],
                    analysisMethod: 'single',
                    detectionType: 'any',
                    singleSentence: false,
                },
            },
            {
                id: 'gen-cond-2',
                type: '关键词检查',
                config: {
                    detectionRole: 'agent',
                    detectionScope: 'full',
                    keywords: ['工作单位'],
                    analysisMethod: 'single',
                    detectionType: 'none',
                    singleSentence: false,
                },
            },
        ];
        setGeneratedConditions(newGeneratedConditions);

        const botResponse: Message = { id: Date.now() + 1, text: `好的，已收到您的指令："${inputValue}"。\n\n系统已为您生成以下条件预览，请确认。如果正确，您可以继续添加或点击保存。`, sender: 'bot' };
        setMessages(prev => [...prev, botResponse]);
        setIsGenerating(false);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Here you would parse the chat interaction into actual rule conditions.
        // For now, we'll just submit the basic info.
        onSubmit({
            name,
            description,
            importance,
            category,
            effectiveTime,
            manualReview,
            ruleType: category,
            detectionLogic: 'CUSTOM', // Or derived from chat
            conditions: generatedConditions,
        });
        onClose();
    };

    return (
        <AnimatePresence>
            <motion.div
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40"
                onClick={onClose}
            >
                <motion.div
                    variants={drawerVariants}
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    className="bg-white h-full w-full max-w-4xl shadow-2xl flex flex-col"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                            <Sparkles className="w-6 h-6 text-purple-500" />
                            <span>创建新规则 (Pro)</span>
                        </h2>
                        <button onClick={onClose} className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors">
                            <X className="w-6 h-6" />
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="flex-grow flex flex-col overflow-hidden">
                        <div className="flex-grow p-6 space-y-6 overflow-y-auto">
                            {/* Basic Info Section */}
                            <div className="space-y-4 p-4 border rounded-lg bg-white">
                                 <h3 className="text-lg font-medium text-gray-800">基本信息</h3>
                                 <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                     <div className="col-span-2">
                                         <label htmlFor="rule-name-pro" className="block text-sm font-medium text-gray-700 mb-1">
                                             规则名称 <span className="text-red-500">*</span>
                                         </label>
                                         <input
                                             id="rule-name-pro"
                                             type="text"
                                             value={name}
                                             onChange={(e) => setName(e.target.value)}
                                             className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                             placeholder="例如：标准开场白与情绪检测"
                                         />
                                     </div>
                                     <div>
                                         <label htmlFor="rule-category-pro" className="block text-sm font-medium text-gray-700 mb-1">规则类型</label>
                                         <select
                                             id="rule-category-pro"
                                             value={category}
                                             onChange={(e) => setCategory(e.target.value)}
                                             className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                                         >
                                            <option value="">请选择规则类型</option>
                                            <option value="合规风险">合规风险</option>
                                            <option value="服务规范">服务规范</option>
                                            <option value="服务质量">服务质量</option>
                                            <option value="服务效率">服务效率</option>
                                            <option value="服务红线">服务红线</option>
                                            <option value="信息安全">信息安全</option>
                                            <option value="客户体验">客户体验</option>
                                            <option value="信息提取">信息提取</option>
                                            <option value="销售合规">销售合规</option>
                                         </select>
                                     </div>
                                     <div>
                                         <label htmlFor="rule-importance-pro" className="block text-sm font-medium text-gray-700 mb-1">重要程度</label>
                                         <select
                                             id="rule-importance-pro"
                                             value={importance}
                                             onChange={(e) => setImportance(e.target.value)}
                                             className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                                         >
                                             <option>轻度违规</option>
                                             <option>中度违规</option>
                                             <option>严重违规</option>
                                         </select>
                                     </div>
                                     <div className="grid grid-cols-1 md:grid-cols-5 items-center gap-4">
                                        <label className="text-sm font-medium text-gray-700">生效时间</label>
                                        <div className="md:col-span-4">
                                            <div className="flex flex-col space-y-2">
                                                <div className="flex items-center space-x-6">
                                                    <label className="inline-flex items-center">
                                                        <input
                                                            type="radio"
                                                            name="effectiveTime-pro"
                                                            value="全年期生效"
                                                            checked={effectiveTime === '全年期生效'}
                                                            onChange={(e) => setEffectiveTime(e.target.value)}
                                                            className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                        />
                                                        <span className="ml-2 text-sm text-gray-700">全年期生效</span>
                                                    </label>
                                                    <label className="inline-flex items-center">
                                                        <input
                                                            type="radio"
                                                            name="effectiveTime-pro"
                                                            value="特定周期生效"
                                                            checked={effectiveTime === '特定周期生效'}
                                                            onChange={(e) => setEffectiveTime(e.target.value)}
                                                            className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                        />
                                                        <span className="ml-2 text-sm text-gray-700">特定周期生效</span>
                                                    </label>
                                                </div>
                                                {effectiveTime === '特定周期生效' && (
                                                    <div className="flex items-center space-x-2">
                                                        <input type="date" className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm" />
                                                        <span className="text-gray-500">至</span>
                                                        <input type="date" className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm" />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-5 items-center gap-4">
                                        <div className="flex items-center whitespace-nowrap">
                                            <label className="text-sm font-medium text-gray-700">人工复核</label>
                                            <div className="relative group ml-2">
                                                <span className="cursor-help text-gray-400">ⓘ</span>
                                                <div className="hidden group-hover:block absolute z-50 w-80 p-4 bg-gray-800 text-white text-sm rounded-lg shadow-lg left-0 top-6 whitespace-normal">
                                                    勾选后，则此规则，可以由人工进行再次复核。
                                                    <br /><br />
                                                    如果不勾选，则此规则命中后，复核状态将自动置为"已复核"，无须人工进行复核；
                                                    <br /><br />
                                                    不勾选适用于命中准确率高，无须人工复核的规则。
                                                </div>
                                            </div>
                                        </div>
                                        <div className="md:col-span-4">
                                            <div className="flex items-center space-x-6">
                                                <label className="inline-flex items-center">
                                                    <input
                                                        type="radio"
                                                        name="manualReview-pro"
                                                        value="需要"
                                                        checked={manualReview === '需要'}
                                                        onChange={(e) => setManualReview(e.target.value)}
                                                        className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                    />
                                                    <span className="ml-2 text-sm text-gray-700">需要</span>
                                                </label>
                                                <label className="inline-flex items-center">
                                                    <input
                                                        type="radio"
                                                        name="manualReview-pro"
                                                        value="不需要"
                                                        checked={manualReview === '不需要'}
                                                        onChange={(e) => setManualReview(e.target.value)}
                                                        className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                    />
                                                    <span className="ml-2 text-sm text-gray-700">不需要</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                     <div className="col-span-2">
                                         <label htmlFor="rule-description-pro" className="block text-sm font-medium text-gray-700 mb-1">规则描述</label>
                                         <textarea
                                             id="rule-description-pro"
                                             value={description}
                                             onChange={(e) => setDescription(e.target.value)}
                                             rows={3}
                                             className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                             placeholder="输入关于此规则的详细说明..."
                                         />
                                     </div>
                                 </div>
                            </div>
                            
                            {/* Condition Preview Section */}
                            {generatedConditions.length > 0 && (
                                <div className="space-y-4 p-4 border rounded-lg bg-white">
                                    <h3 className="text-lg font-medium text-gray-800">条件预览</h3>
                                    <div className="space-y-3">
                                        {generatedConditions.map((condition, index) => (
                                            <div key={condition.id} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                                                <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
                                                    <div className="flex items-center gap-2">
                                                        <FileText className="w-4 h-4 text-blue-500" />
                                                        <span className="text-sm font-medium text-gray-800">{String.fromCharCode(97 + index)}. {condition.type}</span>
                                                    </div>
                                                </div>

                                                {condition.type === '关键词检查' && condition.config && (
                                                    <div className="p-4 space-y-4">
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0"><label className="text-sm text-gray-600">检测角色</label></div>
                                                            <div className="flex-grow"><span className="text-sm font-medium text-gray-800">{ { all: '所有角色', customer: '客户', agent: '客服' }[condition.config.detectionRole] }</span></div>
                                                        </div>
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0"><label className="text-sm text-gray-600">检测范围</label></div>
                                                            <div className="flex-grow"><span className="text-sm font-medium text-gray-800">{ { full: '全文', range: '指定范围' }[condition.config.detectionScope] }</span></div>
                                                        </div>
                                                        <div className="flex items-start">
                                                            <div className="w-24 text-right mr-4 shrink-0 pt-1"><label className="text-sm text-gray-600">关键词</label></div>
                                                            <div className="flex-grow">
                                                                <div className="flex flex-wrap gap-2">
                                                                    {condition.config.keywords.map((keyword, idx) => (
                                                                        <span key={idx} className="inline-flex items-center px-2 py-1 rounded bg-blue-50 text-blue-700 text-sm">{keyword}</span>
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0"><label className="text-sm text-gray-600">分析方式</label></div>
                                                            <div className="flex-grow"><span className="text-sm font-medium text-gray-800">{ { single: '单句分析', multiple: '多句分析' }[condition.config.analysisMethod] }</span></div>
                                                        </div>
                                                        <div className="flex items-start">
                                                            <div className="w-24 text-right mr-4 shrink-0 pt-1"><label className="text-sm text-gray-600">检测类型</label></div>
                                                            <div className="flex-grow"><span className="text-sm font-medium text-gray-800">{ { any: '包含任意一个关键词', all: '包含全部关键词', count: '包含任意N个关键词', none: '全部不包含' }[condition.config.detectionType] }</span></div>
                                                        </div>
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0"><label className="text-sm text-gray-600">扩展功能</label></div>
                                                            <div className="flex-grow"><span className="text-sm font-medium text-gray-800">{ condition.config.singleSentence ? '单句话内生效' : '未启用' }</span></div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Natural Language Authoring Section */}
                            <div className="space-y-4 p-4 border rounded-lg bg-white">
                                <h3 className="text-lg font-medium text-gray-800 flex items-center gap-2">
                                    <Sparkles className="w-5 h-5 text-purple-500" />
                                    <span>自然语言创编</span>
                                </h3>
                                <div className="h-[28rem] flex flex-col border rounded-lg bg-gray-50 overflow-hidden">
                                    {/* Message display area */}
                                    <div className="flex-grow p-4 space-y-4 overflow-y-auto">
                                        {messages.map((msg) => (
                                            <div key={msg.id} className={`flex items-start gap-3 ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                                                {msg.sender === 'bot' && (
                                                    <div className="w-8 h-8 rounded-full bg-purple-500 text-white flex items-center justify-center shrink-0">
                                                        <Bot size={20} />
                                                    </div>
                                                )}
                                                <div className={`px-4 py-2 rounded-lg max-w-lg whitespace-pre-wrap ${msg.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-white border'}`}>
                                                    <p className="text-sm">{msg.text}</p>
                                                </div>
                                                {msg.sender === 'user' && (
                                                    <div className="w-8 h-8 rounded-full bg-gray-600 text-white flex items-center justify-center shrink-0">
                                                        <User size={20} />
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                        {isGenerating && (
                                             <div className="flex items-start gap-3 justify-start">
                                                <div className="w-8 h-8 rounded-full bg-purple-500 text-white flex items-center justify-center shrink-0">
                                                    <Bot size={20} />
                                                </div>
                                                <div className="px-4 py-2 rounded-lg bg-white border">
                                                    <span className="text-sm text-gray-500 animate-pulse">正在生成...</span>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                    {/* Input area */}
                                    <div className="p-3 border-t bg-white">
                                        <div className="relative">
                                            <input
                                                type="text"
                                                value={inputValue}
                                                onChange={(e) => setInputValue(e.target.value)}
                                                onKeyDown={(e) => { if (e.key === 'Enter') handleSendMessage(); }}
                                                placeholder="请描述您的规则条件..."
                                                className="w-full pr-12 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                                disabled={isGenerating}
                                            />
                                            <button 
                                                type="button" 
                                                onClick={handleSendMessage}
                                                className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                                                disabled={isGenerating || !inputValue.trim()}
                                            >
                                                <Send className="w-5 h-5" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Footer with buttons */}
                        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div className="flex justify-end gap-3">
                                <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    取消
                                </button>
                                <button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    保存
                                </button>
                            </div>
                        </div>
                    </form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default CreateRuleProForm; 