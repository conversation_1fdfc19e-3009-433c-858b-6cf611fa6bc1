import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useLocation } from 'react-router-dom';
import { PageHeader } from '../PageHeader';
import { ArrowLeft, MessageSquare, ListChecks, Target, AlertTriangle } from 'lucide-react';

const DialogueLine = ({ line, highlight }: { line: { speaker: string, text: string }, highlight?: boolean }) => (
    <div className={`p-4 rounded-lg flex items-start ${line.speaker === '客户' ? 'bg-gray-100' : 'bg-blue-50'} ${highlight ? 'ring-2 ring-red-400' : ''}`}>
        <div className={`w-12 h-12 rounded-full flex-shrink-0 flex items-center justify-center mr-4 ${line.speaker === '客户' ? 'bg-gray-300' : 'bg-blue-200'}`}>
            <span className="font-bold text-lg">{line.speaker.charAt(0)}</span>
        </div>
        <div>
            <p className="font-semibold text-gray-800">{line.speaker}</p>
            <p className="text-gray-700">{line.text}</p>
        </div>
    </div>
);

export const CaseDetailPage = () => {
    const { state } = useLocation();
    const caseData = state?.caseData;

    if (!caseData) {
        return (
            <div className="p-10 text-center">
                <p className="text-red-500 text-lg font-semibold">案例数据加载失败！</p>
                <p className="text-gray-600 mt-2">请尝试从案例库页面重新进入。</p>
                <Link to="/case-library" className="mt-4 inline-block bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    返回案例库
                </Link>
            </div>
        );
    }

    return (
         <div className="bg-gray-50 min-h-full">
            <PageHeader
                title={caseData.title}
                description={caseData.description}
                badge="案例分析"
                breadcrumbs={[
                    { name: '首页', path: '/' },
                    { name: '案例库', path: '/case-library' },
                    { name: caseData.title, path: `/cases/${caseData.id}` }
                ]}
            />
             <main className="p-6 md:p-10 max-w-7xl mx-auto">
                 <div className="mb-6">
                    <Link to="/case-library" className="flex items-center text-sm font-semibold text-blue-600 hover:underline">
                        <ArrowLeft size={16} className="mr-1" />
                        返回案例库
                    </Link>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border">
                        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <MessageSquare className="mr-3 text-blue-600" />
                            对话场景再现
                        </h3>
                        <div className="space-y-4">
                            {caseData.dialogue.map((line: any, index: number) => (
                                <DialogueLine key={index} line={line} highlight={line.highlight} />
                            ))}
                        </div>
                    </div>

                    <div className="space-y-8">
                        <div className="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                <ListChecks className="mr-3 text-green-600" />
                                规则配置要点
                            </h3>
                            <ul className="space-y-4">
                                {caseData.config.map((item: any, index: number) => (
                                    <li key={index} className="flex items-start">
                                        <Target className="w-5 h-5 text-gray-500 mr-3 mt-1 flex-shrink-0" />
                                        <div>
                                            <p className="font-semibold text-gray-700">{item.label}</p>
                                            <p className="text-sm text-gray-600">{item.value}</p>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </div>
                         <div className="bg-white p-6 rounded-lg shadow-sm border border-red-200">
                            <h3 className="text-xl font-bold text-red-700 mb-4 flex items-center">
                                <AlertTriangle className="mr-3" />
                                案例分析与解读
                            </h3>
                            <p className="text-gray-600 leading-relaxed">{caseData.analysis}</p>
                        </div>
                    </div>
                </div>
             </main>
         </div>
    );
};

export default CaseDetailPage;
