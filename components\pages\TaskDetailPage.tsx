import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { PageHeader } from '../PageHeader';
import { ArrowLeft, Tag, Star, BarChart2, CheckCircle, XCircle, FileText, Search } from 'lucide-react';

// --- Mock Data ---

const mockTaskDetails = {
    id: 'TASK-001',
    name: '2024年5月金融产品销售合规性质检',
    status: 'completed',
    summary: '时间: 2024-05-01 to 2024-05-31 | 抽样: 全量',
    qaScheme: '金融产品销售合规方案',
    creator: '王主管',
    creationDate: '2024-06-01',
    executionDate: '2024-06-03',
};

const mockTaskStats = {
    totalItems: 1500,
    processedItems: 1500,
    passedItems: 1350,
    failedItems: 150,
    averageScore: 92.5,
    topIssues: [
        { tag: '未充分揭示风险', count: 80 },
        { tag: '未使用标准话术', count: 45 },
        { tag: '服务态度一般', count: 25 },
    ],
};

interface TaskResultItem {
    id: string;
    sessionId: string;
    agent: string;
    qaDate: string;
    score: number;
    result: 'passed' | 'failed';
    tags: string[];
}

const mockTaskResultItems: TaskResultItem[] = Array.from({ length: 150 }).map((_, i) => ({
    id: `R-${i + 1}`,
    sessionId: `SESS_${Math.random().toString(36).substr(2, 10).toUpperCase()}`,
    agent: `坐席${Math.floor(Math.random() * 20) + 1}`,
    qaDate: `2024-06-02`,
    score: Math.floor(Math.random() * 30) + 71,
    result: Math.random() > 0.1 ? 'passed' : 'failed',
    tags: Math.random() > 0.5 ? ['未充分揭示风险'] : [],
}));


// --- Components ---

const StatCard = ({ title, value, icon: Icon, className = '' }: { title: string, value: string | number, icon: React.ElementType, className?: string }) => (
    <div className={`bg-white p-5 rounded-lg shadow-sm border border-gray-100 flex items-center ${className}`}>
        <div className="bg-blue-100 p-3 rounded-full mr-4">
            <Icon className="w-6 h-6 text-blue-600" />
        </div>
        <div>
            <p className="text-sm text-gray-500">{title}</p>
            <p className="text-2xl font-bold text-gray-800">{value}</p>
        </div>
    </div>
);


const TaskDetailPage: React.FC = () => {
    const { taskId } = useParams<{ taskId: string }>();

    // In a real app, you would fetch data based on taskId
    const task = mockTaskDetails;
    const stats = mockTaskStats;
    const results = mockTaskResultItems;

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title={task.name}
                breadcrumbs={[
                    { name: '质检管理', path: '/qa-management/task-management' },
                    { name: '质检任务管理', path: '/qa-management/task-management' },
                    { name: `任务详情: ${taskId}`, path: `/qa-management/tasks/${taskId}` }
                ]}
                actions={
                    <Link to="/qa-management/task-management" className="flex items-center bg-white text-gray-600 px-4 py-2 text-sm font-semibold rounded-lg hover:bg-gray-100 transition-colors shadow-sm border">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        返回列表
                    </Link>
                }
            />

            <main className="p-6 md:p-10 max-w-7xl mx-auto space-y-8">
                {/* Statistics Overview */}
                <section>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <StatCard title="质检总数" value={stats.totalItems.toLocaleString()} icon={BarChart2} />
                        <StatCard title="合格数" value={stats.passedItems.toLocaleString()} icon={CheckCircle} className="text-green-600" />
                        <StatCard title="不合格数" value={stats.failedItems.toLocaleString()} icon={XCircle} className="text-red-600" />
                        <StatCard title="平均得分" value={stats.averageScore.toFixed(1)} icon={Star} />
                    </div>
                     <div className="mt-6 bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                        <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                            <Tag className="w-5 h-5 mr-2 text-gray-500" />
                            高频问题 Top 3
                        </h3>
                        <div className="space-y-2">
                            {stats.topIssues.map(issue => (
                                <div key={issue.tag} className="flex justify-between items-center text-sm">
                                    <span className="text-gray-600">{issue.tag}</span>
                                    <span className="font-medium text-gray-800 bg-gray-100 px-2 py-0.5 rounded">{issue.count}次</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>
                
                {/* Results Table */}
                <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                     <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-800">质检明细</h2>
                         <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input type="text" placeholder="搜索会话ID或坐席..." className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
                        </div>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3">会话ID</th>
                                    <th scope="col" className="px-6 py-3">负责坐席</th>
                                    <th scope="col" className="px-6 py-3">质检结果</th>
                                    <th scope="col" className="px-6 py-3">得分</th>
                                    <th scope="col" className="px-6 py-3">发现标签</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {results.map(item => (
                                    <tr key={item.id} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-6 py-4 font-mono text-gray-700">{item.sessionId}</td>
                                        <td className="px-6 py-4">{item.agent}</td>
                                        <td className="px-6 py-4">
                                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                                                item.result === 'passed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            }`}>
                                                {item.result === 'passed' ? '合格' : '不合格'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 font-semibold text-lg">{item.score}</td>
                                        <td className="px-6 py-4">
                                            {item.tags.map(tag => (
                                                <span key={tag} className="bg-gray-200 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
                                                    {tag}
                                                </span>
                                            ))}
                                        </td>
                                        <td className="px-6 py-4 text-right">
                                            <Link to={`/qa-management/sessions/${item.sessionId}`} className="font-medium text-blue-600 hover:underline flex items-center justify-end w-full">
                                                <FileText className="w-4 h-4 mr-1" />
                                                查看详情
                                            </Link>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </section>
            </main>
        </div>
    );
};

export default TaskDetailPage; 