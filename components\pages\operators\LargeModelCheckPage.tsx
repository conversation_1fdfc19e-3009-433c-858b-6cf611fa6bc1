import React from 'react';
import { motion } from 'framer-motion';
import { Settings, RotateCcw } from 'lucide-react';
import LargeModelCheckConcepts from './largeModelCheck/LargeModelCheckConcepts';
import LargeModelCheckConfig, { LargeModelConfig } from './largeModelCheck/LargeModelCheckConfig';
import LargeModelCheckTester, { TestResult } from './largeModelCheck/LargeModelCheckTester';
import LargeModelCheckCases from './largeModelCheck/LargeModelCheckCases';
import LargeModelCheckTips from './largeModelCheck/LargeModelCheckTips';
import { UnifiedPageHeader } from '../final-design/components/UnifiedPageHeader';
import { Brain } from 'lucide-react';

const initialConfig: LargeModelConfig = {
    scenarioName: '通用客服场景',
    backgroundKnowledge: '客服人员在与客户沟通时，应保持礼貌和专业。开场需要有问候语，结束时需要有结束语。',
    dimensions: [
      { id: 1, name: '使用文明用语', definition: '客服在整个对话过程中，使用了"您好"、"请问"、"谢谢"等礼貌用语。' },
      { id: 2, name: '标准开场白', definition: '客服在对话开始时，主动问好并表明身份，例如"您好，xx客服很高兴为您服务"。' },
      { id: 3, name: '标准结束语', definition: '客服在对话结束时，主动询问客户是否还有其他问题，并表示感谢，例如"请问还有什么可以帮您吗？再见。"' },
    ],
};

const initialTestText = `客户：你好，我想咨询一下信用卡的年费政策。
客服：您好！很高兴为您服务。我们的信用卡首年是免年费的，当年刷卡消费满6次就可以免除次年的年费。
客户：那如果我刷不够6次呢？
客服：刷不够6次的话，第二年会收取100元的年费哦。不过我们有很多活动，还是很容易达成的。
客户：好的，我明白了，谢谢你。
客服：不客气，请问还有其他可以帮您的吗？`;

/**
 * 大模型检查页面 (重构版)
 * - 遵循开发规范，优化布局和样式
 * - 补充"学习建议"模块
 * - 拆分配置和测试为独立卡片
 */
const LargeModelCheckPage: React.FC = () => {
  const [config, setConfig] = React.useState<LargeModelConfig>(initialConfig);
  const [testText, setTestText] = React.useState<string>(initialTestText);

  const handleConfigChange = (newConfig: LargeModelConfig) => {
    setConfig(newConfig);
  };
  
  const handleLoadCase = (caseConfig: LargeModelConfig, sampleText: string) => {
    // 重新生成id确保唯一性
    const newCaseConfig = {
      ...caseConfig,
      dimensions: caseConfig.dimensions.map(d => ({...d, id: Date.now() + Math.random()}))
    };
    setConfig(newCaseConfig);
    setTestText(sampleText);
  };

  const handleReset = () => {
    setConfig(initialConfig);
    setTestText(initialTestText);
  };

  const handleTest = (text: string): TestResult => {
    const hitDimensions: string[] = [];

    config.dimensions.forEach(dim => {
      let isHit = false;
      // 模拟大模型检测逻辑：针对预设案例的维度，检查文本中是否包含相关核心词。
      switch (dim.name) {
        case '使用文明用语':
          isHit = /您好|谢谢|请问/.test(text);
          break;
        case '标准开场白':
          isHit = /很高兴为您服务|有什么可以帮您/.test(text);
          break;
        case '标准结束语':
          isHit = /还有什么可以帮您吗|再见/.test(text);
          break;
        case '风险充分揭示':
          isHit = /风险|亏损|不保本/.test(text);
          break;
        case '确认客户理解':
          isHit = /您能理解吗|您了解吗/.test(text);
          break;
        case '清晰说明退货步骤':
          isHit = /申请售后|寄回/.test(text);
          break;
        case '告知运费政策':
          isHit = /运费险/.test(text);
          break;
        case '安抚客户情绪':
          isHit = /抱歉|不好体验/.test(text);
          break;
        default:
          // 对于用户自定义的维度，提供一个基础的回退逻辑（非真实模拟）
          isHit = text.includes(dim.name);
          break;
      }

      if (isHit) {
        hitDimensions.push(dim.name);
      }
    });

    return {
      hit: hitDimensions.length > 0,
      hitDimensions: hitDimensions,
    };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedPageHeader
        title="大模型检查"
        subtitle="利用大语言模型的语义理解能力，从更高维度对对话内容进行定性分析和判断"
        icon={Brain}
        badge={{ text: "语义理解", color: "blue" }}
      />

      <main className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-6 space-y-6">
            <LargeModelCheckConcepts />
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="p-6 bg-white rounded-lg border border-gray-200"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Settings className="w-6 h-6 mr-3 text-blue-500" />
                  配置演示
                </h2>
                <button
                    onClick={handleReset}
                    className="flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                >
                    <RotateCcw className="w-4 h-4 mr-1" />
                    恢复默认
                </button>
              </div>
              <LargeModelCheckConfig config={config} onConfigChange={handleConfigChange} />
            </motion.div>
            
            <LargeModelCheckTester 
              text={testText}
              onTextChange={setTestText}
              dimensions={config.dimensions} 
              onTest={handleTest} 
            />
          </div>

          {/* Right Column */}
          <div className="lg:col-span-4 space-y-6">
            <LargeModelCheckCases onLoadCase={handleLoadCase} />
            <LargeModelCheckTips />
          </div>
        </div>
      </main>

       {/* 学习建议 */}
       <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2 text-sm">
                <p>• <strong>从案例库开始</strong>：点击右侧"案例库"中的"加载此案例"按钮，快速填充配置和测试文本，直观感受不同场景下的检测效果。</p>
                <p>• <strong>理解"维度"</strong>：尝试修改或添加检测维度，观察测试结果的变化，这是大模型算子的核心配置。</p>
                <p>• <strong>善用"背景知识"</strong>：为特定场景补充背景知识，可以显著提升模型判断的准确性，尤其是在处理专业术语和隐晦表达时。</p>
                <p>• <strong>注意</strong>：此页面的"实时测试"仅为前端模拟，用于演示交互流程。实际的大模型检测能力需后端API支持，但这里的模拟逻辑已尽可能贴近真实场景的核心判断方式。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LargeModelCheckPage;
