import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ClipboardList, 
  Clock, 
  AlertOctagon, 
  CheckSquare, 
  Timer,
  Edit,
  Paperclip,
  Send,
  MessageSquare,
  Save
} from 'lucide-react';
import { Tag, Drawer, Input } from 'antd';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';

const { TextArea } = Input;

/**
 * 跟进任务接口
 */
interface FollowUpTask {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'done';
  priority: 'high' | 'medium' | 'low';
  assignee: {
    name: string;
    avatar: string;
  };
  relatedAgent: {
    name: string;
    team: string;
  };
  triggeringRule: string;
  relatedCallId: string;
  createdAt: string;
  dueDate: string;
  result?: string;
}

// 模拟数据
const mockAssignees = [
    { name: '王班长', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang-leader' },
    { name: '李主管', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li-supervisor' },
];

const mockAgents = [
    { name: '张三', team: 'A组' },
    { name: '李四', team: 'B组' },
    { name: '王五', team: 'A组' },
];

const mockRules = ['客户提及"投诉"', '客户情绪严重负面', '长时间静音', '辱骂关键词'];

const mockTasks: FollowUpTask[] = Array.from({ length: 12 }, (_, i) => {
    const statusOptions: ('todo' | 'done')[] = ['todo', 'done'];
    const priorityOptions: ('high' | 'medium' | 'low')[] = ['high', 'medium', 'low'];
    const agent = mockAgents[i % mockAgents.length];
    const created = new Date();
    created.setDate(created.getDate() - (i % 5));
    const due = new Date(created);
    due.setDate(due.getDate() + 2);

    return {
        id: `FT-${1001 + i}`,
        title: `关于[${agent.name}]触发[${mockRules[i % mockRules.length]}]的跟进任务`,
        description: `通话ID: QM250715${1001+i}。请核实情况，并与坐席进行一对一沟通。`,
        status: statusOptions[i % statusOptions.length],
        priority: priorityOptions[i % priorityOptions.length],
        assignee: mockAssignees[i % mockAssignees.length],
        relatedAgent: agent,
        triggeringRule: mockRules[i % mockRules.length],
        relatedCallId: `QM250715${1001+i}`,
        createdAt: created.toISOString().split('T')[0],
        dueDate: due.toISOString().split('T')[0],
        result: i % 3 === 2 ? `已于${new Date().toLocaleDateString()}与坐席沟通，原因为客户理解有误，已安抚。已完成辅导。` : undefined,
    };
});


/**
 * 预警跟进中心页面
 */
const AlertFollowUpCenterPage: React.FC = () => {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState<'pending' | 'completed'>('pending');
    const [filters, setFilters] = useState<Record<string, any>>({});
    const [currentPage, setCurrentPage] = useState(1);
    const pageSize = 10;

    const [isDrawerVisible, setIsDrawerVisible] = useState(false);
    const [selectedTask, setSelectedTask] = useState<FollowUpTask | null>(null);

    const filterFields: FilterField[] = [
        { key: 'title', label: '任务标题/描述', type: 'text' },
        { key: 'rule', label: '关联触发规则', type: 'select', options: mockRules.map(r => ({value: r, label: r})) },
        { key: 'agent', label: '关联坐席/班组', type: 'text' },
        { key: 'assignee', label: '指派给', type: 'select', options: mockAssignees.map(a => ({value: a.name, label: a.name})) },
        { key: 'priority', label: '优先级', type: 'select', options: [{value: 'high', label: '高'}, {value: 'medium', label: '中'}, {value: 'low', label: '低'}]},
        { key: 'createdAt', label: '创建时间', type: 'dateRange' },
        { key: 'dueDate', label: '截止日期', type: 'dateRange' },
    ];

    const filteredData = useMemo(() => {
        return mockTasks.filter(task => {
            if (activeTab === 'pending' && task.status !== 'todo') return false;
            if (activeTab === 'completed' && task.status !== 'done') return false;

            if (filters.title && !task.title.includes(filters.title) && !task.description.includes(filters.title)) return false;
            if (filters.rule && task.triggeringRule !== filters.rule) return false;
            if (filters.agent && !`${task.relatedAgent.name} ${task.relatedAgent.team}`.includes(filters.agent)) return false;
            if (filters.assignee && task.assignee.name !== filters.assignee) return false;
            if (filters.priority && task.priority !== filters.priority) return false;
            return true;
        });
    }, [filters, activeTab]);
    
    const paginatedData = useMemo(() => {
        const startIndex = (currentPage - 1) * pageSize;
        return filteredData.slice(startIndex, startIndex + pageSize);
    }, [filteredData, currentPage, pageSize]);

    const handleShowDrawer = (task: FollowUpTask) => {
        setSelectedTask(task);
        setIsDrawerVisible(true);
    };

    const handleTabChange = (tab: 'pending' | 'completed') => {
        setActiveTab(tab);
        setCurrentPage(1);
    };

    const handleViewDetails = (callId: string) => {
        navigate(`/final-design/multi-mode-session-detail/${callId}?viewMode=basic_view`);
    };

    const statusMap = {
        todo: { label: '待处理', color: 'orange', icon: <Clock className="w-3 h-3" /> },
        done: { label: '已完成', color: 'green', icon: <CheckSquare className="w-3 h-3" /> }
    };

    const priorityMap = {
        high: { label: '高', color: 'red' },
        medium: { label: '中', color: 'yellow' },
        low: { label: '低', color: 'gray' }
    };

    const pendingCount = useMemo(() => mockTasks.filter(t => t.status === 'todo').length, []);
    const completedCount = useMemo(() => mockTasks.filter(t => t.status === 'done').length, []);

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="预警跟进中心"
                subtitle="追踪和管理所有高风险预警的跟进任务与处置过程"
                icon={ClipboardList}
                actions={[
                    // {
                    //     label: '导出报告',
                    //     variant: 'outline',
                    //     onClick: () => {},
                    // }
                ]}
            />
            <main className="p-6 md:p-10">
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                    <UnifiedSearchFilter fields={filterFields} filters={filters} onFiltersChange={setFilters} onSearch={() => setCurrentPage(1)} onReset={() => setFilters({})} />

                    <div className="mt-6">
                        <div className="border-b border-gray-200">
                          <div className="flex">
                            <button
                              onClick={() => handleTabChange('pending')}
                              className={`px-6 py-3 font-medium text-sm border-b-2 ${
                                activeTab === 'pending'
                                  ? 'border-blue-500 text-blue-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700'
                              }`}
                            >
                              待处理 ({pendingCount})
                            </button>
                            <button
                              onClick={() => handleTabChange('completed')}
                              className={`px-6 py-3 font-medium text-sm border-b-2 ${
                                activeTab === 'completed'
                                  ? 'border-blue-500 text-blue-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700'
                              }`}
                            >
                              已完成 ({completedCount})
                            </button>
                          </div>
                        </div>

                        <div className="overflow-x-auto mt-4">
                            <table className="w-full text-sm text-left text-gray-600">
                                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-3">序号</th>
                                        <th className="px-6 py-3">任务标题</th>
                                        <th className="px-6 py-3">状态</th>
                                        <th className="px-6 py-3">优先级</th>
                                        <th className="px-6 py-3">指派给</th>
                                        <th className="px-6 py-3">关联坐席/班组</th>
                                        <th className="px-6 py-3">创建时间</th>
                                        <th className="px-6 py-3">截止日期</th>
                                        <th className="px-6 py-3 text-right">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedData.map((task, index) => (
                                        <tr key={task.id} className="bg-white border-b hover:bg-gray-50">
                                            <td className="px-4 py-4 text-gray-700">
                                                {(currentPage - 1) * pageSize + index + 1}
                                            </td>
                                            <td className="px-6 py-4 font-medium text-gray-900">{task.title}</td>
                                            <td className="px-6 py-4"><Tag color={statusMap[task.status].color}>{statusMap[task.status].label}</Tag></td>
                                            <td className="px-6 py-4"><Tag color={priorityMap[task.priority].color}>{priorityMap[task.priority].label}</Tag></td>
                                            <td className="px-6 py-4">{task.assignee.name}</td>
                                            <td className="px-6 py-4">{`${task.relatedAgent.name} / ${task.relatedAgent.team}`}</td>
                                            <td className="px-6 py-4">{task.createdAt}</td>
                                            <td className="px-6 py-4">{task.dueDate}</td>
                                            <td className="px-6 py-4 text-right">
                                                <button 
                                                    onClick={() => handleShowDrawer(task)} 
                                                    className="px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                >
                                                    处理
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        <UnifiedPagination current={currentPage} total={filteredData.length} pageSize={pageSize} onChange={setCurrentPage} />
                    </div>
                </div>
            </main>

            {selectedTask && (
                <Drawer
                    open={isDrawerVisible}
                    onClose={() => setIsDrawerVisible(false)}
                    width={900}
                    title={<span className="font-semibold text-lg">{selectedTask.title}</span>}
                    destroyOnClose
                >
                    <div className="h-full flex flex-col">
                        {/* 任务详情卡片 */}
                        <div className="bg-gray-50 p-4 rounded-lg mb-6">
                            <h4 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                <MessageSquare className="w-4 h-4" />
                                任务详情
                            </h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">状态:</span> 
                                        <Tag color={statusMap[selectedTask.status].color}>{statusMap[selectedTask.status].label}</Tag>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">优先级:</span> 
                                        <Tag color={priorityMap[selectedTask.priority].color}>{priorityMap[selectedTask.priority].label}</Tag>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">负责人:</span> 
                                        <span className="font-medium">{selectedTask.assignee.name}</span>
                                    </div>
                                </div>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">关联坐席:</span> 
                                        <span className="font-medium">{selectedTask.relatedAgent.name}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">截止日期:</span> 
                                        <span className="font-medium">{selectedTask.dueDate}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">通话ID:</span> 
                                        <span className="font-medium text-blue-600">{selectedTask.relatedCallId}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="mt-4 pt-4 border-t border-gray-200">
                                <div className="flex items-start gap-2">
                                    <span className="text-gray-600 text-sm min-w-fit">触发规则:</span>
                                    <span className="text-sm font-medium text-gray-800">{selectedTask.triggeringRule}</span>
                                </div>
                                <div className="flex items-start gap-2 mt-2">
                                    <span className="text-gray-600 text-sm min-w-fit">任务描述:</span>
                                    <span className="text-sm text-gray-700">{selectedTask.description}</span>
                                </div>
                            </div>
                            <div className="flex gap-3 mt-4">
                                <button onClick={() => handleViewDetails(selectedTask.relatedCallId)} className="flex-1 px-4 py-2 text-sm font-medium text-blue-600 border border-blue-500 rounded-md hover:bg-blue-50 transition-colors">
                                    查看关联通话详情
                                </button>
                            </div>
                        </div>

                        {/* 处理结果区域 */}
                        <div className="flex-1 flex flex-col">
                            <div className="mb-6">
                                <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                    <Edit className="w-4 h-4" />
                                    处理结果
                                </h4>
                                <div className="bg-white border border-gray-200 rounded-lg p-4">
                                    <TextArea 
                                        rows={5} 
                                        placeholder="请详细描述处理过程、沟通内容、解决方案及后续跟进计划..." 
                                        defaultValue={selectedTask.result}
                                        className="mb-3"
                                    />
                                    <div className="flex justify-between items-center">
                                        <button className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                            <Paperclip className="w-4 h-4" />
                                            添加附件
                                        </button>
                                        <div className="flex gap-2">
                                            <button className="flex items-center gap-2 px-4 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors">
                                                <Save className="w-4 h-4" />
                                                提交处理结果
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Drawer>
            )}
        </div>
    );
};

export default AlertFollowUpCenterPage;