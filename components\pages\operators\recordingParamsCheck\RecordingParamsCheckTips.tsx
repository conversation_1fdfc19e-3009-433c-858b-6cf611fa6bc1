import React from 'react';

/**
 * 随录参数检查 - 使用提示组件
 * @constructor
 */
export const RecordingParamsCheckTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">字段选择</span>
                    <span className="text-xs text-gray-500 block">区分系统常规字段和自定义字段</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">多值输入</span>
                    <span className="text-xs text-gray-500 block">输入值后按Enter键添加，可添加多个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">善用自定义字段</span>
                    <span className="text-xs text-gray-500 block">通过大JSON结构传递复杂业务数据</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">组合使用</span>
                    <span className="text-xs text-gray-500 block">与其他算子（如关键词）组合，实现复杂质检逻辑</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 