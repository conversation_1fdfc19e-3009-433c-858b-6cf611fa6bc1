import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, Crown, Medal, Award } from 'lucide-react';

interface RankingItem {
    name: string;
    value: number;
    percentage?: number;
    count?: number;
    team?: string;
    subtitle?: string;
}

interface EnhancedRankingCardProps {
    title: string;
    subtitle?: string;
    data: RankingItem[];
    type: 'rules' | 'teams' | 'agents';
    icon: LucideIcon;
    iconColor?: string;
    iconBgColor?: string;
    showToggle?: boolean;
    toggleType?: 'team' | 'agent';
    onToggleChange?: (type: 'team' | 'agent') => void;
    delay?: number;
    valueFormatter?: (item: RankingItem) => string;
    className?: string;
}

/**
 * Enhanced Ranking Card Component
 * Provides beautiful ranking displays with animations and modern design
 */
export const EnhancedRankingCard: React.FC<EnhancedRankingCardProps> = ({ 
    title, 
    subtitle,
    data, 
    type, 
    icon: Icon, 
    iconColor = 'text-orange-600',
    iconBgColor = 'bg-gradient-to-br from-orange-100 to-amber-100',
    showToggle, 
    toggleType, 
    onToggleChange,
    delay = 0,
    valueFormatter,
    className = ''
}) => {
    const getRankIcon = (index: number) => {
        switch (index) {
            case 0:
                return <Crown className="w-5 h-5 text-yellow-500" />;
            case 1:
                return <Medal className="w-5 h-5 text-gray-500" />;
            case 2:
                return <Award className="w-5 h-5 text-orange-500" />;
            default:
                return null;
        }
    };

    const getRankBadgeStyle = (index: number) => {
        switch (index) {
            case 0:
                return 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white shadow-lg';
            case 1:
                return 'bg-gradient-to-br from-gray-400 to-gray-500 text-white shadow-lg';
            case 2:
                return 'bg-gradient-to-br from-orange-400 to-orange-500 text-white shadow-lg';
            default:
                return 'bg-gradient-to-br from-slate-300 to-slate-400 text-white shadow-md';
        }
    };

    const defaultValueFormatter = (item: RankingItem) => {
        if (type === 'rules') {
            return `${item.count}次`;
        }
        return `${item.value}分`;
    };

    const formatValue = valueFormatter || defaultValueFormatter;

    return (
        <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ 
                duration: 0.6, 
                delay,
                type: "spring",
                stiffness: 80
            }}
            className={`bg-white/95 backdrop-blur-sm rounded-2xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300 ${className}`}
        >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                    <motion.div 
                        className={`p-2.5 ${iconBgColor} rounded-xl shadow-lg`}
                        whileHover={{ 
                            scale: 1.1,
                            rotate: [0, -10, 10, 0]
                        }}
                        transition={{ duration: 0.3 }}
                    >
                        <Icon className={`w-5 h-5 ${iconColor}`} />
                    </motion.div>
                    <div>
                        <h3 className="text-lg font-semibold text-slate-900">{title}</h3>
                        {subtitle && (
                            <p className="text-xs text-slate-500 mt-1">{subtitle}</p>
                        )}
                    </div>
                </div>
                
                {showToggle && (
                    <motion.div 
                        className="flex items-center bg-slate-100 rounded-xl p-1"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: delay + 0.2 }}
                    >
                        <button
                            onClick={() => onToggleChange?.('team')}
                            className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 ${
                                toggleType === 'team'
                                    ? 'bg-white text-blue-600 shadow-sm transform scale-105'
                                    : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                            }`}
                        >
                            团队
                        </button>
                        <button
                            onClick={() => onToggleChange?.('agent')}
                            className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 ${
                                toggleType === 'agent'
                                    ? 'bg-white text-blue-600 shadow-sm transform scale-105'
                                    : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                            }`}
                        >
                            坐席
                        </button>
                    </motion.div>
                )}
            </div>
            
            {/* Ranking List */}
            <div className="space-y-3">
                {data.map((item, index) => (
                    <motion.div 
                        key={index} 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ 
                            delay: delay + 0.1 + (index * 0.05),
                            type: "spring",
                            stiffness: 100
                        }}
                        whileHover={{ 
                            x: 4,
                            transition: { duration: 0.2 }
                        }}
                        className="group flex items-center justify-between p-3 rounded-xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-blue-50/50 transition-all duration-200 border border-transparent hover:border-blue-100"
                    >
                        <div className="flex items-center gap-3 flex-1">
                            <div className="relative">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${getRankBadgeStyle(index)}`}>
                                    {index + 1}
                                </div>
                                {index < 3 && (
                                    <div className="absolute -top-1 -right-1">
                                        {getRankIcon(index)}
                                    </div>
                                )}
                            </div>
                            <div className="flex flex-col min-w-0 flex-1">
                                <span className="text-sm font-medium text-slate-700 truncate group-hover:text-slate-900 transition-colors">
                                    {item.name}
                                </span>
                                {(item.team || item.subtitle) && (
                                    <span className="text-xs text-slate-500">
                                        {item.team || item.subtitle}
                                    </span>
                                )}
                            </div>
                        </div>
                        <div className="text-right">
                            <div className="text-sm font-semibold text-slate-900">
                                {formatValue(item)}
                            </div>
                            {item.percentage && (
                                <div className="text-xs text-slate-500">
                                    {item.percentage}%
                                </div>
                            )}
                        </div>
                    </motion.div>
                ))}
            </div>
        </motion.div>
    );
};

export default EnhancedRankingCard;
