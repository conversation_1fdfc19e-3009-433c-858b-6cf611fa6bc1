import React, { useState } from 'react';

/**
 * 非正常挂机测试器属性接口
 */
interface AbnormalHangupTesterProps {
  config: {
    operator: '>' | '<' | '>=' | '<=' | '=';
    duration: number;
    role: 'any' | 'customer' | 'agent';
  };
  text: string;
  setText: React.Dispatch<React.SetStateAction<string>>;
}

/**
 * 非正常挂机实时测试组件
 * @param config 当前配置
 * @param text 测试文本
 * @param setText 更新测试文本的函数
 * @constructor
 */
const AbnormalHangupTester: React.FC<AbnormalHangupTesterProps> = ({ config, text, setText }) => {
  const [testResult, setTestResult] = useState<{
    matched: boolean;
    details: string;
    actualDuration?: number;
    lastSpeaker?: string;
  } | null>(null);

  /**
   * 执行测试
   */
  const runTest = () => {
    const lines = text.split('\n').filter(s => s.trim());
    const sentences = lines.filter(line => line.startsWith('[') && !line.startsWith('[HANGUP_TIME:'));
    const hangupLine = lines.find(line => line.startsWith('[HANGUP_TIME:'));

    if (sentences.length === 0) {
      setTestResult({ matched: false, details: '规则未命中 - 未找到带时间戳的句子。' });
      return;
    }
    if (!hangupLine) {
      setTestResult({ matched: false, details: '规则未命中 - 未找到挂机时间 `[HANGUP_TIME: ...]`。' });
      return;
    }

    const lastSentence = sentences[sentences.length - 1];
    const lastTimestampMatch = lastSentence.match(/\[(\d{2}):(\d{2})\]/);
    const hangupTimestampMatch = hangupLine.match(/\[HANGUP_TIME:\s*(\d+\.?\d*)]/);

    if (!lastTimestampMatch || !hangupTimestampMatch) {
      setTestResult({ matched: false, details: '规则未命中 - 时间戳格式错误。' });
      return;
    }

    const lastSentenceTime = parseInt(lastTimestampMatch[1]) * 60 + parseInt(lastTimestampMatch[2]);
    const hangupTime = parseFloat(hangupTimestampMatch[1]);
    const actualDuration = hangupTime - lastSentenceTime;

    const lastSpeakerMatch = lastSentence.match(/\]\s*(客户|客服)：/);
    const lastSpeaker = lastSpeakerMatch ? (lastSpeakerMatch[1] === '客户' ? 'customer' : 'agent') : 'unknown';
    
    const roleMap = { any: '任意', customer: '客户', agent: '客服', unknown: '未知' };

    const roleMatched = config.role === 'any' || config.role === lastSpeaker;

    let durationMatched = false;
    switch (config.operator) {
      case '>': durationMatched = actualDuration > config.duration; break;
      case '<': durationMatched = actualDuration < config.duration; break;
      case '>=': durationMatched = actualDuration >= config.duration; break;
      case '<=': durationMatched = actualDuration <= config.duration; break;
      case '=': durationMatched = actualDuration === config.duration; break;
    }

    const matched = roleMatched && durationMatched;
    
    let details = `挂机等待时长 ${actualDuration.toFixed(1)}s，最后一句话角色为 "${roleMap[lastSpeaker]}"。`;
    if (!roleMatched) {
        details += ` 角色不匹配 (需要 "${roleMap[config.role]}")。`;
    }
    if (!durationMatched) {
        details += ` 时长不匹配 (需要 ${config.operator} ${config.duration}s)。`;
    }

    setTestResult({
      matched,
      details: (matched ? '规则已命中 - ' : '规则未命中 - ') + details,
      actualDuration,
      lastSpeaker: roleMap[lastSpeaker],
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
       <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">🧪 实时测试</h2>
        <button
          onClick={() => {
            setText('[00:01] 客服：您好，请问有什么可以帮您？\n[00:05] 客户：我想咨询一下这个产品。\n[00:10] 客服：好的，这个产品我们有现货。\n[00:15] 客户：那太好了，谢谢。\n[HANGUP_TIME: 22.5]');
            setTestResult(null);
          }}
          className="text-sm text-gray-500 hover:text-gray-700 underline"
        >
          重置示例
        </button>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">📝 测试文本</label>
          <textarea
            id="test-text"
            rows={8}
            className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none font-mono"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="请输入带时间戳的对话和挂机时间..."
          />
        </div>

        <button
          onClick={runTest}
          disabled={!text.trim()}
          className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300"
        >
          执行测试
        </button>

        {testResult && (
           <div className="border-t border-gray-200 pt-6 space-y-4">
             <h3 className="text-lg font-semibold text-gray-900">📊 测试结果</h3>
             <div className={`p-6 rounded-xl border-2 ${testResult.matched ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                <div className="flex items-center space-x-4">
                    <span className={`text-3xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>{testResult.matched ? '✓' : '✗'}</span>
                    <div>
                        <div className={`font-bold text-lg ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>{testResult.matched ? '规则命中' : '规则未命中'}</div>
                        <p className={`text-sm ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>{testResult.details}</p>
                    </div>
                </div>
             </div>
             <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-sm space-y-2">
                <div className="flex justify-between"><span className="text-gray-600">实际挂机等待时长:</span> <span className="font-medium text-gray-900">{testResult.actualDuration?.toFixed(1)} 秒</span></div>
                <div className="flex justify-between"><span className="text-gray-600">最后一句话角色:</span> <span className="font-medium text-gray-900">{testResult.lastSpeaker}</span></div>
             </div>
           </div>
        )}
      </div>
    </div>
  );
};

export default AbnormalHangupTester; 