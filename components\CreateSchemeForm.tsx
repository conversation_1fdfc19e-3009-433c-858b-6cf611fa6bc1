import React, { useState, useMemo, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { X, Plus, Trash2 } from 'lucide-react';

// This is the full rule definition, which we'd get from the library
interface Rule {
    id: string;
    name: string;
    category: string;
    importance: string;
    description: string;
}

// This is the rule once it's added to a scheme, with a score
interface RuleInScheme {
    id: string;
    name: string;
    score: number;
}

interface SchemeFormData {
    name: string;
    description: string;
    totalScore: number;
    passThreshold: number;
    appealDeadlineDays: number;
    rules: RuleInScheme[];
}

interface CreateSchemeFormProps {
    onClose: () => void;
    onSubmit: (data: SchemeFormData) => void;
    allRules: Rule[]; // All available rules from the library
    initialData?: any; // For editing
}

interface RuleSelectorModalProps {
    isOpen: boolean;
    onClose: () => void;
    allRules: Rule[];
    selectedRuleIds: Set<string>;
    onAddRules: (selectedRules: Rule[]) => void;
}

const drawerVariants = {
    hidden: { x: '100%' },
    visible: { x: 0 },
};

const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
}

const modalVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1 },
};

const RuleSelectorModal: React.FC<RuleSelectorModalProps> = ({ isOpen, onClose, allRules, selectedRuleIds, onAddRules }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());

    const filteredRules = allRules.filter(rule => 
        rule.name.toLowerCase().includes(searchTerm.toLowerCase()) && !selectedRuleIds.has(rule.id)
    );

    const handleSelectRule = (ruleId: string) => {
        const newSelection = new Set(selectedRules);
        if (newSelection.has(ruleId)) {
            newSelection.delete(ruleId);
        } else {
            newSelection.add(ruleId);
        }
        setSelectedRules(newSelection);
    };
    
    const handleConfirm = () => {
        const rulesToAdd = allRules.filter(rule => selectedRules.has(rule.id));
        onAddRules(rulesToAdd);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/40 z-[60] flex items-center justify-center" onClick={onClose}>
            <div className="bg-white rounded-lg shadow-xl w-full max-w-lg flex flex-col" onClick={e => e.stopPropagation()}>
                <h3 className="p-4 font-semibold text-lg border-b">从规则库添加</h3>
                <div className="p-4">
                    <input
                        type="text"
                        placeholder="搜索规则..."
                        className="w-full px-3 py-2 border rounded-md"
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="p-4 overflow-y-auto h-96 space-y-2">
                    {filteredRules.map(rule => (
                        <div key={rule.id} className={`p-3 rounded-md cursor-pointer ${selectedRules.has(rule.id) ? 'bg-blue-100' : 'bg-gray-50'}`} onClick={() => handleSelectRule(rule.id)}>
                            <p className="font-semibold">{rule.name}</p>
                            <p className="text-sm text-gray-500">{rule.description}</p>
                        </div>
                    ))}
                </div>
                <div className="p-4 border-t flex justify-end gap-2">
                    <button onClick={onClose} className="px-4 py-2 bg-gray-200 rounded-md">取消</button>
                    <button onClick={handleConfirm} className="px-4 py-2 bg-blue-600 text-white rounded-md">确认添加</button>
                </div>
            </div>
        </div>
    );
};

/**
 * A drawer form for creating or editing a scheme.
 */
export const CreateSchemeForm: React.FC<CreateSchemeFormProps> = ({ onClose, onSubmit, allRules, initialData }) => {
    const [formData, setFormData] = useState<SchemeFormData>({
        name: '',
        description: '',
        totalScore: 100,
        passThreshold: 85,
        appealDeadlineDays: 7,
        rules: [],
    });
    
    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || '',
                description: initialData.description || '',
                totalScore: initialData.totalScore || 100,
                passThreshold: initialData.passThreshold || 85,
                appealDeadlineDays: initialData.appealDeadlineDays || 7,
                rules: initialData.rules || [],
            });
        }
    }, [initialData]);

    const [isRuleSelectorOpen, setIsRuleSelectorOpen] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: name === 'totalScore' || name === 'passThreshold' || name === 'appealDeadlineDays' ? parseInt(value, 10) || 0 : value,
        }));
    };

    const handleRemoveRule = (ruleId: string) => {
        setFormData(prev => ({
            ...prev,
            rules: prev.rules.filter(r => r.id !== ruleId),
        }));
    };

    const handleScoreChange = (ruleId: string, score: string) => {
        const newScore = parseInt(score, 10);
        setFormData(prev => ({
            ...prev,
            rules: prev.rules.map(r => (r.id === ruleId ? { ...r, score: isNaN(newScore) ? 0 : newScore } : r)),
        }));
    };
    
    const handleAddRules = (selectedRules: Rule[]) => {
        const newRules = selectedRules.map(rule => ({
            id: rule.id,
            name: rule.name,
            score: 0,
        }));
        setFormData(prev => ({
            ...prev,
            rules: [...prev.rules, ...newRules],
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(formData);
        onClose();
    };

    const totalCalculatedScore = useMemo(() => {
        return formData.rules.reduce((acc, rule) => acc + (rule.score || 0), formData.totalScore);
    }, [formData.totalScore, formData.rules]);

    return (
        <div className="fixed inset-0 bg-black/30 z-50 flex justify-end" onClick={onClose}>
            <motion.div 
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="w-full max-w-2xl bg-white h-full flex flex-col shadow-2xl" 
                onClick={e => e.stopPropagation()}
            >
                <header className="flex-shrink-0 flex items-center justify-between p-4 border-b">
                    <h2 className="text-lg font-semibold text-gray-800">{initialData ? '编辑质检方案' : '创建新质检方案'}</h2>
                    <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-100">
                        <X className="w-5 h-5 text-gray-500" />
                    </button>
                </header>
                <form onSubmit={handleSubmit} className="flex-1 flex flex-col overflow-y-auto">
                    <div className="p-6 space-y-6">
                        {/* Basic Info */}
                        <div className="p-4 border rounded-lg">
                            <h3 className="text-md font-semibold text-gray-700 mb-3">基本信息</h3>
                            <div className="space-y-4">
                                <div>
                                    <label htmlFor="scheme-name" className="block text-sm font-medium text-gray-600 mb-1">方案名称</label>
                                    <input type="text" id="scheme-name" name="name" value={formData.name} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label htmlFor="total-score" className="block text-sm font-medium text-gray-600 mb-1">基准总分</label>
                                        <input type="number" id="total-score" name="totalScore" value={formData.totalScore} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"/>
                                    </div>
                                    <div>
                                        <label htmlFor="pass-threshold" className="block text-sm font-medium text-gray-600 mb-1">合格分数线</label>
                                        <input type="number" id="pass-threshold" name="passThreshold" value={formData.passThreshold} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"/>
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label htmlFor="appeal-deadline" className="block text-sm font-medium text-gray-600 mb-1">申诉期限(天)</label>
                                        <input type="number" id="appeal-deadline" name="appealDeadlineDays" value={formData.appealDeadlineDays} onChange={handleInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"/>
                                    </div>
                                </div>
                                <div>
                                    <label htmlFor="scheme-description" className="block text-sm font-medium text-gray-600 mb-1">方案描述</label>
                                    <textarea id="scheme-description" name="description" rows={2} value={formData.description} onChange={handleInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"/>
                                </div>
                            </div>
                        </div>

                        {/* Rules Config */}
                        <div className="p-4 border rounded-lg">
                            <div className="flex justify-between items-center mb-3">
                                <h3 className="text-md font-semibold text-gray-700">规则配置</h3>
                                <div className="text-lg font-bold">最终分数: <span className="text-blue-600">{totalCalculatedScore}</span></div>
                            </div>
                            <div className="border border-gray-200 rounded-lg">
                                <div className="p-4">
                                    <button type="button" onClick={() => setIsRuleSelectorOpen(true)} className="w-full flex items-center justify-center gap-2 px-4 py-2 border-2 border-dashed border-gray-300 rounded-lg text-sm font-semibold text-gray-600">
                                        <Plus className="w-4 h-4" />
                                        从规则库添加规则
                                    </button>
                                </div>
                                <div className="max-h-96 overflow-y-auto">
                                    <table className="w-full text-sm">
                                        <tbody>
                                            {formData.rules.length > 0 ? formData.rules.map(rule => (
                                                <tr key={rule.id} className="border-t">
                                                    <td className="px-4 py-2 text-gray-800">{rule.name}</td>
                                                    <td className="px-4 py-2 w-32">
                                                        <input type="number" value={rule.score} onChange={(e) => handleScoreChange(rule.id, e.target.value)} className="w-full px-2 py-1 border border-gray-300 rounded-md"/>
                                                    </td>
                                                    <td className="px-4 py-2 w-16 text-center">
                                                        <button type="button" onClick={() => handleRemoveRule(rule.id)} className="p-1 text-gray-400 hover:text-red-600">
                                                            <Trash2 className="w-4 h-4" />
                                                        </button>
                                                    </td>
                                                </tr>
                                            )) : (
                                                <tr className="border-t">
                                                    <td colSpan={3} className="text-center py-6 text-gray-500">
                                                        暂未添加任何规则
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <footer className="flex-shrink-0 p-4 bg-gray-50 border-t mt-auto">
                        <div className="flex justify-end gap-3">
                            <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-white border rounded-lg">取消</button>
                            <button type="submit" className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg">{initialData ? '保存更改' : '创建方案'}</button>
                        </div>
                    </footer>
                </form>
            </motion.div>
             <RuleSelectorModal
                isOpen={isRuleSelectorOpen}
                onClose={() => setIsRuleSelectorOpen(false)}
                allRules={allRules}
                selectedRuleIds={new Set(formData.rules.map(r => r.id))}
                onAddRules={handleAddRules}
            />
        </div>
    );
}; 