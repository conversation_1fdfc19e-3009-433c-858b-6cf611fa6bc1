
---

### **页面设计：27. 历史预警查询 (Historical Alert Query)**

#### **27.1 核心定位与目标**

该页面是系统内所有**非活跃预警数据**的统一查询入口。其核心目标是为管理者提供一个强大的工具，用于**审计、复盘和深度分析**那些已经超过"活跃期"的历史预警事件。它不追求实时性，而是强调**查询的深度、广度和数据的完整性**。

#### **27.2 页面设计与功能模块**

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: `历史预警查询`
*   **副标题**: `查询和分析所有已归档的历史预警记录`
*   **图标**: `History` (历史图标)
*   **核心操作**: `导出查询结果`，将复杂的查询结果导出为文件。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **功能**: 提供全面的历史预警筛选维度。
*   **筛选字段**:
    *   `时间范围`
    *   `触发规则`、`预警等级`、`坐席/班组`
    *   `处置状态`: `被忽略 (已读)` / `已创建跟进任务`
    *   `跟进任务状态`: `已完成` / `处理中` / `已逾期`
    *   `关键词搜索`: 在预警的上下文摘要中进行全文搜索。

**c. 历史预警列表表格 (`Table`)**
*   **功能**: 展示查询结果，强调信息的完整性和可追溯性。
*   **表格列**: `序号`、`预警ID`、`触发时间`、`触发规则`、`预警等级`、`关联坐席/班组`、`处置状态`、`跟进任务状态`、`操作`。
*   **交互**:
    *   点击行末的**"查看详情"**图标按钮，打开**预警详情抽屉**。

**d. 分页组件 (`UnifiedPagination`)**
*   用于处理海量的历史数据。

---

### **组件设计：28. 预警详情 (Alert Detail Drawer/Modal)**

#### **28.1 核心定位与目标**

这是一个**可复用的UI组件**，被**[实时预警中心]**和**[历史预警查询]**共同调用。其目标是提供一个"一站式"的预警信息聚合视图，让用户在一个界面内就能掌握预警事件的"前因后果"。

#### **28.2 组件设计与功能模块**

**a. 头部区域 (Header)**
*   **标题**: `预警详情`，附带唯一的 `预警ID`。
*   **状态徽章**: 醒目地显示预警的最新状态 (`新预警`, `处理中`, `已读`, `已解决`等)。
*   **关闭按钮**: 关闭抽屉。

**b. 主体内容区 (分区块展示)**

*   **模块一：预警核心信息**
    *   `预警等级`、`触发时间`。
    *   `触发规则`: 规则名称旁有帮助图标，悬浮可查看详细描述。

*   **模块二：事发上下文**
    *   `关联通话ID` (提供跳转到**[多模式会话详情页]**的链接)。
    *   `坐席信息` (姓名、ID、班组)、`客户信息` (电话)。
    *   `上下文摘要`: 以丰富的对话形式展示，而非纯文本。每一行包含：
        *   **角色图标**: 区分"坐席"和"客户"。
        *   **发言时间戳**: 相对通话开始时间的秒数。
        *   **播放/暂停按钮**: 模拟音频播放控制。
        *   **发言内容**: 触发预警的关键句子会高亮显示。

*   **模块三：处置与跟进记录**
    *   `处置状态`: 文字描述（如"已由 [主管A] 创建跟进任务"）。
    *   `处置人`和`处置时间`。
    *   **关联的跟进任务卡片** (如果存在):
        *   显示任务的`标题`、`状态`、`负责人`、`截止日期`。
        *   提供一个**"查看任务详情"**的按钮，链接到**[预警跟进中心]**。

**c. 底部操作栏 (Footer Actions)**
*   **功能**: **动态显示**。仅当从**[实时预警中心]**查看"新预警"时才显示。
*   **按钮**: `标记已读`、`标记处理中`、`创建跟进任务`。

#### **28.3 核心交互与设计哲学**

*   **信息聚合**: 将预警、通话、任务三者信息聚合于一处，提供完整的事件追溯入口。
*   **上下文保留**: 使用抽屉/模态框，不脱离主列表，体验流畅。
*   **分层下钻**: 提供从"摘要"到"聚合详情"再到"原始场景"的逐层深入分析路径。
*   **组件复用**: 通过 `props` (如 `alertId`, `isReadOnly`) 控制，实现一套UI逻辑服务于多个不同场景。

---