import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Calendar, Users, CheckCircle, AlertCircle, Eye, ChevronUp, ChevronDown } from 'lucide-react';
import UnifiedSearchFilter, { FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';

/**
 * 质检明细数据接口
 */
interface DetailRecord {
  id: string; // 唯一ID
  recordNumber: string; // 记录编号
  taskName: string; // 所属任务 (虽然不显示，但数据中保留)
  agentName: string; // 坐席姓名
  agentId: string; // 坐席工号
  teamName: string; // 所属班组
  customerNumber: string; // 客户号码
  callStartTime: string; // 通话开始时间
  callDuration: string; // 通话时长
  finalScore: number; // 最终得分
  qualityResult: '合格' | '不合格'; // 质检结果
  appealStatus: 'none' | 'available' | 'pending' | 'processed' | 'expired'; // 申诉状态
  appealResult?: '成功' | '失败'; // 申诉结果
  appealTime?: string; // 申诉时间
  processTime?: string; // 处理时间
  appealDeadline?: string; // 申诉有效期
  scoreEvolution: {
    ai: number;
    review?: number;
    appeal?: number;
    final: number;
    source: 'AI' | '复核' | '申诉';
  };
}

/**
 * 搜索条件接口
 */
interface SearchFilters {
  recordNumber: string;
  agentName: string;
  teamName: string;
  customerNumber: string;
  callStartTime: [string, string];
  finalScoreMin: string;
  finalScoreMax: string;
  qualityResult: '' | '合格' | '不合格';
  appealStatus: '' | 'none' | 'available' | 'pending' | 'processed' | 'expired';
  appealResult: '' | '成功' | '失败';
}

/**
 * 模拟数据生成
 */
const generateMockData = (): DetailRecord[] => {
  const agentOptions = [
    { name: '王小明', id: '10001', team: 'A组' },
    { name: '李静', id: '10002', team: 'B组' },
    { name: '陈晨', id: '10003', team: 'A组' },
    { name: '赵大', id: '10004', team: 'B组' },
    { name: '孙小美', id: '10005', team: 'C组' },
  ];
  const taskOptions = ['营销流程质检方案', '服务流程质检方案', '投诉处理质检方案'];

  return Array.from({ length: 50 }, (_, index) => {
    const id = (index + 1).toString().padStart(3, '0');
    const agent = agentOptions[Math.floor(Math.random() * agentOptions.length)];
    
    const aiScore = Math.floor(Math.random() * 41) + 60; // 60-100
    const hasReview = Math.random() > 0.7;
    const reviewScore = hasReview ? Math.min(100, aiScore + Math.floor(Math.random() * 10) - 3) : undefined;
    const hasAppeal = Math.random() > 0.8;
    const appealScore = hasAppeal ? Math.min(100, (reviewScore || aiScore) + Math.floor(Math.random() * 8) - 2) : undefined;
    
    const finalScore = appealScore || reviewScore || aiScore;
    const qualityResult = finalScore >= 80 ? '合格' : '不合格';
    
    let appealStatus: DetailRecord['appealStatus'] = 'none';
    let appealResult: DetailRecord['appealResult'] | undefined;
    let appealTime: string | undefined;
    let processTime: string | undefined;
    
    if (qualityResult === '不合格' && Math.random() > 0.6) {
      const statusRandom = Math.random();
      if (statusRandom > 0.8) {
        appealStatus = 'available';
      } else if (statusRandom > 0.6) {
        appealStatus = 'pending';
        appealTime = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
      } else if (statusRandom > 0.3) {
        appealStatus = 'processed';
        appealResult = Math.random() > 0.5 ? '成功' : '失败';
        appealTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
        processTime = new Date(new Date(appealTime).getTime() + Math.random() * 48 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
      } else {
        appealStatus = 'expired';
      }
    }
    
    const callStartTimeDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const callStartTime = callStartTimeDate.toISOString().replace('T', ' ').substring(0, 19);
    const durationMinutes = Math.floor(Math.random() * 17) + 3;
    const durationSeconds = Math.floor(Math.random() * 60);
    const callDuration = `${durationMinutes.toString().padStart(2, '0')}:${durationSeconds.toString().padStart(2, '0')}`;
    
    return {
      id: `TD_${id}`,
      recordNumber: `QM250702${id}`,
      taskName: taskOptions[Math.floor(Math.random() * taskOptions.length)],
      agentName: agent.name,
      agentId: agent.id,
      teamName: agent.team,
      customerNumber: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      callStartTime,
      callDuration,
      finalScore,
      qualityResult,
      appealStatus,
      appealResult,
      appealTime,
      processTime,
      appealDeadline: appealStatus === 'available' ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19) : undefined,
      scoreEvolution: {
        ai: aiScore,
        review: reviewScore,
        appeal: appealScore,
        final: finalScore,
        source: appealScore ? '申诉' : (reviewScore ? '复核' : 'AI')
      }
    };
  });
};

/**
 * 获取申诉状态显示
 * @param detail 质检明细数据
 * @returns 申诉状态的JSX元素
 */
const getAppealStatusDisplay = (detail: DetailRecord) => {
  switch (detail.appealStatus) {
    case 'available':
      return (
        <div className="space-y-1">
          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            可申诉
          </span>
          <div className="text-xs text-gray-500">
            有效期至: {detail.appealDeadline?.substring(0, 16)}
          </div>
        </div>
      );
    case 'pending':
      return (
        <div className="space-y-1">
          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
            申诉中
          </span>
          <div className="text-xs text-gray-500">
            申诉时间: {detail.appealTime?.substring(0, 16)}
          </div>
        </div>
      );
    case 'processed':
      return (
        <div className="space-y-1">
          <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
            detail.appealResult === '成功' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            已处理
          </span>
          <div className="text-xs text-gray-500">
            结果: {detail.appealResult} | {detail.processTime?.substring(0, 16)}
          </div>
        </div>
      );
    case 'expired':
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
          已过期
        </span>
      );
    default:
      return (
        <span className="text-xs text-gray-400">-</span>
      );
  }
};

/**
 * 获取分数演进显示
 * @param detail 质检明细数据
 * @returns 分数演进的JSX元素
 */
const getScoreEvolutionDisplay = (detail: DetailRecord) => {
  const { scoreEvolution } = detail;
  const parts = [`AI: ${scoreEvolution.ai}`];
  
  if (scoreEvolution.review !== undefined) {
    parts.push(`复核: ${scoreEvolution.review}`);
  }
  
  if (scoreEvolution.appeal !== undefined) {
    parts.push(`申诉: ${scoreEvolution.appeal}`);
  }
  
  parts.push(`(采纳: ${scoreEvolution.source})`);
  
  return (
    <div className="group relative">
      <span className={`font-bold cursor-help ${
        detail.finalScore >= 80 ? 'text-green-600' :
        detail.finalScore >= 60 ? 'text-yellow-600' : 'text-red-600'
      }`}>
        {detail.finalScore}
      </span>
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
        {parts.join(' → ')}
      </div>
    </div>
  );
};

const FinalTaskDetailPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    recordNumber: '',
    agentName: '',
    teamName: '',
    customerNumber: '',
    callStartTime: ['', ''],
    finalScoreMin: '',
    finalScoreMax: '',
    qualityResult: '',
    appealStatus: '',
    appealResult: '',
  });

  const filteredData: DetailRecord[] = useMemo(() => {
    const allData = generateMockData();
    return allData.filter(detail => {
      if (searchFilters.recordNumber && !detail.recordNumber.includes(searchFilters.recordNumber)) return false;
      if (searchFilters.agentName && !`${detail.agentName}/${detail.agentId}`.includes(searchFilters.agentName)) return false;
      if (searchFilters.teamName && !detail.teamName.includes(searchFilters.teamName)) return false;
      if (searchFilters.customerNumber && !detail.customerNumber.includes(searchFilters.customerNumber)) return false;
      
      if (searchFilters.callStartTime[0] && new Date(detail.callStartTime) < new Date(searchFilters.callStartTime[0])) return false;
      if (searchFilters.callStartTime[1]) {
        const end = new Date(searchFilters.callStartTime[1]);
        end.setHours(23, 59, 59, 999);
        if (new Date(detail.callStartTime) > end) return false;
      }

      if (searchFilters.finalScoreMin && detail.finalScore < parseInt(searchFilters.finalScoreMin)) return false;
      if (searchFilters.finalScoreMax && detail.finalScore > parseInt(searchFilters.finalScoreMax)) return false;

      if (searchFilters.qualityResult && detail.qualityResult !== searchFilters.qualityResult) return false;

      if (searchFilters.appealStatus && detail.appealStatus !== searchFilters.appealStatus) return false;
      if (searchFilters.appealResult && detail.appealResult !== searchFilters.appealResult) return false;

      return true;
    });
  }, [searchFilters]);

  const paginatedData = useMemo(() => {
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, pagination]);

  const filterFields: FilterField[] = [
    {
      key: 'recordNumber',
      label: '记录编号',
      type: 'text',
      placeholder: '请输入记录编号',
    },
    {
      key: 'agentName',
      label: '坐席',
      type: 'text',
      placeholder: '请输入坐席姓名或工号',
    },
    {
      key: 'teamName',
      label: '所属班组',
      type: 'text',
      placeholder: '请输入班组名称',
    },
    {
      key: 'customerNumber',
      label: '客户号码',
      type: 'text',
      placeholder: '请输入客户号码',
    },
    {
      key: 'callStartTime',
      label: '通话开始时间',
      type: 'datetimeRange',
      placeholder: ['开始时间', '结束时间'],
    },
    {
      key: 'finalScore',
      label: '最终得分',
      type: 'numberRange',
      placeholder: '最低分-最高分',
    },
    {
      key: 'qualityResult',
      label: '质检结果',
      type: 'select',
      placeholder: '全部结果',
      options: [
        { label: '合格', value: '合格' },
        { label: '不合格', value: '不合格' },
      ],
    },
    {
      key: 'appealStatus',
      label: '申诉状态',
      type: 'select',
      placeholder: '请选择申诉状态',
      options: [
        { label: '无申诉', value: 'none' },
        { label: '可申诉', value: 'available' },
        { label: '申诉中', value: 'pending' },
        { label: '已处理', value: 'processed' },
        { label: '已过期', value: 'expired' },
      ],
    },
    {
      key: 'appealResult',
      label: '申诉结果',
      type: 'select',
      placeholder: '全部结果',
      options: [
        { label: '成功', value: '成功' },
        { label: '失败', value: '失败' },
      ],
    },
  ];

  const handleFiltersChange = (newFilters: Record<string, any>) => {
    setSearchFilters(newFilters as SearchFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleReset = () => {
    setSearchFilters({
      recordNumber: '',
      agentName: '',
      teamName: '',
      customerNumber: '',
      callStartTime: ['', ''],
      finalScoreMin: '',
      finalScoreMax: '',
      qualityResult: '',
      appealStatus: '',
      appealResult: '',
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  return (
    <div className="bg-gray-50/50 min-h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/final-design/task-list')}
              className="flex items-center gap-2 px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeft className="w-4 h-4" />
              返回列表
            </button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                11月营销活动通话质检
              </h1>
              <p className="text-sm text-gray-600 mt-1">任务ID: {taskId}</p>
            </div>
          </div>
        </div>
      </div>

      <main className="p-6">
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
        >
            {/* Task Information */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
              <div className="bg-gray-50/80 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-4">
                  <Calendar className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-800">任务信息</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">质检方案:</span>
                    <span className="font-medium text-gray-900">营销流程质检方案</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">质检范围:</span>
                    <span className="font-medium text-gray-900">坐席组A、B、C</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">通话时段:</span>
                    <span className="font-medium text-gray-900">2023.11.01 ~ 2023.11.10</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">创建人:</span>
                    <span className="font-medium text-gray-900">张主管</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">创建时间:</span>
                    <span className="font-medium text-gray-900">2023.11.12 09:00:00</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">完成时间:</span>
                    <span className="font-medium text-gray-900">2023.11.12 18:30:00</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {/* Card 1 */}
                <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-200 flex items-center gap-5">
                    <div className="p-3 bg-blue-100 rounded-full">
                        <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 font-medium">任务记录总数</p>
                        <p className="text-2xl font-bold text-gray-800">{filteredData.length}</p>
                    </div>
                </div>
                {/* Card 2 */}
                <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-200 flex items-center gap-5">
                    <div className="p-3 bg-green-100 rounded-full">
                        <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 font-medium">任务平均分</p>
                        <p className="text-2xl font-bold text-gray-800">{(filteredData.reduce((sum, d) => sum + d.finalScore, 0) / (filteredData.length || 1)).toFixed(1)} <span className="text-base font-normal">分</span></p>
                    </div>
                </div>
                {/* Card 3 */}
                <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-200 flex items-center gap-5">
                    <div className="p-3 bg-purple-100 rounded-full">
                        <CheckCircle className="w-6 h-6 text-purple-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 font-medium">合格率</p>
                        <p className="text-2xl font-bold text-gray-800">{(filteredData.filter(d => d.qualityResult === '合格').length / (filteredData.length || 1) * 100).toFixed(1)}<span className="text-base font-normal">%</span></p>
                    </div>
                </div>
                {/* Card 4 */}
                <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-200 flex items-center gap-5">
                    <div className="p-3 bg-orange-100 rounded-full">
                        <AlertCircle className="w-6 h-6 text-orange-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 font-medium">进入人工复核</p>
                        <p className="text-2xl font-bold text-gray-800">{filteredData.filter(d => d.appealStatus === 'pending' || d.appealStatus === 'processed').length}</p>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              {/* 搜索筛选区域 */}
              <UnifiedSearchFilter
                fields={filterFields}
                filters={searchFilters}
                onFiltersChange={handleFiltersChange}
                onSearch={handleSearch}
                onReset={handleReset}
              />
              <div className="overflow-x-auto mt-6">
                <table className="w-full text-sm text-left text-gray-600">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" className="px-4 py-3">序号</th>
                            <th scope="col" className="px-6 py-3">记录编号</th>
                            <th scope="col" className="px-6 py-3">坐席</th>
                            <th scope="col" className="px-6 py-3">所属班组</th>
                            <th scope="col" className="px-6 py-3">客户号码</th>
                            <th scope="col" className="px-6 py-3">通话开始时间</th>
                            <th scope="col" className="px-6 py-3">通话时长</th>
                            <th scope="col" className="px-6 py-3">最终得分</th>
                            <th scope="col" className="px-6 py-3">质检结果</th>
                            <th scope="col" className="px-6 py-3">申诉信息</th>
                            <th scope="col" className="px-6 py-3 text-right">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {paginatedData.map((detail, index) => (
                          <tr key={detail.id} className="bg-white border-b hover:bg-gray-50">
                              <td className="px-4 py-4 text-gray-700">{(pagination.current - 1) * pagination.pageSize + index + 1}</td>
                              <td className="px-6 py-4">
                                <Link to={`/final-design/multi-mode-session-detail/${detail.id}?viewMode=basic_view`} className="text-blue-600 hover:text-blue-800 font-medium">
                                  {detail.recordNumber}
                                </Link>
                              </td>
                              <td className="px-6 py-4">{`${detail.agentName}/${detail.agentId}`}</td>
                              <td className="px-6 py-4">{detail.teamName}</td>
                              <td className="px-6 py-4">{detail.customerNumber}</td>
                              <td className="px-6 py-4">{detail.callStartTime}</td>
                              <td className="px-6 py-4">{detail.callDuration}</td>
                              <td className="px-6 py-4">{getScoreEvolutionDisplay(detail)}</td>
                              <td className="px-6 py-4">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${detail.qualityResult === '合格' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                  {detail.qualityResult}
                                </span>
                              </td>
                              <td className="px-6 py-4">{getAppealStatusDisplay(detail)}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <Link to={`/final-design/multi-mode-session-detail/${detail.id}?viewMode=basic_view`} className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors" title="查看详情">
                                  <Eye className="w-4 h-4" />
                                </Link>
                              </td>
                          </tr>
                        ))}
                    </tbody>
                </table>
              </div>
              {filteredData.length === 0 && (
                  <div className="text-center py-10 text-gray-500">
                      <p>未找到匹配的记录。</p>
                  </div>
              )}
              {filteredData.length > 0 && (
                <UnifiedPagination
                   current={pagination.current}
                   pageSize={pagination.pageSize}
                   total={filteredData.length}
                   onChange={(page) => setPagination(prev => ({ ...prev, current: page }))}
                 />
              )}
            </div>
        </motion.div>
      </main>
    </div>
  );
};

export default FinalTaskDetailPage;