### “能量检测”算子核心用途

**能量检测**是通话的**“情绪晴雨表”**。它通过客观地量化语音的音量大小及其变化，来捕捉那些无法在文本中体现的情绪波动。其核心用途是自动识别出对话中**情绪激动**、**态度冷漠**或**情绪剧烈变化**的时刻。

---

### 方式一：能量范围检测 (绝对音量检测)

这是最基础的用法，用于识别音量持续过高或过低的说话者。

#### 原子性示例 1A：识别客户咆哮（持续高能量）

*   **原子性目标**：只找出那些持续大声说话、可能处于愤怒状态的客户。
*   **原子性配置**：
    *   **检测角色**：客户
    *   **检查方式**：能量范围检测
    *   **检查逻辑**：语音能量等级 **大于 7**
*   **场景举例**：
    *   客户（音量极大）：“我受够了！你们的产品根本就是垃圾！”
*   **分析**：
    *   规则命中。一个正常说话的能量等级可能在4-5，达到7或以上几乎可以肯定是吼叫或咆哮。此规则能精准地从所有通话中筛选出客户情绪失控的最高风险案例，需要立刻进行人工干预和安抚。

#### 原子性示例 1B：识别客服低语（持续低能量）

*   **原子性目标**：只找出那些说话声音过小、可能显得不自信或敷衍的客服。
*   **原子性配置**：
    *   **检测角色**：客服
    *   **检查方式**：能量范围检测
    *   **检查逻辑**：语音能量等级 **小于 3**
*   **场景举例**：
    *   客户：“这个新功能具体怎么用？”
    *   客服（声音微弱，含糊不清）：“嗯…这个…你点那个设置…然后…”
*   **分析**：
    *   规则命中。客服声音过小，会让客户听不清，感觉非常不专业。这可能反映了客服对业务不熟练、缺乏自信，或者是工作态度疲惫懈怠。筛选出这些案例有助于进行针对性培训。

---

### 方式二：相邻句能量波动 (情绪突变检测)

这种方式更精妙，它不关心绝对音量，而是关心音量的**瞬间剧烈变化**，这往往是情绪爆发的拐点。

#### 原子性示例 2A：识别客户情绪由平稳到爆发的瞬间

*   **原子性目标**：只找出客户音量突然增大的那个瞬间。
*   **原子性配置**：
    *   **检测角色**：客户
    *   **检查方式**：相邻句能量波动
    *   **检查逻辑**：能量差大于或等于 **4**
*   **场景举例**：
    *   客户（能量等级4，正常）：“请问我的退款处理得怎么样了？”
    *   客服：“抱歉先生，您的申请被驳回了。”
    *   客户（能量等级8，咆哮）：“什么？！为什么驳回！你们什么意思！”
*   **分析**：
    *   规则会在客户说出“什么？！”这一句时命中。因为这一句的能量（8）与前一句（4）的能量差是4，达到了阈值。这精准地捕捉到了客户情绪从平稳询问到愤怒质问的转折点，非常有价值，便于分析是哪个环节或哪句话术引发了客户的负面情绪。

---

### 方式三：最大能量跨度 (情绪波动范围检测)

这种方式着眼于全局，评估一个角色在整通电话中情绪波动的剧烈程度。

#### 原子性示例 3A：识别情绪大起大落的客服

*   **原子性目标**：只找出那些在通话中声音忽高忽低、情绪不稳定的客服。
*   **原子性配置**：
    *   **检测角色**：客服
    *   **检查方式**：最大能量跨度
    *   **检查逻辑**：能量差大于或等于 **5**
*   **场景举-例**：
    *   在通话开始时，客服用非常轻柔的声音（能量等级2）向客户问好。
    *   在通话中段，因为客户的质疑而变得不耐烦，用很大的声音（能量等级7）反驳客户。
    *   在通话结尾，又恢复了轻柔的语气（能量等级3）。
*   **分析**：
    *   规则命中。因为该客服在通话中的最大能量（7）和最小能量（2）之差为5，达到了设定的阈值。这表明该客服情绪控制能力较差，服务状态不稳定。即使他没有持续大吼，但这种剧烈的波动本身就是一个值得关注的问题，需要管理者进行沟通和辅导。