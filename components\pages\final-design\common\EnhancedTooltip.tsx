import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface TooltipPayload {
  name?: string;
  value?: number | string;
  color?: string;
  dataKey?: string;
  payload?: any;
  unit?: string;
}

interface EnhancedTooltipProps {
  active?: boolean;
  payload?: TooltipPayload[];
  label?: string;
  labelFormatter?: (label: string) => string;
  valueFormatter?: (value: number | string, name?: string) => string;
  separator?: string;
  contentStyle?: React.CSSProperties;
  labelStyle?: React.CSSProperties;
  itemStyle?: React.CSSProperties;
  wrapperStyle?: React.CSSProperties;
  cursor?: boolean;
  coordinate?: { x: number; y: number };
  position?: { x: number; y: number };
  viewBox?: { x: number; y: number; width: number; height: number };
  isAnimationActive?: boolean;
  animationDuration?: number;
  animationEasing?: string;
  offset?: number;
  filterNull?: boolean;
  itemSorter?: (a: TooltipPayload, b: TooltipPayload) => number;
  allowEscapeViewBox?: { x?: boolean; y?: boolean };
  reverseDirection?: { x?: boolean; y?: boolean };
  useTranslate3d?: boolean;
}

/**
 * 增强的图表工具提示组件
 * 提供现代化的设计和流畅的动画效果
 */
export const EnhancedTooltip: React.FC<EnhancedTooltipProps> = ({
  active,
  payload,
  label,
  labelFormatter,
  valueFormatter,
  separator = ' : ',
}) => {
  if (!active || !payload || !payload.length) {
    return null;
  }

  const formatLabel = (label: string) => {
    if (labelFormatter) {
      return labelFormatter(label);
    }
    return label;
  };

  const formatValue = (value: number | string, name?: string) => {
    if (valueFormatter) {
      return valueFormatter(value, name);
    }
    
    if (typeof value === 'number') {
      // 格式化数字，添加千分位分隔符
      return value.toLocaleString('zh-CN');
    }
    
    return value;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        transition={{ 
          duration: 0.2, 
          ease: "easeOut",
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        className="bg-white/98 backdrop-blur-md border border-slate-200/60 rounded-2xl shadow-2xl shadow-slate-900/10 p-4 min-w-[200px] max-w-[320px]"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.8)',
        }}
      >
        {/* 标签 */}
        {label && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="text-sm font-semibold text-slate-700 mb-3 pb-2 border-b border-slate-100"
          >
            {formatLabel(label)}
          </motion.div>
        )}

        {/* 数据项 */}
        <div className="space-y-2">
          {payload.map((entry, index) => (
            <motion.div
              key={`tooltip-item-${index}`}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 + index * 0.05 }}
              className="flex items-center justify-between gap-3"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                {/* 颜色指示器 */}
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0 shadow-sm"
                  style={{ 
                    backgroundColor: entry.color,
                    boxShadow: `0 0 0 2px ${entry.color}20`
                  }}
                />
                
                {/* 数据名称 */}
                <span className="text-sm font-medium text-slate-600 truncate">
                  {entry.name}
                </span>
              </div>

              {/* 数据值 */}
              <div className="flex items-center gap-1 flex-shrink-0">
                <span className="text-sm font-bold text-slate-900">
                  {formatValue(entry.value || 0, entry.name)}
                </span>
                {entry.unit && (
                  <span className="text-xs text-slate-500 font-medium">
                    {entry.unit}
                  </span>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部装饰 */}
        <motion.div
          initial={{ scaleX: 0 }}
          animate={{ scaleX: 1 }}
          transition={{ delay: 0.3, duration: 0.3 }}
          className="mt-3 pt-2 border-t border-slate-100"
        >
          <div className="flex items-center justify-center">
            <div className="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full" />
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

/**
 * 简化的工具提示组件（用于简单场景）
 */
export const SimpleTooltip: React.FC<{
  active?: boolean;
  payload?: TooltipPayload[];
  label?: string;
}> = ({ active, payload, label }) => {
  if (!active || !payload || !payload.length) {
    return null;
  }

  return (
    <div className="bg-white/95 backdrop-blur-sm border border-slate-200 rounded-xl shadow-lg p-3">
      {label && (
        <div className="text-sm font-medium text-slate-700 mb-2">
          {label}
        </div>
      )}
      {payload.map((entry, index) => (
        <div key={index} className="flex items-center gap-2 text-sm">
          <div
            className="w-2 h-2 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span className="text-slate-600">{entry.name}:</span>
          <span className="font-semibold text-slate-900">{entry.value}</span>
        </div>
      ))}
    </div>
  );
};

export default EnhancedTooltip;
