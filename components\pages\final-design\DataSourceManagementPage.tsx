import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Database, 
  Plus, 
  Edit, 
  Trash2, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Eye,
  HardDrive,
  Server,
  Upload,
  Key,
  Shield,
  AlertCircle,
  FileText,
  Settings
} from 'lucide-react';
import { Drawer, Form, Input, Radio, Select, Button, Upload as AntUpload, message } from 'antd';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import UnifiedPagination from './components/UnifiedPagination';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';

/**
 * 数据源配置接口
 */
interface DataSource {
  id: string;
  name: string;
  type: 'directory' | 'api' | 'database';
  status: 'connected' | 'error' | 'testing';
  config: {
    // 监控目录配置
      protocol?: 'sftp' | 'ftp' | 'smb';
      serverAddress?: string;
      port?: number;
      recordingPath?: string;
      metadataPath?: string;
      authMethod?: 'password' | 'key';
      username?: string;
      password?: string;
      keyFile?: string;
    
    // API接口配置
    baseUrl?: string;
    audioProcessStrategy?: 'sync_download' | 'external_link';
    authType?: 'none' | 'api_key' | 'bearer_token' | 'oauth2';
    apiKeyName?: string;
    apiKeyValue?: string;
    apiKeyLocation?: 'header' | 'query';
    bearerToken?: string;
    oauthClientId?: string;
    oauthClientSecret?: string;
    oauthTokenUrl?: string;
    
    // 数据库配置
    dbType?: 'mysql' | 'postgresql' | 'sqlserver' | 'oracle';
    dbHost?: string;
    dbPort?: number;
    dbName?: string;
    dbUsername?: string;
    dbPassword?: string;
    sqlTemplate?: string;
    
    // 字段映射
    fieldMappings?: FieldMapping[];
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 标准化字段映射接口
 */
interface FieldMapping {
  systemField: string;
  systemFieldLabel: string;
  externalField: string;
  sampleValue: string;
  required: boolean;
  description: string;
}

/**
 * 测试结果接口
 */
interface TestResult {
  success: boolean;
  message: string;
  sampleData?: any;
}

/**
 * 数据源管理页面组件
 */
const DataSourceManagementPage: React.FC = () => {
  // 状态管理
  const [dataSources, setDataSources] = useState<DataSource[]>([
    {
      id: '1',
      name: '呼叫中心录音SFTP目录',
      type: 'directory',
      status: 'connected',
      config: {
        protocol: 'sftp',
        serverAddress: 'sftp.callcenter.com',
        port: 22,
         recordingPath: '/data/call_records/recordings/',
         metadataPath: '/data/call_records/metadata/',
         authMethod: 'password',
         username: 'qc_user',
         password: '******',
        fieldMappings: [
          { systemField: 'recording_id', systemFieldLabel: '录音唯一ID', externalField: 'callId', sampleValue: 'call-123xyz', required: true, description: '通话记录的唯一标识符' },
          { systemField: 'start_time', systemFieldLabel: '通话开始时间', externalField: 'callInfo.startTime', sampleValue: '2023-11-15T10:30:00Z', required: true, description: '通话开始的时间戳' },
          { systemField: 'agent_id', systemFieldLabel: '坐席工号', externalField: 'agent.id', sampleValue: '8001', required: true, description: '处理通话的坐席工号' },
          { systemField: 'skill_group', systemFieldLabel: '技能组', externalField: 'agent.group', sampleValue: 'Sales', required: false, description: '坐席所属的技能组' },
          { systemField: 'customer_number', systemFieldLabel: '客户号码', externalField: 'customer.phone', sampleValue: '138****1234', required: false, description: '客户的联系电话' },
          { systemField: 'audio_url', systemFieldLabel: '录音文件地址', externalField: 'audioFile.path', sampleValue: '/recordings/2023/11/call-123xyz.wav', required: true, description: '录音文件的存储路径或URL' }
        ]
      },
      createdBy: '管理员',
      createdAt: '2023-11-15 10:30:00',
      updatedAt: '2023-11-15 10:30:00'
    },
    {
      id: '2',
      name: '客服系统A通话记录API',
      type: 'api',
      status: 'connected',
      config: {
        baseUrl: 'https://api.callcenter.com/v1/',
        audioProcessStrategy: 'sync_download',
        authType: 'api_key',
        apiKeyName: 'X-Api-Key',
        apiKeyValue: '******',
        apiKeyLocation: 'header',
        fieldMappings: [
          { systemField: 'recording_id', systemFieldLabel: '录音唯一ID', externalField: 'data.call_id', sampleValue: 'api-call-456', required: true, description: '通话记录的唯一标识符' },
          { systemField: 'start_time', systemFieldLabel: '通话开始时间', externalField: 'data.start_timestamp', sampleValue: '2023-11-15T14:20:00Z', required: true, description: '通话开始的时间戳' },
          { systemField: 'agent_id', systemFieldLabel: '坐席工号', externalField: 'data.agent_code', sampleValue: '9002', required: true, description: '处理通话的坐席工号' },
          { systemField: 'skill_group', systemFieldLabel: '技能组', externalField: 'data.department', sampleValue: 'Support', required: false, description: '坐席所属的技能组' },
          { systemField: 'customer_number', systemFieldLabel: '客户号码', externalField: 'data.caller_number', sampleValue: '139****5678', required: false, description: '客户的联系电话' },
          { systemField: 'audio_url', systemFieldLabel: '录音文件地址', externalField: 'data.recording_url', sampleValue: 'https://cdn.example.com/recordings/api-call-456.mp3', required: true, description: '录音文件的存储路径或URL' }
        ]
      },
      createdBy: '管理员',
      createdAt: '2023-11-14 16:45:00',
      updatedAt: '2023-11-14 16:45:00'
    },
    {
      id: '3',
      name: '呼叫中心业务数据库',
      type: 'database',
      status: 'testing',
      config: {
        dbType: 'mysql',
        dbHost: 'db.callcenter.com',
        dbPort: 3306,
        dbName: 'callcenter_db',
        dbUsername: 'qc_readonly',
        dbPassword: '******',
        audioProcessStrategy: 'sync_download',
        sqlTemplate: "SELECT call_id as recording_id, agent_id, start_time, skill_group, customer_phone as customer_number, recording_url as audio_url FROM calls WHERE start_time > '{{last_execution_time}}' ORDER BY start_time DESC LIMIT 100",
        fieldMappings: [
          { systemField: 'recording_id', systemFieldLabel: '录音唯一ID', externalField: 'recording_id', sampleValue: 'db-call-789', required: true, description: '通话记录的唯一标识符' },
          { systemField: 'start_time', systemFieldLabel: '通话开始时间', externalField: 'start_time', sampleValue: '2023-11-15T09:15:00Z', required: true, description: '通话开始的时间戳' },
          { systemField: 'agent_id', systemFieldLabel: '坐席工号', externalField: 'agent_id', sampleValue: '7003', required: true, description: '处理通话的坐席工号' },
          { systemField: 'skill_group', systemFieldLabel: '技能组', externalField: 'skill_group', sampleValue: 'Technical', required: false, description: '坐席所属的技能组' },
          { systemField: 'customer_number', systemFieldLabel: '客户号码', externalField: 'customer_number', sampleValue: '137****9012', required: false, description: '客户的联系电话' },
          { systemField: 'audio_url', systemFieldLabel: '录音文件地址', externalField: 'audio_url', sampleValue: 'https://storage.example.com/recordings/db-call-789.wav', required: true, description: '录音文件的存储路径或URL' }
        ]
      },
      createdBy: '管理员',
      createdAt: '2023-11-13 11:20:00',
      updatedAt: '2023-11-15 09:30:00'
    }
  ]);
  
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editingDataSource, setEditingDataSource] = useState<DataSource | null>(null);
  const [form] = Form.useForm();
  const [selectedType, setSelectedType] = useState<string>('');
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  // 筛选字段配置
  const filterFields: FilterField[] = [
    {
      key: 'search',
      label: '数据源名称',
      type: 'text',
      placeholder: '按数据源名称搜索...',
      width: 'w-full md:w-72'
    },
    {
      key: 'type',
      type: 'select',
      label: '数据源类型',
      placeholder: '所有类型',
      options: [
        { label: '所有类型', value: 'all' },
        { label: '监控目录', value: 'directory' },
        { label: 'API接口', value: 'api' },
        { label: '数据库直连', value: 'database' }
      ],
      width: 'w-full md:w-48'
    },
    {
      key: 'status',
      type: 'select',
      label: '连接状态',
      placeholder: '所有状态',
      options: [
        { label: '所有状态', value: 'all' },
        { label: '正常', value: 'connected' },
        { label: '异常', value: 'error' },
        { label: '未测试', value: 'testing' }
      ],
      width: 'w-full md:w-48'
    }
  ];

  // 处理搜索
  const handleSearch = () => {
    console.log('搜索筛选条件:', filters);
    // 这里可以添加实际的搜索逻辑
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({});
    console.log('重置筛选条件');
    // 这里可以添加实际的重置逻辑
  };

  // 数据源类型选项
  const typeOptions = [
    { value: 'directory', label: '监控目录', icon: HardDrive },
    { value: 'api', label: 'API接口', icon: Server },
    { value: 'database', label: '数据库直连', icon: Database }
  ];

  // 标准字段映射模板
  const getStandardFieldMappings = (): FieldMapping[] => [
    { systemField: 'recording_id', systemFieldLabel: '录音唯一ID', externalField: '', sampleValue: '', required: true, description: '通话记录的唯一标识符' },
    { systemField: 'start_time', systemFieldLabel: '通话开始时间', externalField: '', sampleValue: '', required: true, description: '通话开始的时间戳' },
    { systemField: 'agent_id', systemFieldLabel: '坐席工号', externalField: '', sampleValue: '', required: true, description: '处理通话的坐席工号' },
    { systemField: 'skill_group', systemFieldLabel: '技能组', externalField: '', sampleValue: '', required: false, description: '坐席所属的技能组' },
    { systemField: 'customer_number', systemFieldLabel: '客户号码', externalField: '', sampleValue: '', required: false, description: '客户的联系电话' },
    { systemField: 'audio_url', systemFieldLabel: '录音文件地址', externalField: '', sampleValue: '', required: true, description: '录音文件的存储路径或URL' }
  ];



  /**
   * 处理新建数据源
   */
  const handleCreate = () => {
    setEditingDataSource(null);
    setSelectedType('');
    setFieldMappings(getStandardFieldMappings());
    setTestResult(null);
    form.resetFields();
    setDrawerVisible(true);
  };

  /**
   * 处理编辑数据源
   */
  const handleEdit = (record: DataSource) => {
    setEditingDataSource(record);
    setSelectedType(record.type);
    setFieldMappings(record.config.fieldMappings || getStandardFieldMappings());
    setTestResult(null);
    form.setFieldsValue({
      name: record.name,
      type: record.type,
      ...record.config,
      recordingPath: record.config.recordingPath,
      metadataPath: record.config.metadataPath
    });
    setDrawerVisible(true);
  };

  /**
   * 处理保存数据源
   */
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const config = {
        ...values,
        recordingPath: values.recordingPath,
        metadataPath: values.metadataPath,
        fieldMappings: fieldMappings
      };
      delete config.name;
      delete config.type;

      if (editingDataSource) {
        // 更新现有数据源
        setDataSources(prev => prev.map(ds => 
          ds.id === editingDataSource.id 
            ? { ...ds, name: values.name, type: values.type, config, updatedAt: new Date().toLocaleString('zh-CN') }
            : ds
        ));
        message.success('数据源更新成功');
      } else {
        // 创建新数据源
        const newDataSource: DataSource = {
          id: Date.now().toString(),
          name: values.name,
          type: values.type,
          status: 'testing',
          config,
          createdBy: '当前用户',
          createdAt: new Date().toLocaleString('zh-CN'),
          updatedAt: new Date().toLocaleString('zh-CN')
        };
        setDataSources(prev => [...prev, newDataSource]);
        message.success('数据源创建成功');
      }
      setDrawerVisible(false);
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  /**
   * 处理删除数据源
   */
  const handleDelete = (id: string) => {
    setDataSources(prev => prev.filter(ds => ds.id !== id));
    message.success('数据源删除成功');
  };

  /**
   * 处理测试连接
   */
  const handleTestConnection = async (record: DataSource) => {
    setTestResult({ success: false, message: '正在测试连接...' });
    
    // 模拟测试连接
    setTimeout(() => {
      try {
        // 验证配置信息
        if (record.type === 'directory') {
          if (!record.config.serverAddress || !record.config.recordingPath || !record.config.metadataPath) {
            throw new Error('服务器地址、录音文件路径和元数据文件路径不能为空');
          }
        }
        
        const mockSampleData = {
          directory: {
            callId: 'test-call-001',
            callInfo: { startTime: '2023-11-15T15:30:00Z' },
            agent: { id: '8001', group: 'Sales' },
            customer: { phone: '138****1234' },
            audioFile: { path: record.config.recordingPath + '2023/11/test-call-001.wav' },
            metadataFile: { path: record.config.metadataPath + 'cdr_2023-11-15.csv' }
          },
          api: {
            data: {
              call_id: 'api-test-002',
              start_timestamp: '2023-11-15T15:30:00Z',
              agent_code: '9002',
              department: 'Support',
              caller_number: '139****5678',
              recording_url: 'https://cdn.example.com/recordings/api-test-002.mp3'
            }
          },
          database: {
            recording_id: 'db-test-003',
            start_time: '2023-11-15T15:30:00Z',
            agent_id: '7003',
            skill_group: 'Technical',
            customer_number: '137****9012',
            audio_url: 'https://storage.example.com/recordings/db-test-003.wav'
          }
        };

        setTestResult({
          success: true,
          message: '连接测试成功，已获取样本数据',
          sampleData: mockSampleData[record.type]
        });

        // 更新数据源状态
        setDataSources(prev => prev.map(ds => 
          ds.id === record.id ? { ...ds, status: 'connected' } : ds
        ));
      } catch (error) {
        setTestResult({
          success: false,
          message: error instanceof Error ? error.message : '连接测试失败'
        });
      }
    }, 2000);
  };

  /**
   * 处理当前配置的测试连接
   */
  const handleTestCurrentConnection = async () => {
    try {
      const values = await form.validateFields();
      setTestResult({ success: false, message: '正在测试连接...' });
      
      // 模拟测试连接并获取样本数据
      setTimeout(() => {
        const mockSampleData = {
          directory: {
            callId: 'test-call-001',
            callInfo: { startTime: '2023-11-15T15:30:00Z' },
            agent: { id: '8001', group: 'Sales' },
            customer: { phone: '138****1234' },
            audioFile: { path: '/recordings/2023/11/test-call-001.wav' }
          },
          api: {
            data: {
              call_id: 'api-test-002',
              start_timestamp: '2023-11-15T15:30:00Z',
              agent_code: '9002',
              department: 'Support',
              caller_number: '139****5678',
              recording_url: 'https://cdn.example.com/recordings/api-test-002.mp3'
            }
          },
          database: {
            recording_id: 'db-test-003',
            start_time: '2023-11-15T15:30:00Z',
            agent_id: '7003',
            skill_group: 'Technical',
            customer_number: '137****9012',
            audio_url: 'https://storage.example.com/recordings/db-test-003.wav'
          }
        };

        const sampleData = mockSampleData[selectedType as keyof typeof mockSampleData];
        
        // 自动填充字段映射的示例值
        if (sampleData) {
          const updatedMappings = fieldMappings.map(mapping => {
            if (mapping.externalField) {
              const value = getNestedValue(sampleData, mapping.externalField);
              return { ...mapping, sampleValue: value || '' };
            }
            return mapping;
          });
          setFieldMappings(updatedMappings);
        }

        setTestResult({
          success: true,
          message: '连接测试成功，已自动填充字段映射示例值',
          sampleData
        });
      }, 2000);
    } catch (error) {
      setTestResult({
        success: false,
        message: '请先完善配置信息'
      });
    }
  };

  /**
   * 获取嵌套对象的值
   */
  const getNestedValue = (obj: any, path: string): string => {
    return path.split('.').reduce((current, key) => current?.[key], obj) || '';
  };

  /**
   * 更新字段映射
   */
  const updateFieldMapping = (index: number, field: keyof FieldMapping, value: string) => {
    const newMappings = [...fieldMappings];
    newMappings[index] = { ...newMappings[index], [field]: value };
    setFieldMappings(newMappings);
  };

  /**
   * 渲染配置表单
   */
  const renderConfigForm = () => {
    if (!selectedType) return null;

    switch (selectedType) {
      case 'directory':
        return (
          <div className="space-y-4">
            <Form.Item name="protocol" label="连接协议" rules={[{ required: true, message: '请选择连接协议' }]}>
              <Select placeholder="选择连接协议">
                <Select.Option value="sftp">SFTP</Select.Option>
                <Select.Option value="ftp">FTP</Select.Option>
                <Select.Option value="smb">本地/网络共享路径(SMB)</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="serverAddress" label="服务器地址" rules={[{ required: true, message: '请输入服务器地址' }]}>
              <Input placeholder="如：sftp.example.com" />
            </Form.Item>
            <Form.Item name="port" label="端口" rules={[{ required: true, message: '请输入端口' }]}>
              <Input placeholder="如：22" type="number" />
            </Form.Item>
             <Form.Item name="recordingPath" label="录音文件路径" rules={[{ required: true, message: '请输入录音文件路径' }]}>
               <Input placeholder="如：/data/call_records/recordings/" />
             </Form.Item>
             <Form.Item name="metadataPath" label="元数据文件路径" rules={[{ required: true, message: '请输入元数据文件路径' }]}>
               <Input placeholder="如：/data/call_records/metadata/" />
             </Form.Item>
            <Form.Item name="authMethod" label="认证方式" rules={[{ required: true, message: '请选择认证方式' }]}>
              <Radio.Group>
                <Radio value="password">用户名/密码</Radio>
                <Radio value="key">密钥文件</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="username" label="用户名" rules={[{ required: true, message: '请输入用户名' }]}>
              <Input placeholder="用户名" />
            </Form.Item>
            <Form.Item name="password" label="密码">
              <Input.Password placeholder="密码" />
            </Form.Item>
          </div>
        );

      case 'api':
        return (
          <div className="space-y-4">
            <Form.Item name="baseUrl" label="基础URL" rules={[{ required: true, message: '请输入基础URL' }]}>
              <Input placeholder="如：https://api.callcenter.com/v1/" />
            </Form.Item>
            <Form.Item name="audioProcessStrategy" label="录音文件处理策略" rules={[{ required: true, message: '请选择处理策略' }]}>
              <Radio.Group>
                <Radio value="sync_download">同步拉取存储（推荐）</Radio>
                <Radio value="external_link">引用外部链接</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="authType" label="认证方式" rules={[{ required: true, message: '请选择认证方式' }]}>
              <Select placeholder="选择认证方式">
                <Select.Option value="none">无认证</Select.Option>
                <Select.Option value="api_key">API Key</Select.Option>
                <Select.Option value="bearer_token">Bearer Token</Select.Option>
                <Select.Option value="oauth2">OAuth 2.0</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="apiKeyName" label="Key 名称">
              <Input placeholder="如：X-Api-Key" />
            </Form.Item>
            <Form.Item name="apiKeyValue" label="Key 值">
              <Input.Password placeholder="API Key 值" />
            </Form.Item>
            <Form.Item name="apiKeyLocation" label="添加到">
              <Select placeholder="选择位置">
                <Select.Option value="header">请求头 Header</Select.Option>
                <Select.Option value="query">查询参数 Query Params</Select.Option>
              </Select>
            </Form.Item>
          </div>
        );

      case 'database':
        return (
          <div className="space-y-4">
            <Form.Item name="dbType" label="数据库类型" rules={[{ required: true, message: '请选择数据库类型' }]}>
              <Select placeholder="选择数据库类型">
                <Select.Option value="mysql">MySQL</Select.Option>
                <Select.Option value="postgresql">PostgreSQL</Select.Option>
                <Select.Option value="sqlserver">SQL Server</Select.Option>
                <Select.Option value="oracle">Oracle</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="dbHost" label="服务器地址/主机名" rules={[{ required: true, message: '请输入服务器地址' }]}>
              <Input placeholder="如：db.callcenter.com" />
            </Form.Item>
            <Form.Item name="dbPort" label="端口" rules={[{ required: true, message: '请输入端口' }]}>
              <Input placeholder="如：3306" type="number" />
            </Form.Item>
            <Form.Item name="dbName" label="数据库名称/SID" rules={[{ required: true, message: '请输入数据库名称' }]}>
              <Input placeholder="数据库名称" />
            </Form.Item>
            <Form.Item name="dbUsername" label="用户名" rules={[{ required: true, message: '请输入用户名' }]}>
              <Input placeholder="数据库用户名" />
            </Form.Item>
            <Form.Item name="dbPassword" label="密码" rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password placeholder="数据库密码" />
            </Form.Item>
            <Form.Item name="audioProcessStrategy" label="录音文件处理策略" rules={[{ required: true, message: '请选择处理策略' }]}>
              <Radio.Group>
                <Radio value="sync_download">同步拉取存储（推荐）</Radio>
                <Radio value="external_link">引用外部链接</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="sqlTemplate" label="SQL查询模板" rules={[{ required: true, message: '请输入SQL查询模板' }]}>
              <Input.TextArea 
                rows={4} 
                placeholder="SELECT call_id as recording_id, agent_id, start_time FROM calls WHERE start_time > '{{last_execution_time}}'" 
              />
            </Form.Item>
            <div className="bg-amber-50 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-amber-800">
                  <div className="font-medium">SQL查询模板说明：</div>
                  <div className="mt-1">用于查询需要质检的通话记录。必须包含在字段映射中定义的字段别名。可以使用预设的时间变量（如 {"{{last_execution_time}}"}）来实现增量同步。</div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <UnifiedPageHeader
        title="数据源管理"
        subtitle="配置和管理所有与外部系统对接的数据源"
        icon={Database}
        badge={{ text: `共 ${dataSources.length} 个数据源`, color: "blue" }}
        actions={[
          {
            label: "新建数据源",
            icon: Plus,
            variant: "primary",
            onClick: handleCreate
          }
        ]}
      />

      {/* 主要内容区域 */}
      <main className="p-6 md:p-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
          {/* 统一查询筛选区域 */}
          <UnifiedSearchFilter
            fields={filterFields}
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
            onReset={handleResetFilters}
            isExpanded={isFilterExpanded}
            onToggleExpanded={() => setIsFilterExpanded(!isFilterExpanded)}
            className="mb-6"
          />

          {/* 数据源表格 */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left text-gray-600">
              <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                  <th scope="col" className="px-4 py-3">序号</th>
                  <th scope="col" className="px-6 py-3">数据源名称</th>
                  <th scope="col" className="px-6 py-3">数据源类型</th>
                  <th scope="col" className="px-6 py-3">连接状态</th>
                  <th scope="col" className="px-6 py-3">创建人</th>
                  <th scope="col" className="px-6 py-3">创建时间</th>
                  <th scope="col" className="px-6 py-3 text-right">操作</th>
                </tr>
              </thead>
              <tbody>
                {dataSources.map((dataSource, index) => (
                  <tr key={dataSource.id} className="bg-white border-b hover:bg-gray-50">
                    <td className="px-4 py-4 text-gray-700">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          {dataSource.type === 'directory' && <HardDrive className="w-5 h-5 text-blue-600" />}
                          {dataSource.type === 'api' && <Server className="w-5 h-5 text-orange-600" />}
                          {dataSource.type === 'database' && <Database className="w-5 h-5 text-purple-600" />}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{dataSource.name}</div>
                          <div className="text-sm text-gray-500">
                            {dataSource.type === 'directory' && '监控目录'}
                            {dataSource.type === 'api' && 'API接口'}
                            {dataSource.type === 'database' && '数据库直连'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {(() => {
                        const typeMap = {
                          directory: { label: '监控目录', color: 'bg-blue-100 text-blue-800' },
                          api: { label: 'API接口', color: 'bg-orange-100 text-orange-800' },
                          database: { label: '数据库直连', color: 'bg-purple-100 text-purple-800' }
                        };
                        const typeInfo = typeMap[dataSource.type as keyof typeof typeMap];
                        return (
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                            {typeInfo.label}
                          </span>
                        );
                      })()} 
                    </td>
                    <td className="px-6 py-4">
                      {(() => {
                        const statusMap = {
                          connected: { label: '正常', color: 'text-green-600', bg: 'bg-green-100', icon: CheckCircle },
                          error: { label: '异常', color: 'text-red-600', bg: 'bg-red-100', icon: XCircle },
                          testing: { label: '未测试', color: 'text-gray-600', bg: 'bg-gray-100', icon: AlertCircle }
                        };
                        const statusInfo = statusMap[dataSource.status as keyof typeof statusMap];
                        const IconComponent = statusInfo.icon;
                        return (
                          <div className="flex items-center gap-2">
                            <div className={`w-6 h-6 rounded-full flex items-center justify-center ${statusInfo.bg}`}>
                              <IconComponent className={`w-3 h-3 ${statusInfo.color}`} />
                            </div>
                            <span className={`text-sm font-medium ${statusInfo.color}`}>{statusInfo.label}</span>
                          </div>
                        );
                      })()} 
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm text-gray-900">{dataSource.createdBy}</span>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm text-gray-500">{dataSource.createdAt}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleEdit(dataSource)}
                        className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                        title="编辑"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleTestConnection(dataSource)}
                        className="text-green-600 hover:text-green-900 p-1.5 hover:bg-green-50 rounded-lg transition-colors mx-1"
                        title="测试连接"
                      >
                        <TestTube className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(dataSource.id)}
                        className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                        title="删除"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
               </tbody>
             </table>
           </div>
           {dataSources.length === 0 && (
             <div className="text-center py-10 text-gray-500">
               <p>暂无数据源。</p>
             </div>
           )}
           
           {/* 分页 */}
           {dataSources.length > 0 && (
             <UnifiedPagination
               current={1}
               total={dataSources.length}
               pageSize={10}
               onChange={() => {}}
             />
           )}
          
          {/* 测试连接结果 */}
          {testResult && (
            <div className="mt-6">
              <div className={`p-4 rounded-lg border ${testResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                <div className="flex items-start gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 ${testResult.success ? 'bg-green-100' : 'bg-red-100'}`}>
                    {testResult.success ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className={`text-sm font-medium ${testResult.success ? 'text-green-800' : 'text-red-800'}`}>
                      {testResult.success ? '连接测试成功' : '连接测试失败'}
                    </h4>
                    <p className={`text-xs mt-1 ${testResult.success ? 'text-green-700' : 'text-red-700'}`}>
                      {testResult.message}
                    </p>
                  </div>
                </div>
              </div>
              
              {testResult.success && testResult.sampleData && (
                <div className="mt-4 bg-white p-4 rounded-lg border border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <Eye className="w-4 h-4 text-blue-600" />
                    样本数据预览
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                    <pre className="text-xs text-gray-700 overflow-auto max-h-40 whitespace-pre-wrap">
                      {JSON.stringify(testResult.sampleData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}
           

        </motion.div>
      </main>

      {/* 新建/编辑数据源抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Database className="w-4 h-4 text-blue-600" />
            </div>
            <span className="text-lg font-semibold text-gray-900">
              {editingDataSource ? '编辑数据源' : '新建数据源'}
            </span>
          </div>
        }
        width={900}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        footer={
          <div className="flex justify-between">
            <button 
              onClick={handleTestCurrentConnection}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <TestTube className="w-4 h-4 mr-2 inline" />
              测试连接
            </button>
            <div className="flex gap-3">
              <button 
                onClick={() => setDrawerVisible(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button 
                onClick={handleSave}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
              >
                保存
              </button>
            </div>
          </div>
        }
        className="[&_.ant-drawer-header]:bg-gray-50 [&_.ant-drawer-header]:border-b [&_.ant-drawer-header]:border-gray-200"
      >
        <Form form={form} layout="vertical" className="space-y-6">
          {/* 配置说明 */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Database className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-blue-800">数据源配置说明</h3>
                <p className="text-xs text-blue-700 mt-1">配置数据源信息，支持监控目录、API接口和数据库直连三种类型。请确保填写正确的连接信息并进行连接测试。</p>
              </div>
            </div>
          </div>

          {/* 第一步：选择数据源类型 */}
          <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <span className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-semibold">1</span>
              选择数据源类型
            </h3>
            <Form.Item name="type" rules={[{ required: true, message: '请选择数据源类型' }]}>
              <Radio.Group 
                onChange={(e) => setSelectedType(e.target.value)}
                className="grid grid-cols-1 md:grid-cols-3 gap-4"
              >
                {typeOptions.map(option => {
                  const IconComponent = option.icon;
                  return (
                    <Radio.Button key={option.value} value={option.value} className="h-auto p-0 border-0">
                      <div className="flex items-center p-4 border border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all cursor-pointer">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <IconComponent className="w-5 h-5 text-blue-600" />
                        </div>
                        <div className="text-left">
                          <div className="font-medium text-gray-900">{option.label}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {option.value === 'directory' && '监控本地或网络文件夹'}
                            {option.value === 'api' && '通过HTTP/HTTPS接口获取数据'}
                            {option.value === 'database' && '直接连接数据库获取数据'}
                          </div>
                        </div>
                      </div>
                    </Radio.Button>
                  );
                })}
              </Radio.Group>
            </Form.Item>
          </div>

          {selectedType && (
            <>
              {/* 第二步：填写配置信息 */}
              <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
                <h3 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <span className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-semibold">2</span>
                  填写配置信息
                </h3>
                <div className="space-y-4">
                  <Form.Item name="name" label="数据源名称" rules={[{ required: true, message: '请输入数据源名称' }]}>
                    <Input 
                      placeholder="如：呼叫中心录音SFTP目录" 
                      className="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                    />
                  </Form.Item>
                  {renderConfigForm()}
                </div>
              </div>

              {/* 第三步：配置字段映射 */}
              <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
                <h3 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <span className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-semibold">3</span>
                  配置字段映射
                </h3>
                <div className="bg-amber-50 p-4 rounded-lg mb-6">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <Eye className="w-3 h-3 text-amber-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-amber-800">字段映射说明</h4>
                      <div className="text-xs text-amber-700 mt-1 space-y-1">
                        <p>• <strong>监控目录</strong>：从CSV/JSON等元数据文件中提取字段，如 "call_id" 或 "data.callInfo.id"</p>
                        <p>• <strong>API接口</strong>：从HTTP响应JSON中提取字段，支持嵌套路径，如 "response.data[0].recording_url"</p>
                        <p>• <strong>数据库直连</strong>：对应SQL查询结果的列名或别名，如 "recording_id" 或 "call_start_time"</p>
                        <p>• <strong>录音文件地址</strong>：必须配置可访问的录音文件URL或网络路径，支持相对路径和绝对路径</p>
                        <p>• <strong>路径语法</strong>：支持点号分隔（data.user.id）、数组索引（items[0].name）和混合嵌套</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-5 gap-4 text-sm font-medium text-gray-700 pb-2 border-b border-gray-200">
                    <div>系统字段</div>
                    <div>外部字段名/路径</div>
                    <div>示例值</div>
                    <div>是否必填</div>
                    <div>说明</div>
                  </div>
                  {fieldMappings.map((mapping, index) => (
                    <div key={mapping.systemField} className="grid grid-cols-5 gap-4 items-center py-2 hover:bg-gray-50 rounded-lg px-2 transition-colors">
                      <div className="font-medium text-gray-900">
                        {mapping.systemFieldLabel}
                        {mapping.required && <span className="text-red-500 ml-1">*</span>}
                      </div>
                      <Input
                        placeholder="外部字段名/路径"
                        value={mapping.externalField}
                        onChange={(e) => updateFieldMapping(index, 'externalField', e.target.value)}
                        className="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                      />
                      <Input
                        placeholder="示例值"
                        value={mapping.sampleValue}
                        disabled
                        className="bg-gray-50 rounded-lg border-gray-200"
                      />
                      <div className="text-sm">
                        {mapping.required ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            必填
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            可选
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {mapping.description}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </Form>
      </Drawer>
    </div>
  );
};

export default DataSourceManagementPage;