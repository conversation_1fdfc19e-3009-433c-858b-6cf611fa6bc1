import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Sprout, Home, Component, Presentation, FileText, Code, Microscope, Bot, FileCheck2, 
  ChevronRight, Blocks, GalleryVertical, ShieldAlert, ShieldCheck, Siren, BrainCircuit, Map, 
  HelpCircle, BookOpen, LayoutDashboard, Settings, ListTodo, Ear, RadioTower, Bell, Users, ShieldQuestion, 
  Library, BookCopy, BarChart3, Search, Trophy, Activity, TrendingUp, MessageSquare, Frown, Link2, Phone, 
  UserCheck, AlertTriangle, User, Target, MessageSquareX, Scale, BookText, Database } from 'lucide-react';

interface MenuItem {
  name: string;
  path: string;
  icon: React.ElementType;
}

interface MenuItemGroup {
  name: string;
  items: MenuItem[];
}

const homeMenuItem: MenuItem = { name: '主页', icon: Home, path: '/' };

const aliyunDocGroups: MenuItemGroup[] = [
    {
        name: '系统指南',
        items: [
          { name: '使用指南', icon: HelpCircle, path: '/user-guide' },
          { name: '使用流程', icon: Map, path: '/usage-flow' },
          { name: '深度解析', icon: BookOpen, path: '/detailed-guide' },
        ],
      },
      {
        name: '核心概念',
        items: [
          { name: '规则构成说明', icon: Blocks, path: '/rule-composition' },
          { name: '检测范围说明', icon: Presentation, path: '/demos/detection-scope' },
          { name: '逻辑规则说明', icon: Component, path: '/complex-example' },
        ],
      },
];

const operatorGroups: MenuItemGroup[] = [
    {
        name: '文字检查',
        items: [
          { name: '关键词检查', icon: FileText, path: '/operators/keyword-check' },
          { name: '文本相似度检查', icon: FileText, path: '/operators/text-similarity' },
          { name: '正则表达式检查', icon: Code, path: '/operators/regex-check' },
          { name: '上下文重复检查', icon: FileText, path: '/operators/context-repeat' },
          { name: '信息实体检查', icon: FileText, path: '/operators/entity-check' },
        ],
      },
      {
        name: '语音检查',
        items: [
          { name: '通话静音检查', icon: Microscope, path: '/operators/call-silence-check' },
          { name: '语速检查', icon: Microscope, path: '/operators/speech-speed-check' },
          { name: '抢话设置', icon: Microscope, path: '/operators/interrupt-check' },
          { name: '角色判断', icon: Microscope, path: '/operators/role-judgment' },
          { name: '非正常挂机', icon: Microscope, path: '/operators/abnormal-hangup' },
          { name: '非正常接听', icon: Microscope, path: '/operators/abnormal-answer' },
          { name: '录音时长检测', icon: Microscope, path: '/operators/duration-check' },
          { name: '能量检测', icon: Microscope, path: '/operators/energy-check' },
          { name: '对话语句数检测', icon: Microscope, path: '/operators/sentence-count-check' },
        ],
      },
      {
        name: '模型检查',
        items: [
          { name: '客服模型检测', icon: Bot, path: '/operators/agent-model' },
          { name: '客户模型检测', icon: Bot, path: '/operators/customer-model' },
          { name: '大模型检查', icon: BrainCircuit, path: '/operators/large-model-check' },
        ],
      },
      {
        name: '其它检查',
        items: [
          { name: '随录参数检查', icon: FileCheck2, path: '/operators/recording-params-check' },
        ],
      },
];

const exampleGroups: MenuItemGroup[] = [
    {
        name: '综合示例',
        items: [
            { name: '客户不满未道歉', icon: GalleryVertical, path: '/examples/customer-dissatisfaction' },
            { name: '客服未主动核身', icon: ShieldAlert, path: '/examples/proactive-verification' },
            { name: '金融合规综合质检', icon: ShieldCheck, path: '/examples/comprehensive-compliance' },
            { name: '主动服务缺失', icon: Siren, path: '/examples/proactive-service-failure' },
        ],
    }
]

const systemDesignGroups: MenuItemGroup[] = [
    {
        name: '首页',
        items: [
            { name: '质检主管', icon: LayoutDashboard, path: '/demos/supervisor-dashboard' },
            { name: '质检员', icon: LayoutDashboard, path: '/demos/agent-dashboard' },
            { name: '一线坐席', icon: LayoutDashboard, path: '/demos/frontline-agent-dashboard' },
        ]
    },
    {
        name: '质检工作台',
        items: [
            { name: '我的复核任务', icon: ListTodo, path: '/qa-workbench/my-review-tasks' },
            { name: '实时预警中心', icon: Ear, path: '/qa-workbench/real-time-alerts' },
            { name: '申诉处理', icon: ShieldQuestion, path: '/qa-workbench/appeal-processing' },
            { name: '通知中心', icon: Bell, path: '/qa-workbench/notifications' },
            { name: '坐席实时监控', icon: Users, path: '/qa-workbench/agent-monitoring' },
            { name: '实时监控配置', icon: RadioTower, path: '/qa-management/real-time-config' },
        ]
    },
    {
        name: '质检管理',
        items: [
            { name: '质检任务管理', icon: ListTodo, path: '/qa-management/task-management' },
            { name: '质检方案配置', icon: Settings, path: '/qa-management/scheme-config' },
            { name: '质检规则库', icon: Settings, path: '/qa-management/rule-library' },
            { name: '复核策略配置', icon: Settings, path: '/qa-management/automation-strategy' },
        ]
    },
    {
      name: '数据洞察',
      items: [
        { name: '全景数据看板', icon: BarChart3, path: '/data-insights/dashboard' },
      ]
    },
    {
      name: '员工成长',
      items: [
        { name: '个人成长档案', icon: Sprout, path: '/employee-growth/personal-profile' },
        { name: '团队成长中心', icon: Users, path: '/employee-growth/team-center' },
        { name: '案例库', icon: BookCopy, path: '/case-library' },
      ]
    },
];

const homepagesGroup: MenuItemGroup = {
    name: '主页',
    items: [
        { name: '质检主管', icon: LayoutDashboard, path: '/homepage/supervisor' },
        { name: '班组长', icon: Users, path: '/homepage/team-leader' },
        { name: '质检员', icon: LayoutDashboard, path: '/homepage/reviewer' },
        { name: '坐席员', icon: LayoutDashboard, path: '/homepage/agent' },
    ]
};

const newSystemDesignGroups: MenuItemGroup[] = [
    {
        name: '质检流程设计',
        items: [
            { name: '质检计划管理', icon: ListTodo, path: '/demos/plan-management/list' },
            { name: '质检任务列表', icon: ListTodo, path: '/demos/task-management/list' },
            { name: '复核策略配置', icon: Settings, path: '/demos/review-strategy-demo/list' },
            { name: '实时预警配置', icon: Siren, path: '/demos/real-time-alert-demo/list' },
            { name: '我的复核任务', icon: ShieldCheck, path: '/demos/my-review-tasks-demo/list' },
            { name: '我的成绩', icon: BarChart3, path: '/demos/my-performance-demo/list' },
            { name: '团队业绩看板', icon: Users, path: '/demos/my-performance-demo/team-dashboard' },
            { name: '全局数据看板', icon: LayoutDashboard, path: '/demos/my-performance-demo/global-dashboard' },
            { name: '申诉处理', icon: ShieldQuestion, path: '/demos/appeal-handling-demo/list' },
        ]
    },
    {
        name: '多模式详情页（演示）',
        items: [
            { name: '查看质检结果', icon: FileCheck2, path: '/demos/multi-mode-session-detail/SESS_123?viewMode=basic_view' },
            { name: '执行复核任务', icon: UserCheck, path: '/demos/multi-mode-session-detail/SESS_123?viewMode=review' },
            { name: '处理申诉', icon: ShieldQuestion, path: '/demos/multi-mode-session-detail/SESS_123?viewMode=appeal_processing' },
            { name: '查看个人成绩', icon: BarChart3, path: '/demos/multi-mode-session-detail/SESS_123?viewMode=performance_check' },
        ]
    }
];

const statisticsAnalysisGroup: MenuItemGroup = {
    name: '统计分析',
    items: [
        { name: '质检明细查询', icon: Search, path: '/data-insights/session-search' },
        { name: '质检综合运营报表', icon: LayoutDashboard, path: '/reports/qa-operation-dashboard' },
        { name: '综合运营报表', icon: LayoutDashboard, path: '/demos/dashboard-demo/comprehensive-operation' },
        { name: 'AI质检准确性分析', icon: BrainCircuit, path: '/demos/dashboard-demo/ai-accuracy-review-strategy' },
        { name: '实时预警分析', icon: AlertTriangle, path: '/demos/dashboard-demo/real-time-alert-analysis' },
        { name: '团队绩效报表', icon: Users, path: '/demos/dashboard-demo/team-performance-report' },
        { name: '个人成长报表', icon: User, path: '/demos/dashboard-demo/personal-growth-report' },
        { name: '申诉分析报表', icon: AlertTriangle, path: '/demos/dashboard-demo/appeal-analysis' },
        { name: '服务质量仪表盘', icon: LayoutDashboard, path: '/demos/dashboard-demo/service-quality' },
        { name: '高频失分项与客户反馈', icon: BarChart3, path: '/demos/dashboard-demo/high-frequency-deduction' },
        { name: '质检覆盖率分析', icon: BarChart3, path: '/demos/dashboard-demo/coverage' },
        { name: '绩效排行榜', icon: Trophy, path: '/demos/dashboard-demo/leaderboard' },
        { name: '能力分析', icon: Activity, path: '/demos/dashboard-demo/agent-capability' },
        { name: '绩效趋势', icon: TrendingUp, path: '/demos/dashboard-demo/performance-trend' },
        { name: '客户之声分析', icon: MessageSquare, path: '/demos/dashboard-demo/customer-voice' },
        { name: '负面情绪根源分析', icon: Frown, path: '/demos/dashboard-demo/negative-emotion' },
        { name: '质量业务关联分析', icon: Link2, path: '/demos/dashboard-demo/quality-business-correlation' },
        { name: '通话原因分析', icon: Phone, path: '/demos/dashboard-demo/call-reason-analysis' },
        { name: '质检员工作量分析', icon: UserCheck, path: '/demos/dashboard-demo/qa-workload-consistency' },
    ]
};

const finalDesignGroups: MenuItemGroup[] = [
    {
        name: '首页',
        items: [
            { name: '坐席员视角', icon: LayoutDashboard, path: '/final-design/agent-homepage' },
            { name: '班组长视角', icon: LayoutDashboard, path: '/final-design/team-leader-homepage' },
            { name: '复核员视角', icon: LayoutDashboard, path: '/final-design/reviewer-homepage' },
            { name: '质检主管视角', icon: LayoutDashboard, path: '/final-design/supervisor-homepage' },
        ]
    },
    {
        name: '质检工作台',
        items: [
            { name: '我的复核任务', icon: ListTodo, path: '/final-design/my-review-tasks' },
            { name: '申诉处理', icon: ShieldQuestion, path: '/final-design/appeal-processing' },
            { name: '质检成绩管理', icon: Trophy, path: '/final-design/personal-performance' },
            { name: '通知中心', icon: Bell, path: '/final-design/notification-center' },
        ]
    },
    {
        name: '质检管理',
        items: [
            { name: '质检规则管理', icon: Scale, path: '/final-design/rule-library' },
            { name: '质检方案管理', icon: Search, path: '/final-design/quality-scheme-config' },
            { name: '质检计划管理', icon: ListTodo, path: '/final-design/plan-list' },
            { name: '质检任务管理', icon: ListTodo, path: '/final-design/task-list' },
            { name: '复核策略配置', icon: Settings, path: '/final-design/review-strategy-list' },
            { name: '质检词库管理', icon: BookText, path: '/final-design/word-library' },
            { name: '质检明细查询', icon: Search, path: '/final-design/quality-detail-query' },
        ]
    },
    {
        name: '统计分析',
        items: [
            { name: '质检运营总览', icon: BarChart3, path: '/final-design/quality-operation-overview' },
            { name: '服务质量深度分析', icon: Target, path: '/final-design/service-quality-deep-analysis' },
            { name: '复核工作分析报告', icon: FileCheck2, path: '/final-design/review-analysis-report' },
            { name: '坐席申诉洞察报告', icon: MessageSquareX, path: '/final-design/appeal-insight-report' },
        ]
    },
    {
        name: '系统管理',
        items: [
            { name: '数据源管理', icon: Database, path: '/final-design/data-source-management' },
        ]
    }
];

export const Sidebar: React.FC = () => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    const newExpanded = new Set<string>();
    const pathname = location.pathname;

    const checkGroups = (groups: MenuItemGroup[], topLevel: string, midLevel?: string) => {
        for (const group of groups) {
            if (group.items.some(item => pathname.startsWith(item.path))) {
                newExpanded.add(topLevel);
                if (midLevel) newExpanded.add(midLevel);
                newExpanded.add(group.name);
                return;
            }
        }
    };

    checkGroups(aliyunDocGroups, '阿里智能质检系统学习');
    checkGroups(operatorGroups, '阿里智能质检系统学习', '质检算子');
    checkGroups(exampleGroups, '阿里智能质检系统学习');
    
    if (pathname === '/examples/side-menu-design') {
        newExpanded.add('智能质检系统设计');
    }
    checkGroups(systemDesignGroups, '智能质检系统设计');

    const allNewSystemDesignGroups = [homepagesGroup, ...newSystemDesignGroups, statisticsAnalysisGroup];
    checkGroups(allNewSystemDesignGroups, '智能质检系统设计（新）');

    checkGroups(finalDesignGroups, '智能质检系统设计（定稿）');

    setExpandedItems(newExpanded);
  }, [location.pathname]);

  const toggleExpansion = (name: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(name)) {
      newExpanded.delete(name);
    } else {
      newExpanded.add(name);
    }
    setExpandedItems(newExpanded);
  };
  
  const isActive = (path: string) => location.pathname === path;
  
  const renderMenuItems = (items: MenuItem[]) => {
    return items.map(item => (
        <Link
            key={item.path}
            to={item.path}
            className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-md transition-colors ${
              isActive(item.path)
                ? 'bg-blue-100 text-blue-700 font-medium'
                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <item.icon className="w-4 h-4" />
            <span>{item.name}</span>
          </Link>
    ));
  }
  
  const renderGroup = (group: MenuItemGroup) => {
      return (
        <div key={group.name} className="pt-1">
            <button onClick={() => toggleExpansion(group.name)} className="w-full flex items-center justify-between py-2 px-3 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50">
              <span>{group.name}</span>
              <ChevronRight className={`w-4 h-4 transform transition-transform ${expandedItems.has(group.name) ? 'rotate-90' : ''}`} />
            </button>
            {expandedItems.has(group.name) && (
              <div className="pl-4 mt-1 space-y-1 border-l border-gray-200 ml-3">
                {renderMenuItems(group.items)}
              </div>
            )}
        </div>
      )
  }

  return (
    <div className="w-72 bg-white shadow-sm border-r border-gray-200 h-full overflow-y-auto scrollbar-hide">
      <div className="p-4">
        <nav>
          <Link
            key={homeMenuItem.path}
            to={homeMenuItem.path}
            className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-md transition-colors ${
              isActive(homeMenuItem.path)
                ? 'bg-blue-100 text-blue-700 font-semibold'
                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <homeMenuItem.icon className="w-5 h-5" />
            <span>{homeMenuItem.name}</span>
          </Link>
          
          <div className="mt-4 space-y-2">
            {/* 阿里云智能对话分析 */}
            <div key='阿里智能质检系统学习'>
                <button
                  onClick={() => toggleExpansion('阿里智能质检系统学习')}
                  className="w-full flex items-center justify-between p-3 text-sm font-semibold text-gray-800 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Library className="w-5 h-5 text-gray-600" />
                    <span>阿里智能质检系统学习</span>
                  </div>
                  <ChevronRight className={`w-4 h-4 transform transition-transform ${
                    expandedItems.has('阿里智能质检系统学习') ? 'rotate-90' : ''
                  }`} />
                </button>
                {expandedItems.has('阿里智能质检系统学习') && (
                    <div className="pl-4 mt-1 space-y-1">
                        {aliyunDocGroups.map(g => renderGroup(g))}
                        <div key='质检算子' className="pt-1">
                            <button onClick={() => toggleExpansion('质检算子')} className="w-full flex items-center justify-between py-2 px-3 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50">
                                <span>质检算子</span>
                                <ChevronRight className={`w-4 h-4 transform transition-transform ${expandedItems.has('质检算子') ? 'rotate-90' : ''}`} />
                            </button>
                            {expandedItems.has('质检算子') && (
                                <div className="pl-4 mt-1 space-y-1 border-l border-gray-200 ml-3">
                                    {operatorGroups.map(g => renderGroup(g))}
                                </div>
                            )}
                        </div>
                        {exampleGroups.map(g => renderGroup(g))}
                    </div>
                )}
            </div>

            {/* 智能质检系统设计 */}
             <div key='智能质检系统设计'>
                <button
                  onClick={() => toggleExpansion('智能质检系统设计')}
                  className="w-full flex items-center justify-between p-3 text-sm font-semibold text-gray-800 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Settings className="w-5 h-5 text-gray-600" />
                    <span>智能质检系统设计</span>
                  </div>
                  <ChevronRight className={`w-4 h-4 transform transition-transform ${
                    expandedItems.has('智能质检系统设计') ? 'rotate-90' : ''
                  }`} />
                </button>
                {expandedItems.has('智能质检系统设计') && (
                    <div className="pl-4 mt-1 space-y-1">
                        <Link to="/examples/side-menu-design" className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-md transition-colors ${ isActive('/examples/side-menu-design') ? 'bg-blue-100 text-blue-700 font-medium' : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50' }`}>
                            <GalleryVertical className="w-4 h-4" />
                            <span>导航菜单设计</span>
                        </Link>
                        {systemDesignGroups.map(g => renderGroup(g))}
                    </div>
                )}
            </div>

            {/* 智能质检系统设计（新） */}
            <div key='智能质检系统设计（新）'>
                <button
                  onClick={() => toggleExpansion('智能质检系统设计（新）')}
                  className="w-full flex items-center justify-between p-3 text-sm font-semibold text-gray-800 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <BrainCircuit className="w-5 h-5 text-gray-600" />
                    <span>智能质检系统设计（新）</span>
                  </div>
                  <ChevronRight className={`w-4 h-4 transform transition-transform ${
                    expandedItems.has('智能质检系统设计（新）') ? 'rotate-90' : ''
                  }`} />
                </button>
                {expandedItems.has('智能质检系统设计（新）') && (
                    <div className="pl-4 mt-1 space-y-1">
                        {renderGroup(homepagesGroup)}
                        {newSystemDesignGroups.map(g => renderGroup(g))}
                        {renderGroup(statisticsAnalysisGroup)}
                    </div>
                )}
            </div>

            {/* 智能质检系统设计（定稿） */}
            <div key='智能质检系统设计（定稿）'>
                <button
                  onClick={() => toggleExpansion('智能质检系统设计（定稿）')}
                  className="w-full flex items-center justify-between p-3 text-sm font-semibold text-gray-800 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <ShieldCheck className="w-5 h-5 text-gray-600" />
                    <span>智能质检系统设计（定稿）</span>
                  </div>
                  <ChevronRight className={`w-4 h-4 transform transition-transform ${
                    expandedItems.has('智能质检系统设计（定稿）') ? 'rotate-90' : ''
                  }`} />
                </button>
                {expandedItems.has('智能质检系统设计（定稿）') && (
                    <div className="pl-4 mt-1 space-y-1">
                        {finalDesignGroups.map(g => renderGroup(g))}
                    </div>
                )}
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;