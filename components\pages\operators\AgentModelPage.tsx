import React, { useState } from 'react';
import { PageHeader } from '../../PageHeader';
import { AgentModelConcepts } from './agentModel/AgentModelConcepts';
import { AgentModelConfig } from './agentModel/AgentModelConfig';
import { AgentModelTester } from './agentModel/AgentModelTester';
import { AgentModelCases } from './agentModel/AgentModelCases';
import { AgentModelTips } from './agentModel/AgentModelTips';

export const detectionTypes = [
  '反问反感', '引导投诉', '推诿', '辱骂'
] as const;

export type DetectionType = typeof detectionTypes[number];

/**
 * @typedef {object} AgentModelConfigType
 * @property {'agent'} detectionRole
 * @property {'full'} detectionScope
 * @property {DetectionType | ''} detectionType
 */
export interface AgentModelConfigType {
  detectionRole: 'agent';
  detectionScope: 'full';
  detectionType: DetectionType | '';
}

/**
 * @typedef {object} TestResultType
 * @property {boolean} matched
 * @property {DetectionType | null} matchedType
 * @property {string} details
 */
export interface TestResultType {
  matched: boolean;
  matchedType: DetectionType | null;
  details: string;
}

/**
 * 客服模型检测页面
 * @returns {React.ReactElement}
 */
export const AgentModelPage: React.FC = () => {
  const [config, setConfig] = useState<AgentModelConfigType>({
    detectionRole: 'agent',
    detectionScope: 'full',
    detectionType: '',
  });

  const [testText, setTestText] = useState(
    '客户：这个怎么办？\n客服：我不是跟你说了吗，这个不归我管。'
  );

  const [testResult, setTestResult] = useState<TestResultType | null>(null);

  const pageInfo = {
    title: '🤖 客服模型检测',
    description: '检测客服常见的违规用语或负面情绪，由系统内置的算法模型进行分析。',
    tag: '模型检查',
    badge: '高级功能',
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader {...pageInfo} />

      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          <div className="w-[60%] space-y-6">
            <AgentModelConcepts />
            <AgentModelConfig config={config} setConfig={setConfig} />
            <AgentModelTester
              config={config}
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              setTestResult={setTestResult}
            />
          </div>

          <div className="w-[40%] space-y-6">
            <AgentModelCases setConfig={setConfig} setTestText={setTestText} />
            <AgentModelTips />
          </div>
        </div>
      </div>
      
      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2 text-sm">
                <p>• <strong>理解违规分类</strong>：掌握"推诿"、"反问反感"等客服负面行为的判断标准和典型用例。</p>
                <p>• <strong>进行边界测试</strong>：尝试构造模糊、边界化的违规用语，测试模型的识别准确率和鲁棒性。</p>
                <p>• <strong>结合质检方案</strong>：将此算子作为服务质量监控的核心规则，对所有通话进行自动化的第一轮筛查。</p>
                <p>• <strong>反向优化培训</strong>：定期分析高频触发的违规类型，以此为依据，反向优化客服的培训内容和话术SOP。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 