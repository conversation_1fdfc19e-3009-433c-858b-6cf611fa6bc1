import React from 'react';
import { TextSimilarityConfig } from './TextSimilarityConfig';

interface CaseItem {
  name: string;
  description: string;
  config: TextSimilarityConfig;
  testText: string;
  expectedResult: string;
}

interface TextSimilarityCasesProps {
  onLoadCase: (config: TextSimilarityConfig, testText: string) => void;
}

/**
 * 文本相似度案例库组件
 */
const TextSimilarityCases: React.FC<TextSimilarityCasesProps> = ({ onLoadCase }) => {
  // 预设案例
  const cases: CaseItem[] = [
    {
      name: '标准问候语检测',
      description: '检测客服是否使用规范的问候语',
      config: {
        detectionRole: 'agent',
        detectionScope: 'range',
        rangeStart: 1,
        rangeEnd: 2,
        similarityThreshold: 80,
        templates: ['您好，请问有什么可以帮助您的', '你好，有什么问题吗'],
        singleSentence: false,
      },
      testText: '客服：您好，请问有什么可以帮助您的吗？\n客户：我想咨询产品信息。\n客服：好的，我来为您介绍。',
      expectedResult: '应该命中（前2句中与标准问候语相似度达到要求）'
    },
    {
      name: '投诉处理话术',
      description: '检测客服处理投诉时的标准话术',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        similarityThreshold: 75,
        templates: ['非常抱歉给您带来不便', '我们会认真处理您的投诉', '请您提供详细情况'],
        singleSentence: false,
        rangeStart: 1,
        rangeEnd: 1,
      },
      testText: '客户：我要投诉你们的服务太差了！\n客服：非常抱歉给您带来了不便，我们会认真对待。\n客服：请您详细说明遇到的问题。',
      expectedResult: '应该命中（包含标准投诉处理话术）'
    },
    {
      name: '业务咨询引导',
      description: '检测客服是否正确引导客户咨询',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        similarityThreshold: 85,
        templates: ['请问您想了解哪方面的信息', '我来为您详细介绍', '有什么具体问题吗'],
        singleSentence: false,
        rangeStart: 1,
        rangeEnd: 1,
      },
      testText: '客户：我想了解你们的产品。\n客服：好的，请问您想了解哪方面的信息呢？\n客服：我来为您详细介绍一下我们的产品特点。',
      expectedResult: '应该命中（包含标准咨询引导话术）'
    },
    {
      name: '单句话内生效演示',
      description: '演示单句话内生效功能的使用',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        similarityThreshold: 70,
        templates: ['感谢您的来电', '为您服务'],
        singleSentence: true,
        rangeStart: 1,
        rangeEnd: 1,
      },
      testText: '客服：您好，感谢您的来电，我是客服小李，很高兴为您服务。\n客户：我有问题要咨询。',
      expectedResult: '应该命中（启用单句话内生效，按标点分割后匹配）'
    },
    {
      name: '高相似度要求',
      description: '设置高相似度阈值的严格匹配',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        similarityThreshold: 95,
        templates: ['请您稍等，我马上为您查询'],
        singleSentence: false,
        rangeStart: 1,
        rangeEnd: 1,
      },
      testText: '客户：帮我查一下订单状态。\n客服：好的，请您稍等，我马上为您查询订单信息。\n客服：查询结果出来了。',
      expectedResult: '应该命中（95%高相似度要求下的精确匹配）'
    },
    {
      name: '多模板匹配',
      description: '配置多个话术模板进行匹配',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        similarityThreshold: 80,
        templates: [
          '感谢您选择我们的服务',
          '我会尽力帮助您解决问题',
          '如果还有其他问题请随时联系我们',
          '祝您生活愉快'
        ],
        singleSentence: false,
        rangeStart: 1,
        rangeEnd: 1,
      },
      testText: '客服：感谢您选择我们的服务，有问题请联系我们。\n客户：好的。\n客服：祝您生活愉快，再见！',
      expectedResult: '应该命中（多个模板中任意匹配即可）'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((caseItem, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-800">{caseItem.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{caseItem.description}</p>
                <p className="text-xs text-blue-500 mt-1">预期结果：{caseItem.expectedResult}</p>
                <div className="mt-2 flex flex-wrap gap-1">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                    {caseItem.config.detectionRole === 'agent' ? '客服' : caseItem.config.detectionRole === 'customer' ? '客户' : '所有角色'}
                  </span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                    {caseItem.config.similarityThreshold}%相似度
                  </span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-100 text-green-800">
                    {caseItem.config.templates.length}个模板
                  </span>
                  {caseItem.config.singleSentence && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-orange-100 text-orange-800">
                      单句生效
                    </span>
                  )}
                </div>
              </div>
              <button 
                onClick={() => onLoadCase(caseItem.config, caseItem.testText)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TextSimilarityCases; 