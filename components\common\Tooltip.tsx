import React, { useState } from 'react';

interface TooltipProps {
  text: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  widthClass?: string;
}

export const Tooltip: React.FC<TooltipProps> = ({ text, children, className, placement = 'top', widthClass = 'w-48' }) => {
  const [isVisible, setIsVisible] = useState(false);

  const placementClasses = {
    top: 'bottom-full mb-2 -translate-x-1/2 left-1/2',
    bottom: 'top-full mt-2 -translate-x-1/2 left-1/2',
    left: 'right-full mr-2 -translate-y-1/2 top-1/2',
    right: 'left-full ml-2 -translate-y-1/2 top-1/2',
  };

  const arrowClasses = {
    top: 'top-full left-1/2 -translate-x-1/2 border-t-gray-700',
    bottom: 'bottom-full left-1/2 -translate-x-1/2 border-b-gray-700',
    left: 'left-full top-1/2 -translate-y-1/2 border-l-gray-700',
    right: 'right-full top-1/2 -translate-y-1/2 border-r-gray-700',
  }

  return (
    <div className={`relative inline-flex ${className}`}>
      <div
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        className="cursor-pointer"
      >
        {children}
      </div>
      {isVisible && (
        <div className={`absolute z-50 p-2 text-xs text-white bg-gray-700 rounded-md shadow-lg ${widthClass} ${placementClasses[placement]}`}>
          {text}
          <div className={`absolute w-0 h-0 border-4 border-transparent ${arrowClasses[placement]}`}></div>
        </div>
      )}
    </div>
  );
};
