import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Component, Presentation, FileText, Code, Microscope, Bot, FileCheck2, 
  ChevronRight, Blocks, GalleryVertical, ShieldAlert, ShieldCheck, Siren, BrainCircuit, Map, 
  HelpCircle, BookOpen, LayoutDashboard, Settings, ListTodo, Bell, ShieldQuestion, 
  Library, BarChart3, Search, Trophy, Target, MessageSquareX, Scale, BookText, Database, Cpu, Mic, Send, ClipboardList, History } from 'lucide-react';

interface MenuItem {
  name: string;
  path: string;
  icon: React.ElementType;
}

interface MenuItemGroup {
  name: string;
  items: MenuItem[];
}

interface SidebarProps {
  isCollapsed: boolean;
}

const aliyunDocGroups: MenuItemGroup[] = [
    {
        name: '系统指南',
        items: [
          { name: '使用指南', icon: HelpCircle, path: '/user-guide' },
          { name: '使用流程', icon: Map, path: '/usage-flow' },
          { name: '深度解析', icon: BookOpen, path: '/detailed-guide' },
        ],
      },
      {
        name: '核心概念',
        items: [
          { name: '规则构成说明', icon: Blocks, path: '/rule-composition' },
          { name: '检测范围说明', icon: Presentation, path: '/demos/detection-scope' },
          { name: '逻辑规则说明', icon: Component, path: '/complex-example' },
        ],
      },
];

const operatorGroups: MenuItemGroup[] = [
    {
        name: '文字检查',
        items: [
          { name: '关键词检查', icon: FileText, path: '/operators/keyword-check' },
          { name: '文本相似度检查', icon: FileText, path: '/operators/text-similarity' },
          { name: '正则表达式检查', icon: Code, path: '/operators/regex-check' },
          { name: '上下文重复检查', icon: FileText, path: '/operators/context-repeat' },
          { name: '信息实体检查', icon: FileText, path: '/operators/entity-check' },
        ],
      },
      {
        name: '语音检查',
        items: [
          { name: '通话静音检查', icon: Microscope, path: '/operators/call-silence-check' },
          { name: '语速检查', icon: Microscope, path: '/operators/speech-speed-check' },
          { name: '抢话设置', icon: Microscope, path: '/operators/interrupt-check' },
          { name: '角色判断', icon: Microscope, path: '/operators/role-judgment' },
          { name: '非正常挂机', icon: Microscope, path: '/operators/abnormal-hangup' },
          { name: '非正常接听', icon: Microscope, path: '/operators/abnormal-answer' },
          { name: '录音时长检测', icon: Microscope, path: '/operators/duration-check' },
          { name: '能量检测', icon: Microscope, path: '/operators/energy-check' },
          { name: '对话语句数检测', icon: Microscope, path: '/operators/sentence-count-check' },
        ],
      },
      {
        name: '模型检查',
        items: [
          { name: '客服模型检测', icon: Bot, path: '/operators/agent-model' },
          { name: '客户模型检测', icon: Bot, path: '/operators/customer-model' },
          { name: '大模型检查', icon: BrainCircuit, path: '/operators/large-model-check' },
        ],
      },
      {
        name: '其它检查',
        items: [
          { name: '随录参数检查', icon: FileCheck2, path: '/operators/recording-params-check' },
        ],
      },
];

const exampleGroups: MenuItemGroup[] = [
    {
        name: '综合示例',
        items: [
            { name: '客户不满未道歉', icon: GalleryVertical, path: '/examples/customer-dissatisfaction' },
            { name: '客服未主动核身', icon: ShieldAlert, path: '/examples/proactive-verification' },
            { name: '金融合规综合质检', icon: ShieldCheck, path: '/examples/comprehensive-compliance' },
            { name: '主动服务缺失', icon: Siren, path: '/examples/proactive-service-failure' },
        ],
    }
];

const finalDesignGroups: MenuItemGroup[] = [
    {
        name: '首页',
        items: [
            { name: '坐席员视角', icon: LayoutDashboard, path: '/final-design/agent-homepage' },
            { name: '班组长视角', icon: LayoutDashboard, path: '/final-design/team-leader-homepage' },
            { name: '复核员视角', icon: LayoutDashboard, path: '/final-design/reviewer-homepage' },
            { name: '质检主管视角', icon: LayoutDashboard, path: '/final-design/supervisor-homepage' },
        ]
    },
    {
        name: '质检工作台',
        items: [
            { name: '我的复核任务', icon: ListTodo, path: '/final-design/my-review-tasks' },
            { name: '申诉处理', icon: ShieldQuestion, path: '/final-design/appeal-processing' },
            { name: '质检成绩管理', icon: Trophy, path: '/final-design/personal-performance' },
            { name: '通知中心', icon: Bell, path: '/final-design/notification-center' },
            { name: '实时预警中心', icon: Siren, path: '/final-design/real-time-alert-center' },
            { name: '预警跟进中心', icon: ClipboardList, path: '/final-design/alert-follow-up-center' },
        ]
    },
    {
        name: '质检管理',
        items: [
            { name: '质检规则管理', icon: Scale, path: '/final-design/rule-library' },
            { name: '质检方案管理', icon: Search, path: '/final-design/quality-scheme-config' },
            { name: '质检计划管理', icon: ListTodo, path: '/final-design/plan-list' },
            { name: '质检任务管理', icon: ListTodo, path: '/final-design/task-list' },
            { name: '复核策略配置', icon: Settings, path: '/final-design/review-strategy-list' },
            { name: '质检词库管理', icon: BookText, path: '/final-design/word-library' },
            { name: '质检明细查询', icon: Search, path: '/final-design/quality-detail-query' },
            { name: '历史预警查询', icon: History, path: '/final-design/historical-alert-query' },
        ]
    },
    {
        name: '统计分析',
        items: [
            { name: '质检运营总览', icon: BarChart3, path: '/final-design/quality-operation-overview' },
            { name: '服务质量深度分析', icon: Target, path: '/final-design/service-quality-deep-analysis' },
            { name: '复核工作分析报告', icon: FileCheck2, path: '/final-design/review-analysis-report' },
            { name: '坐席申诉洞察报告', icon: MessageSquareX, path: '/final-design/appeal-insight-report' },
        ]
    },
    {
        name: '系统管理',
        items: [
            { name: '数据源管理', icon: Database, path: '/final-design/data-source-management' },
            { name: '大模型管理', icon: Cpu, path: '/final-design/model-management' },
            { name: '语音识别引擎管理', icon: Mic, path: '/final-design/speech-engine-management' },
            { name: '通知渠道管理', icon: Send, path: '/final-design/notification-channel-management' },
        ]
    }
];

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed }) => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(() => {
    const initialExpanded = new Set<string>();
    // Always expand finalDesignGroups initially when not collapsed
    if (!isCollapsed) {
    finalDesignGroups.forEach(group => initialExpanded.add(group.name));
    }
    return initialExpanded;
  });

  const [hoveredGroup, setHoveredGroup] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const hoverTimerRef = useRef<NodeJS.Timeout | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const pathname = location.pathname;
    const newExpanded = new Set(expandedItems);

    if (!isCollapsed) {
    // Ensure the parent groups of the current path are expanded
    finalDesignGroups.forEach(group => {
      if (group.items.some(item => pathname.startsWith(item.path))) {
        newExpanded.add(group.name);
      }
    });

    // Re-add expansion logic for Aliyun Doc groups
    const checkAliyunDocGroups = (groups: MenuItemGroup[]) => {
        for (const group of groups) {
            if (group.items.some(item => pathname.startsWith(item.path))) {
                newExpanded.add('阿里智能质检系统学习');
                newExpanded.add(group.name);
                // If it's an operator group, also expand '质检算子'
                if (operatorGroups.some(opGroup => opGroup.name === group.name)) {
                    newExpanded.add('质检算子');
                }
                return;
            }
        }
    };
    checkAliyunDocGroups(aliyunDocGroups);
    checkAliyunDocGroups(operatorGroups);
    checkAliyunDocGroups(exampleGroups);
    }

    setExpandedItems(newExpanded);
  }, [location.pathname, isCollapsed]);

  const toggleExpansion = (name: string) => {
    if (isCollapsed) return; // 收缩状态下不允许切换展开状态

    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(name)) {
      newExpanded.delete(name);
    } else {
      newExpanded.add(name);
    }
    setExpandedItems(newExpanded);
  };

  // 计算悬浮菜单的最佳位置
  const calculateMenuPosition = (triggerRect: DOMRect) => {
    const menuWidth = 320; // 预估菜单宽度
    const menuHeight = 400; // 预估菜单高度
    const padding = 12; // 边距

    let x = triggerRect.right + padding;
    let y = triggerRect.top;

    // 检查右侧空间，如果不够则显示在左侧
    if (x + menuWidth > window.innerWidth) {
      x = triggerRect.left - menuWidth - padding;
    }

    // 检查底部空间，如果不够则向上调整
    if (y + menuHeight > window.innerHeight) {
      y = Math.max(padding, window.innerHeight - menuHeight - padding);
    }

    // 确保不会超出顶部
    y = Math.max(padding, y);

    return { x, y };
  };

  const handleMouseEnter = (groupKey: string, event: React.MouseEvent) => {
    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
    }

    const rect = event.currentTarget.getBoundingClientRect();
    const position = calculateMenuPosition(rect);
    setMenuPosition(position);

    hoverTimerRef.current = setTimeout(() => {
      setHoveredGroup(groupKey);
    }, 200); // 稍微增加延迟，减少误触发
  };

  const handleMouseLeave = (event: React.MouseEvent) => {
    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
    }

    // 检查鼠标是否移向悬浮菜单
    const rect = event.currentTarget.getBoundingClientRect();
    const mouseX = event.clientX;

    // 如果鼠标在触发器和菜单之间的区域，延迟关闭
    const isMovingToMenu = mouseX > rect.right && mouseX < rect.right + 60;

    hoverTimerRef.current = setTimeout(() => {
      setHoveredGroup(null);
    }, isMovingToMenu ? 400 : 200);
  };

  const handleMenuMouseEnter = () => {
    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
    }
  };

  const handleMenuMouseLeave = (event: React.MouseEvent) => {
    // 检查鼠标是否移回到触发器
    const menuRect = event.currentTarget.getBoundingClientRect();
    const mouseX = event.clientX;

    // 如果鼠标移向左侧（可能回到触发器），稍微延迟关闭
    const isMovingBack = mouseX < menuRect.left;

    setTimeout(() => {
      setHoveredGroup(null);
    }, isMovingBack ? 100 : 50);
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && hoveredGroup) {
        setHoveredGroup(null);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (hoveredGroup && menuRef.current && !menuRef.current.contains(event.target as Node)) {
        // 检查点击是否在触发器上
        const triggers = document.querySelectorAll('[data-hover-trigger]');
        let clickedOnTrigger = false;
        triggers.forEach(trigger => {
          if (trigger.contains(event.target as Node)) {
            clickedOnTrigger = true;
          }
        });

        if (!clickedOnTrigger) {
          setHoveredGroup(null);
        }
      }
    };

    if (hoveredGroup) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
      }
    };
  }, [hoveredGroup]);
  
  const isActive = (path: string) => location.pathname === path;
  
  // 普通菜单项渲染
  const renderMenuItems = (items: MenuItem[]) => {
    return items.map(item => (
        <Link
            key={item.path}
            to={item.path}
            className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-md transition-colors ${
              isActive(item.path)
                ? 'bg-blue-100 text-blue-700 font-medium'
                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <item.icon className="w-4 h-4" />
            <span>{item.name}</span>
          </Link>
    ));
  };
  
  // 组渲染
  const renderGroup = (group: MenuItemGroup) => {
      return (
        <div key={group.name} className="pt-1">
            <button onClick={() => toggleExpansion(group.name)} className="w-full flex items-center justify-between py-2 px-3 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50">
              <span>{group.name}</span>
              <ChevronRight className={`w-4 h-4 transform transition-transform ${expandedItems.has(group.name) ? 'rotate-90' : ''}`} />
            </button>
            {expandedItems.has(group.name) && (
              <div className="pl-4 mt-1 space-y-1 border-l border-gray-200 ml-3">
                {renderMenuItems(group.items)}
              </div>
            )}
        </div>
      );
  };

  // 收缩模式下的主组悬浮菜单
  const CollapsedMainGroup: React.FC<{
    groupKey: string;
    title: string;
    icon: React.ElementType;
  }> = ({ groupKey, title, icon: Icon }) => (
    <div className="relative">
      <div
        data-hover-trigger
        className={`flex items-center justify-center w-12 h-12 rounded-lg cursor-pointer transition-all duration-200 ${
          hoveredGroup === groupKey
            ? 'bg-blue-100 text-blue-600 shadow-md scale-105'
            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800 hover:scale-105'
        }`}
        onMouseEnter={(e) => handleMouseEnter(groupKey, e)}
        onMouseLeave={handleMouseLeave}
        title={title}
        role="button"
        tabIndex={0}
        aria-label={`打开 ${title} 菜单`}
      >
        <Icon className="w-5 h-5" />
      </div>
    </div>
  );

  // 悬浮菜单Portal组件
  const HoverMenu: React.FC<{
    groupKey: string;
    title: string;
    groups: MenuItemGroup[]
  }> = ({ groupKey, title, groups }) => {
    if (hoveredGroup !== groupKey) return null;

    return (
      <div
        ref={menuRef}
        className="fixed bg-white/95 backdrop-blur-sm shadow-2xl border border-gray-200/60 rounded-xl py-4 min-w-80 max-w-96 max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent animate-in fade-in-0 zoom-in-95 duration-200"
        style={{
          left: menuPosition.x,
          top: menuPosition.y,
          zIndex: 9999,
        }}
        onMouseEnter={handleMenuMouseEnter}
        onMouseLeave={handleMenuMouseLeave}
        role="menu"
        aria-label={`${title} 菜单`}
      >
        {/* 标题区域 */}
        <div className="px-5 py-3 border-b border-gray-100/80 bg-gradient-to-r from-blue-50/50 to-indigo-50/50">
          <h3 className="text-sm font-semibold text-gray-800 flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            {title}
          </h3>
          <p className="text-xs text-gray-500 mt-1">选择功能模块</p>
        </div>

        {/* 菜单内容 */}
        <div className="py-2">
          {groups.map(group => (
            <div key={group.name} className="px-3 mb-4 last:mb-2">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100/50 mb-2">
                {group.name}
              </div>
              <div className="space-y-0.5">
                {group.items.map(item => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`flex items-center space-x-3 px-3 py-2.5 text-sm transition-all duration-200 rounded-lg mx-1 group ${
                      isActive(item.path)
                        ? 'bg-blue-100 text-blue-700 font-medium shadow-sm'
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50/80 hover:shadow-sm'
                    }`}
                    onClick={() => {
                      setHoveredGroup(null);
                      // 添加点击反馈
                      if (hoverTimerRef.current) {
                        clearTimeout(hoverTimerRef.current);
                      }
                    }}
                  >
                    <item.icon className={`w-4 h-4 transition-colors ${
                      isActive(item.path) ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'
                    }`} />
                    <span className="flex-1">{item.name}</span>
                    {isActive(item.path) && (
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          ))}

          {/* 质检算子特殊处理 */}
          {groupKey === '阿里智能质检系统学习' && (
            <div className="px-3 mt-6 border-t border-gray-100/50 pt-4">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100/50 mb-3">
                质检算子
              </div>
              {operatorGroups.map(group => (
                <div key={group.name} className="mb-4 last:mb-2">
                  <div className="px-3 py-1.5 text-xs font-medium text-gray-400 bg-gray-50/50 rounded-md mb-2">
                    {group.name}
                  </div>
                  <div className="space-y-0.5">
                    {group.items.map(item => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={`flex items-center space-x-3 px-3 py-2 text-sm transition-all duration-200 rounded-lg mx-1 group ${
                          isActive(item.path)
                            ? 'bg-blue-100 text-blue-700 font-medium shadow-sm'
                            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50/80 hover:shadow-sm'
                        }`}
                        onClick={() => {
                          setHoveredGroup(null);
                          if (hoverTimerRef.current) {
                            clearTimeout(hoverTimerRef.current);
                          }
                        }}
                      >
                        <item.icon className={`w-4 h-4 transition-colors ${
                          isActive(item.path) ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'
                        }`} />
                        <span className="flex-1">{item.name}</span>
                        {isActive(item.path) && (
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                        )}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  if (isCollapsed) {
    // 收缩模式渲染
    return (
      <>
        <div className="w-16 bg-white shadow-sm border-r border-gray-200 h-full overflow-visible transition-all duration-300 scrollbar-hide relative">
          <div className="p-2">
            {/* 主页链接 */}
            <Link
              to="/"
              className={`flex items-center justify-center w-12 h-12 text-sm rounded-md transition-colors ${
                isActive('/')
                  ? 'bg-blue-100 text-blue-700 font-semibold'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
              }`}
              title="主页"
            >
              <Home className="w-5 h-5" />
            </Link>

            <nav>
              <div className="mt-4 space-y-2">
                {/* 阿里云智能对话分析 */}
                <CollapsedMainGroup
                  groupKey="阿里智能质检系统学习"
                  title="阿里智能质检系统学习"
                  icon={Library}
                />

                {/* 智能客服质检系统设计 */}
                <CollapsedMainGroup
                  groupKey="智能客服质检系统设计"
                  title="智能客服质检系统设计"
                  icon={ShieldCheck}
                />
              </div>
            </nav>
          </div>
        </div>

        {/* 悬浮菜单 */}
        {hoveredGroup === '阿里智能质检系统学习' && (
          <HoverMenu
            groupKey="阿里智能质检系统学习"
            title="阿里智能质检系统学习"
            groups={[...aliyunDocGroups, ...exampleGroups]}
          />
        )}

        {hoveredGroup === '智能客服质检系统设计' && (
          <HoverMenu
            groupKey="智能客服质检系统设计"
            title="智能客服质检系统设计"
            groups={finalDesignGroups}
          />
        )}
      </>
    );
  }

  // 正常模式渲染
  return (
    <div className="w-72 bg-white shadow-sm border-r border-gray-200 h-full overflow-y-auto transition-all duration-300 scrollbar-hide">
      <div className="p-4">
        <Link
            key={'/'}
            to={'/'}
            className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-md transition-colors ${
              isActive('/')
                ? 'bg-blue-100 text-blue-700 font-semibold'
                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <Home className="w-5 h-5" />
            <span>主页</span>
          </Link>

        <nav>
          <div className="mt-4 space-y-2">
            {/* 阿里云智能对话分析 */}
            <div key='阿里智能质检系统学习'>
                <button
                  onClick={() => toggleExpansion('阿里智能质检系统学习')}
                  className="w-full flex items-center justify-between p-3 text-sm font-semibold text-gray-800 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Library className="w-5 h-5 text-gray-600" />
                    <span>阿里智能质检系统学习</span>
                  </div>
                  <ChevronRight className={`w-4 h-4 transform transition-transform ${
                    expandedItems.has('阿里智能质检系统学习') ? 'rotate-90' : ''
                  }`} />
                </button>
                {expandedItems.has('阿里智能质检系统学习') && (
                    <div className="pl-4 mt-1 space-y-1">
                        {aliyunDocGroups.map(g => renderGroup(g))}
                        <div key='质检算子' className="pt-1">
                            <button onClick={() => toggleExpansion('质检算子')} className="w-full flex items-center justify-between py-2 px-3 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50">
                                <span>质检算子</span>
                                <ChevronRight className={`w-4 h-4 transform transition-transform ${expandedItems.has('质检算子') ? 'rotate-90' : ''}`} />
                            </button>
                            {expandedItems.has('质检算子') && (
                                <div className="pl-4 mt-1 space-y-1 border-l border-gray-200 ml-3">
                                    {operatorGroups.map(g => renderGroup(g))}
                                </div>
                            )}
                        </div>
                        {exampleGroups.map(g => renderGroup(g))}
                    </div>
                )}
            </div>

            {/* 智能客服质检系统设计 */}
            <div key='智能客服质检系统设计'>
                <button
                  onClick={() => toggleExpansion('智能客服质检系统设计')}
                  className="w-full flex items-center justify-between p-3 text-sm font-semibold text-gray-800 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <ShieldCheck className="w-5 h-5 text-gray-600" />
                    <span>智能客服质检系统设计</span>
                  </div>
                  <ChevronRight className={`w-4 h-4 transform transition-transform ${
                    expandedItems.has('智能客服质检系统设计') ? 'rotate-90' : ''
                  }`} />
                </button>
                {expandedItems.has('智能客服质检系统设计') && (
                    <div className="pl-4 mt-1 space-y-1">
                        {finalDesignGroups.map(g => renderGroup(g))}
                    </div>
                )}
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;