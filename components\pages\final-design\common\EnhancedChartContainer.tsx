import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface EnhancedChartContainerProps {
    title: string;
    subtitle?: string;
    icon: LucideIcon;
    iconColor?: string;
    iconBgColor?: string;
    children: React.ReactNode;
    height?: string;
    delay?: number;
    actions?: React.ReactNode;
    className?: string;
}

/**
 * Enhanced Chart Container Component
 * Provides consistent styling for chart sections across all statistical pages
 */
export const EnhancedChartContainer: React.FC<EnhancedChartContainerProps> = ({
    title,
    subtitle,
    icon: Icon,
    iconColor = 'text-blue-600',
    iconBgColor = 'bg-gradient-to-br from-blue-100 to-indigo-100',
    children,
    height = 'h-80',
    delay = 0,
    actions,
    className = ''
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ 
                duration: 0.6, 
                delay,
                type: "spring",
                stiffness: 80
            }}
            className={`bg-white/95 backdrop-blur-sm rounded-2xl shadow-sm border border-gray-200/60 p-8 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300 ${className}`}
        >
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-4">
                    <motion.div 
                        className={`p-3 ${iconBgColor} rounded-xl shadow-lg`}
                        whileHover={{ 
                            scale: 1.1,
                            rotate: [0, -5, 5, 0]
                        }}
                        transition={{ duration: 0.3 }}
                    >
                        <Icon className={`w-6 h-6 ${iconColor}`} />
                    </motion.div>
                    <div>
                        <motion.h3 
                            className="text-xl font-bold text-slate-900"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: delay + 0.1 }}
                        >
                            {title}
                        </motion.h3>
                        {subtitle && (
                            <motion.p 
                                className="text-sm text-slate-600 mt-1"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: delay + 0.2 }}
                            >
                                {subtitle}
                            </motion.p>
                        )}
                    </div>
                </div>
                {actions && (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: delay + 0.3 }}
                    >
                        {actions}
                    </motion.div>
                )}
            </div>
            
            {/* Chart Content */}
            <motion.div 
                className={height}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: delay + 0.4, duration: 0.5 }}
            >
                {children}
            </motion.div>
        </motion.div>
    );
};

export default EnhancedChartContainer;
