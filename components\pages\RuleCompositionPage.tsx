import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Zap, Puzzle, Target, Share2, Type, Mic, Bot, Settings, ChevronRight, GraduationCap, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

// --- 数据定义 ---

const coreConcepts = [
  {
    icon: Puzzle,
    title: '规则 (Rule)',
    description: '由一个或多个"条件"通过"逻辑关系"组合而成，是质检的最高单位。',
  },
  {
    icon: Target,
    title: '条件 (Condition)',
    description: '规则的基本组成部分，由一个"算子"和其对应的"检查范围"构成。',
  },
  {
    icon: Zap,
    title: '算子 (Operator)',
    description: '具体的分析方式或检查手段，如关键词检查、静音检测等。',
  },
  {
    icon: Share2,
    title: '逻辑关系 (Logic)',
    description: '用于连接多个"条件"，定义它们之间的组合方式，如 AND (&&), OR (||), NOT (!)。',
  },
];

const logicExamples = [
  {
    expression: 'A && B',
    description: '逻辑与：当条件A和条件B都满足时，规则才命中。',
  },
  {
    expression: 'A || B',
    description: '逻辑或：当条件A或条件B中任意一个满足时，规则即命中。',
  },
  {
    expression: 'A && !B',
    description: '逻辑非：当条件A满足，且条件B不满足时，规则命中。',
  },
  {
    expression: 'A && (B || C)',
    description: '组合逻辑：当条件A满足，并且(条件B或条件C中任意一个满足)时，规则才命中。括号用于提升运算优先级。',
  },
];

const exampleConversation = [
  { speaker: '客户', text: '你好，我上个月买的那个保险，现在不想要了，请问怎么退保？', highlight: null },
  { speaker: '客服', text: '您好，女士。请问您说的具体是哪个保单呢？', highlight: null },
  { speaker: '客户', text: '就是那个安享一生的重疾险。我想了解下犹豫期内外退保有什么区别？', highlight: 'A' },
  { speaker: '客服', text: '好的，我为您查询一下... 是这样的，在15天的犹豫期内退保，我们会退还您所交的全部保费；如果超过了犹豫期，就只能退还保单当前的现金价值了。', highlight: 'B' },
];

const operatorGroups = [
  { name: '文字检查', icon: Type, color: 'blue', operators: ['关键词检查', '文本相似度检查', '正则表达式检查', '上下文重复检查', '信息实体检查'] },
  { name: '语音检查', icon: Mic, color: 'green', operators: ['通话静音检查', '语速检查', '抢话设置', '角色判断', '非正常挂机', '非正常接听', '录音时长检测', '能量检测', '对话语句数检测'] },
  { name: '模型检查', icon: Bot, color: 'purple', operators: ['客户模型检测', '客服模型检测'] },
  { name: '其它检查', icon: Settings, color: 'orange', operators: ['随录参数检查'] },
];

const ConditionCard = ({ id, name, operator, scope, color, isOpen, onClick }: any) => (
  <motion.div
    layout
    className={`bg-white rounded-lg shadow-sm border-l-4 p-4 cursor-pointer hover:shadow-md transition-shadow`}
    style={{ borderColor: color }}
    onClick={onClick}
  >
    <motion.div layout className="flex justify-between items-center">
      <h4 className="font-semibold text-gray-800">{id}: {name}</h4>
      <ChevronRight className={`w-5 h-5 text-gray-400 transform transition-transform ${isOpen ? 'rotate-90' : ''}`} />
    </motion.div>
    {isOpen && (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-4 pt-4 border-t border-dashed"
      >
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-500">构成:</span>
          <span className="inline-flex items-center px-2 py-1 bg-gray-100 text-sm font-semibold text-gray-700 rounded-md">
            <Zap size={14} className="mr-1" /> {operator}
          </span>
          <span className="text-gray-400">+</span>
          <span className="inline-flex items-center px-2 py-1 bg-gray-100 text-sm font-semibold text-gray-700 rounded-md">
            <Target size={14} className="mr-1" /> {scope}
          </span>
        </div>
      </motion.div>
    )}
  </motion.div>
);

const LogicSymbol = ({ symbol }: { symbol: string }) => (
  <div className="flex items-center justify-center my-2">
    <div className="w-8 h-8 flex items-center justify-center bg-gray-200 text-gray-600 font-mono font-bold rounded-full">
      {symbol}
    </div>
  </div>
);


export const RuleCompositionPage: React.FC = () => {
    const [openCondition, setOpenCondition] = useState<string | null>(null);

    const handleConditionClick = (id: string) => {
        setOpenCondition(openCondition === id ? null : id);
    };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="bg-white border-b border-gray-200 mb-6 -mx-6 -mt-6">
        <div className="max-w-full mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">规则构成说明</h1>
          <p className="text-gray-600 mt-1">可视化地理解一个质检规则是如何由【条件】、【算子】、【逻辑】等基本元素构成的。</p>
        </div>
      </div>
      
      <div className="max-w-full mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">算子家族概览</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {operatorGroups.map(group => (
                        <div key={group.name} className={`rounded-lg p-4 bg-${group.color}-50 border border-${group.color}-200`}>
                            <div className={`flex items-center font-semibold text-${group.color}-600 mb-3`}>
                                <group.icon className="w-5 h-5 mr-2" />
                                {group.name}
                            </div>
                            <div className="flex flex-wrap gap-2">
                                {group.operators.map(op => (
                                    <span key={op} className={`text-xs font-medium bg-${group.color}-100 text-${group.color}-800 px-2 py-1 rounded-full`}>{op}</span>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-1">规则构成可视化</h2>
                <p className="text-sm text-gray-500 mb-6">这是一个典型的规则示例。点击下方的"条件"卡片可以查看其内部构成。</p>
                
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-center text-gray-700 mb-4">规则：客户询问退保且客服正确回答</h3>
                    <div className="max-w-md mx-auto">
                        <ConditionCard 
                            id="条件 A"
                            name="客户询问犹豫期退保"
                            operator="关键词检查"
                            scope="适用角色：客户"
                            color="#3b82f6"
                            isOpen={openCondition === 'A'}
                            onClick={() => handleConditionClick('A')}
                        />
                        <LogicSymbol symbol="&&" />
                        <ConditionCard 
                            id="条件 B"
                            name="客服回答犹豫期内外区别"
                            operator="文本相似度"
                            scope="适用角色：客服"
                            color="#10b981"
                            isOpen={openCondition === 'B'}
                            onClick={() => handleConditionClick('B')}
                        />
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">逻辑关系示例</h2>
                <div className="space-y-4">
                    {logicExamples.map(ex => (
                        <div key={ex.expression} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <p className="font-mono text-blue-600 font-semibold text-base">{ex.expression}</p>
                            <p className="mt-2 text-sm text-gray-700">{ex.description}</p>
                        </div>
                    ))}
                </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-1">场景代入：一次完整的对话</h2>
                <p className="text-sm text-gray-500 mb-6">下面这段对话将触发我们在"规则构成"中定义的示例规则。</p>
                <div className="space-y-4 rounded-lg bg-gray-50 border border-gray-200 p-4">
                    {exampleConversation.map((item, index) => (
                        <div key={index} className={`flex ${item.speaker === '客服' ? 'justify-end' : 'justify-start'}`}>
                            <div className={`max-w-[80%] p-3 rounded-lg ${
                                item.speaker === '客服' ? 'bg-green-100 text-green-900' : 'bg-blue-100 text-blue-900'
                            } ${item.highlight ? `ring-2 ${item.highlight === 'A' ? 'ring-blue-400' : 'ring-green-400'}` : ''}`}>
                                <p className="text-sm">{item.text}</p>
                                {item.highlight && (
                                    <div className={`text-xs font-bold mt-2 text-right ${item.highlight === 'A' ? 'text-blue-600' : 'text-green-600'}`}>
                                        匹配 [条件 {item.highlight}]
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
                 <div className="mt-4 text-center text-sm text-gray-600">
                    <p>因为 <span className="font-bold text-blue-600">[条件 A]</span> 和 <span className="font-bold text-green-600">[条件 B]</span> 都已命中，且逻辑关系为 <span className="font-mono bg-gray-200 px-1 rounded">A && B</span>，故此规则最终判定为 <span className="font-bold text-red-600">命中</span>。</p>
                </div>
            </div>
        </div>

        {/* Right Column */}
        <div className="lg:col-span-1 space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">核心名词解析</h2>
                <div className="space-y-4">
                    {coreConcepts.map((concept) => (
                        <div key={concept.title} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                           <div className="flex items-center text-gray-800 font-semibold mb-2">
                             <concept.icon className="w-5 h-5 mr-3 text-blue-600" />
                             {concept.title}
                           </div>
                           <p className="text-sm text-gray-600 ml-8">{concept.description}</p>
                        </div>
                    ))}
                </div>
             </div>
             <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">速查表</h2>
                <div className="space-y-4">
                    <div>
                        <h3 className="text-base font-semibold text-gray-700 mb-2">逻辑运算符</h3>
                        <ul className="space-y-1 text-sm text-gray-600 list-inside">
                           <li><span className="font-mono bg-gray-200 px-1 rounded mr-2">&&</span> 并且 (AND)</li>
                           <li><span className="font-mono bg-gray-200 px-1 rounded mr-2">||</span> 或者 (OR)</li>
                           <li><span className="font-mono bg-gray-200 px-1 rounded mr-2">!</span> 非 (NOT)</li>
                        </ul>
                    </div>
                    <div className="pt-4 border-t border-gray-200">
                        <h3 className="text-base font-semibold text-gray-700 mb-2">范围指示符</h3>
                         <ul className="space-y-1 text-sm text-gray-600 list-inside">
                           <li><span className="font-semibold text-blue-600 mr-2">正数</span> 从段落开头向后数</li>
                           <li><span className="font-semibold text-blue-600 mr-2">负数</span> 从段落末尾向前数</li>
                           <li><span className="font-semibold text-blue-600 mr-2">0</span> 命中句本身 (仅部分模式)</li>
                        </ul>
                    </div>
                </div>
            </div>
             <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">关联学习</h2>
                <div className="space-y-3">
                    <Link to="/demos/detection-scope" className="group block p-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors">
                        <div className="flex items-center justify-between">
                            <div className="font-semibold text-blue-700">
                                <GraduationCap className="w-5 h-5 mr-2 inline-block" />
                                深入检测范围
                            </div>
                            <ArrowRight className="w-5 h-5 text-blue-500 group-hover:translate-x-1 transition-transform" />
                        </div>
                    </Link>
                    <Link to="/complex-example" className="group block p-4 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors">
                         <div className="flex items-center justify-between">
                            <div className="font-semibold text-purple-700">
                                <GraduationCap className="w-5 h-5 mr-2 inline-block" />
                                深入逻辑规则
                            </div>
                            <ArrowRight className="w-5 h-5 text-purple-500 group-hover:translate-x-1 transition-transform" />
                        </div>
                    </Link>
                </div>
            </div>
             
        </div>
      </div>
    </div>
  );
};

export default RuleCompositionPage; 