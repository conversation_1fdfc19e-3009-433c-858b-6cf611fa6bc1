import React from 'react';
import { AgentModelConfigType, detectionTypes, DetectionType } from '../AgentModelPage';
import { Tooltip } from '../../../common/Tooltip';

/**
 * @typedef {object} Props
 * @property {AgentModelConfigType} config
 * @property {React.Dispatch<React.SetStateAction<AgentModelConfigType>>} setConfig
 */
interface Props {
  config: AgentModelConfigType;
  setConfig: React.Dispatch<React.SetStateAction<AgentModelConfigType>>;
}

/**
 * 客服模型检测配置组件
 * @param {Props} props
 * @returns {React.ReactElement}
 */
export const AgentModelConfig: React.FC<Props> = ({ config, setConfig }) => {
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setConfig({ ...config, detectionType: e.target.value as DetectionType });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
            value={config.detectionRole}
            disabled
          >
            <option value="agent">客服</option>
          </select>
          <Tooltip text="此模型专用于检测客服的意图，因此角色固定为“客服”。">
            <span 
              className="inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50"
            >
              ?
            </span>
          </Tooltip>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
            value={config.detectionScope}
            disabled
          >
            <option value="full">全文</option>
          </select>
        </div>

        {/* 系统分析提示 */}
        <div className="flex items-center space-x-4 pl-[96px]">
          <p className="text-xs text-gray-500 bg-gray-50 p-2 rounded-md">
            由系统内置的算法模型进行分析，无需配置。目前可检测的类型有：
            <span className="text-blue-600 font-medium">
              反问反感、引导投诉、推诿、辱骂
            </span>。
          </p>
        </div>

        {/* 检测类型 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测类型</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionType}
            onChange={handleTypeChange}
          >
            <option value="">请选择</option>
            {detectionTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}; 