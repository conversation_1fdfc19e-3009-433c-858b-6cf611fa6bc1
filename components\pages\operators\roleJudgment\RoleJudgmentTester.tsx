import React, { useState } from 'react';

/**
 * 角色判断测试器属性接口
 */
interface RoleJudgmentTesterProps {
  config: {
    sentenceIndex: number;
    role: 'customer' | 'agent';
  };
  text: string;
  setText: React.Dispatch<React.SetStateAction<string>>;
}

/**
 * 角色判断实时测试组件
 * @param config 当前配置
 * @param text 测试文本
 * @param setText 更新测试文本的函数
 * @constructor
 */
const RoleJudgmentTester: React.FC<RoleJudgmentTesterProps> = ({ config, text, setText }) => {
  const [testResult, setTestResult] = useState<{
    matched: boolean;
    details: string;
    checkedSentence?: string;
    detectedRole?: string;
  } | null>(null);

  /**
   * 执行测试
   */
  const runTest = () => {
    if (config.sentenceIndex === 0) {
      setTestResult({
        matched: false,
        details: '规则未命中 - 句子索引不能为 0。',
      });
      return;
    }

    const sentences = text.split('\n').filter(s => s.trim());
    if (sentences.length === 0) {
      setTestResult({
        matched: false,
        details: '规则未命中 - 测试文本为空。',
      });
      return;
    }

    let targetSentence: string | undefined;
    let actualIndex: number;

    if (config.sentenceIndex > 0) {
      actualIndex = config.sentenceIndex - 1;
    } else { // sentenceIndex < 0
      actualIndex = sentences.length + config.sentenceIndex;
    }
    
    targetSentence = sentences[actualIndex];

    if (!targetSentence) {
      setTestResult({
        matched: false,
        details: `规则未命中 - 找不到第 ${config.sentenceIndex} 句，总共只有 ${sentences.length} 句。`,
      });
      return;
    }

    let detectedRole: 'customer' | 'agent' | 'unknown' = 'unknown';
    if (targetSentence.startsWith('客户：')) {
      detectedRole = 'customer';
    } else if (targetSentence.startsWith('客服：')) {
      detectedRole = 'agent';
    }

    const matched = detectedRole === config.role;
    const roleMap = { customer: '客户', agent: '客服', unknown: '未知' };

    setTestResult({
      matched,
      details: matched ? '规则已命中' : `规则未命中 - 检测到角色为 "${roleMap[detectedRole]}"，需要为 "${roleMap[config.role]}"`,
      checkedSentence: targetSentence,
      detectedRole: roleMap[detectedRole],
    });
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">🧪 实时测试</h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              setText('客户：你好，我想咨询一下。\n客服：您好，请问有什么可以帮您？\n客户：这个产品怎么用？\n客服：我来为您介绍。');
              setTestResult(null);
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">📝 测试文本</label>
          <textarea
            id="test-text"
            rows={8}
            className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="请输入需要测试的对话文本，每行一句话..."
          />
        </div>

        <button
          onClick={runTest}
          disabled={!text.trim() || config.sentenceIndex === 0}
          className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all"
        >
          执行测试
        </button>

        {testResult && (
          <div className="border-t border-gray-200 pt-6 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">📊 测试结果</h3>
            <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${testResult.matched ? 'bg-green-100' : 'bg-red-100'}`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                      {testResult.matched ? '✓' : '✗'}
                    </span>
                  </div>
                  <div>
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                      {testResult.matched ? '规则命中' : '规则未命中'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {testResult.checkedSentence && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                 <h4 className="font-semibold text-gray-800 mb-2">详细信息</h4>
                 <div className="space-y-2 text-sm">
                   <div className="flex justify-between">
                     <span className="text-gray-600">被检测句子:</span>
                     <span className="font-medium text-right text-gray-800">{testResult.checkedSentence}</span>
                   </div>
                   <div className="flex justify-between">
                     <span className="text-gray-600">检测到的角色:</span>
                     <span className={`font-medium px-2 py-0.5 rounded-full text-xs ${testResult.detectedRole === '客户' ? 'bg-blue-100 text-blue-800' : testResult.detectedRole === '客服' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>{testResult.detectedRole}</span>
                   </div>
                 </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default RoleJudgmentTester; 