import React from 'react';

/**
 * 角色判断使用提示组件
 * @constructor
 */
const RoleJudgmentTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">使用提示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">句子索引规则</h4>
              <div className="space-y-2 text-sm">
                <p>• <strong className="text-gray-700">正数索引：</strong> 从对话开头计数，例如 `1` 代表第一句话。</p>
                <p>• <strong className="text-gray-700">负数索引：</strong> 从对话末尾计数，例如 `-1` 代表最后一句话。</p>
                <p>• <strong className="text-red-600">无效索引：</strong> `0` 是无效输入，会导致规则不执行。</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <div className="space-y-2 text-sm">
                <p>• <strong className="text-gray-700">开场白检查：</strong> 使用索引 `1` 检查第一句是否为客服的标准欢迎语。</p>
                <p>• <strong className="text-gray-700">结束语检查：</strong> 使用索引 `-1` 检查最后一句是否为客服的标准结束语。</p>
                <p>• <strong className="text-gray-700">文本格式：</strong> 确保测试文本每行以"客户："或"客服："开头，以保证角色识别的准确性。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleJudgmentTips; 