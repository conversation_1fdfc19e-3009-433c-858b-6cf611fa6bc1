import React from 'react';
import {
    Card, Input, DatePicker, Button, Space, Radio, Tree, Select, Form, Typography,
} from 'antd';
import { motion, AnimatePresence } from 'framer-motion';
import { CloseOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { Title } = Typography;

// Mock Data for Quality Schemes
const mockQualitySchemes = [
    { id: 'scheme1', name: '标准服务流程质检方案' },
    { id: 'scheme2', name: '营销流程质检方案' },
    { id: 'scheme3', name: '新人上岗考察方案' },
    { id: 'scheme4', name: '金融合规质检方案' },
];

// Mock Data for Data Sources, inspired by DataSourceManagementPage
const mockDataSources = [
    {
      id: '1',
      name: '呼叫中心录音SFTP目录',
      type: 'directory',
    },
    {
      id: '2',
      name: '客服系统A通话记录API',
      type: 'api',
    },
    {
      id: '3',
      name: '呼叫中心业务数据库',
      type: 'database',
    }
];

// Mock Data for Tree Select
const treeData = [
    {
        title: '客服中心',
        key: '0-0',
        children: [
            { title: '客服A组', key: '0-0-0' },
            { title: '客服B组', key: '0-0-1' },
        ],
    },
    {
        title: '营销中心',
        key: '0-1',
        children: [{ title: '营销A组', key: '0-1-0' }],
    },
];

interface FinalCreateTaskPageProps {
    onClose: () => void;
    onSubmit: (values: any) => void;
    taskId?: string;
    initialValues?: any;
}

const drawerVariants = {
    hidden: { x: '100%' },
    visible: { x: 0 },
};

const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
}


/**
 * 质检任务管理 - 新建/编辑任务页
 * @constructor
 */
const FinalCreateTaskPage: React.FC<FinalCreateTaskPageProps> = ({ onClose, onSubmit, taskId, initialValues }) => {
    const isEditing = !!taskId;
    const [form] = Form.useForm();

    const dataSourceId = Form.useWatch('dataSourceId', form);
    const executionPlan = Form.useWatch('executionPlan', form);
    const targetType = Form.useWatch('targetType', form);
    const qualityMode = Form.useWatch('qualityMode', form);
    
    // Default values
    React.useEffect(() => {
        if (isEditing && initialValues) {
            form.setFieldsValue(initialValues);
        } else {
            form.setFieldsValue({
                dataSourceId: '1',
                executionPlan: 'now',
                qualityMode: 'full',
                targetType: 'all',
                samplingRatio: 10,
                ...(initialValues || {})
            });
        }
    }, [form, isEditing, initialValues]);

    const onFinish = (values: any) => {
        onSubmit({ ...values, taskId });
    };

    return (
        <AnimatePresence>
             <motion.div
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40"
                onClick={onClose}
            >
                <motion.div
                    variants={drawerVariants}
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    className="bg-gray-50 h-full w-full max-w-4xl shadow-2xl flex flex-col"
                    onClick={(e) => e.stopPropagation()}
                >
                     <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200 bg-white">
                        <h2 className="text-xl font-semibold text-gray-800">{isEditing ? '编辑一次性质检任务' : '新建一次性质检任务'}</h2>
                        <button
                            onClick={onClose}
                            className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors"
                        >
                            <CloseOutlined className="w-6 h-6" />
                        </button>
                    </div>

                    <Form form={form} layout="vertical" onFinish={onFinish} className="flex-grow flex flex-col overflow-hidden">
                       <div className="flex-grow p-6 space-y-6 overflow-y-auto">
                            <Card title={<Title level={5}>第一部分：基本信息</Title>}>
                                <Form.Item label="任务名称" name="taskName" required rules={[{ required: true }]}>
                                    <Input placeholder="例如：11月营销活动通话质检" />
                                </Form.Item>
                                <Form.Item label="任务描述" name="taskDescription">
                                    <Input.TextArea rows={2} placeholder="可选，用于备注任务目的等" />
                                </Form.Item>
                            </Card>

                            <Card title={<Title level={5}>第二部分：质检范围</Title>}>
                                <Form.Item label="数据来源" name="dataSourceId" required>
                                    <Select placeholder="请选择一个数据来源">
                                        {mockDataSources.map(ds => (
                                            <Select.Option key={ds.id} value={ds.id}>{ds.name}</Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                {!!dataSourceId && <>
                                    <Form.Item 
                                        label="通话时间范围" 
                                        name="fixedDateRange" 
                                        required 
                                        rules={[{required: true, message: '请选择固定的时间范围'}]}
                                        help="一次性任务必须选择固定的时间范围"
                                    >
                                        <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" className="w-full" />
                                    </Form.Item>
                                    <Form.Item label="质检对象" name="targetType" required>
                                        <Radio.Group>
                                            <Radio value="all">全部坐席</Radio>
                                            <Radio value="byStruct">按组织架构选择</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                    {targetType === 'byStruct' && (
                                        <Form.Item name="targetKeys" valuePropName="checkedKeys">
                                            <Tree checkable defaultExpandAll treeData={treeData} />
                                        </Form.Item>
                                    )}
                                </>}
                            </Card>

                            <Card title={<Title level={5}>第三部分：质检规则与模式</Title>}>
                                <Form.Item label="质检方案" name="qualityScheme" required rules={[{ required: true }]}>
                                    <Select placeholder="请选择一个质检方案">
                                        {mockQualitySchemes.map(scheme => (
                                            <Select.Option key={scheme.id} value={scheme.id}>{scheme.name}</Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                                <Form.Item label="质检模式" name="qualityMode" required>
                                    <Radio.Group>
                                        <Radio value="full">全量质检</Radio>
                                        <Radio value="sampling">抽样质检</Radio>
                                    </Radio.Group>
                                </Form.Item>
                                {qualityMode === 'sampling' && (
                                    <Form.Item label="抽样比例" name="samplingRatio">
                                        <Input type='number' addonAfter="%" style={{ maxWidth: 150 }} />
                                    </Form.Item>
                                )}
                            </Card>
                            
                            <Card title={<Title level={5}>第四部分：执行计划</Title>}>
                                <Form.Item label="执行方式" name="executionPlan" required>
                                    <Radio.Group>
                                        <Radio value="now">立即执行</Radio>
                                        <Radio value="timed">定时执行</Radio>
                                    </Radio.Group>
                                </Form.Item>
                                {executionPlan === 'timed' && (
                                    <Form.Item name="timedDate" required rules={[{ required: true, message: '请选择执行时间' }]}>
                                        <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" placeholder="选择一个未来的时间点" />
                                    </Form.Item>
                                )}
                            </Card>
                        </div>

                        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div className="flex justify-end gap-3">
                                <Button onClick={onClose}>取消</Button>
                                <Button type="primary" htmlType="submit">
                                    {isEditing ? '保存任务' : '创建任务'}
                                </Button>
                            </div>
                        </div>
                    </Form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default FinalCreateTaskPage; 