
---

### 页面二十二：语音识别引擎管理 (SpeechEngineManagementPage.tsx)

#### 1. 核心定位与目标
该页面是系统**语音转文本 (Speech-to-Text, STT)** 能力的配置中心。其核心目标是让管理员能够统一管理和配置用于将**通话录音**转换成**文字**的语音识别引擎。这是所有后续文本分析（如关键词匹配、情绪识别、大模型质检）的**绝对前提**，其识别的准确率直接决定了整个质检系统的天花板。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "语音识别引擎管理"
*   **副标题**: "管理云端和离线语音识别引擎配置"
*   **图标**: `Mic` (麦克风图标)，直观地代表语音和录音。
*   **核心操作**: `新建引擎` 按钮，点击后弹出添加引擎的抽屉表单。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 在多个已配置的STT引擎中进行快速查找。
*   **筛选字段**: `引擎名称/服务商`, `引擎类型`, `状态`。

**c. 引擎列表表格 (`Table`)**
*   **布局**: 以表格形式展示所有已配置的语音识别引擎。
*   **表格列**:
    *   `序号`
    *   `引擎名称`: 用户自定义的名称，下方是服务商名称。
    *   `引擎类型`: 用带图标的徽章清晰地区分**“云端引擎” (`Cloud`图标)**和**“离线引擎” (`HardDrive`图标)**。
    *   `服务商`: 如阿里云、腾讯云、OpenAI (Whisper)等。
    *   `状态`: 用“运行中”或“未启用”的徽章显示引擎的当前可用状态。
    *   `最后使用`: 记录该引擎最近一次处理任务的时间。
    *   `操作`:
        *   **编辑 (`Edit`图标)**: 修改引擎配置。
        *   **启用/禁用 (`Power`/`PowerOff`图标)**: 快速切换引擎是否在系统中可用。系统在处理任务时，只会调用“运行中”的引擎。
        *   **删除 (`Trash2`图标)**: 删除该引擎配置（通常会带有确认提示）。

**d. 分页组件 (`UnifiedPagination`)**
*   用于浏览和导航引擎列表。

**e. 添加/编辑引擎的抽屉表单 (`Drawer`)**
*   **功能**: 提供一个结构化的界面来配置一个新的STT服务。
*   **表单字段 (根据类型动态变化)**:
    *   `引擎名称`: 用户自定义的别名。
    *   `引擎类型`: **核心选择**，下拉选择“云端引擎”或“离线引擎”。
    *   `服务商`: 引擎提供商的名称。
    *   **当类型为“云端引擎”时**:
        *   `API端点`: 云服务的接口地址。
        *   `API密钥`: 访问服务的凭证（通常是AccessKey ID/Secret）。
    *   **当类型为“离线引擎”时**:
        *   `模型路径`: 指向部署在本地服务器上的模型文件或服务地址。
    *   `描述`: 对该引擎特点或用途的说明。
    *   `启用状态`: 一个开关（Switch），用于设置创建后是否立即启用。

#### 3. 核心交互与操作
*   **统一接入与解耦**: 与大模型管理页面类似，此页面为系统提供了一个统一的STT接入层。上游的数据处理模块无需关心具体使用的是哪个厂商的引擎，只需调用统一的接口即可。这使得更换或增加STT供应商变得非常容易，而无需修改核心业务代码。
*   **混合部署支持**: 同时支持**云端**和**离线**引擎，是这个系统的一大亮点。
    *   **云端引擎**: 优点是开箱即用、性能稳定、通常识别效果好。缺点是数据需要上传到云端，可能存在数据安全顾虑，且按量付费会产生持续成本。
    *   **离线引擎 (如Whisper)**: 优点是**数据私有化**，所有处理都在本地完成，安全性高；一次性投入后无持续调用费用。缺点是需要自行部署和维护，对服务器硬件有一定要求。
    *   **价值**: 为不同需求（如金融、政府等对数据安全要求极高的行业）的企业提供了灵活的选择。
*   **服务可用性管理**: 通过“启用/禁用”和状态监控，管理员可以灵活控制当前系统默认使用哪个STT引擎，或者在某个引擎出现故障时快速切换到备用引擎，保证了系统的稳定性和高可用性。

---