import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Calendar, Plus, Trash2, Clock, Repeat } from 'lucide-react';
import Select, { OnChangeValue } from 'react-select';
import { ConfirmationModal } from './common/ConfirmationModal';

// --- Type Definitions & Constants ---

type SelectOption = { value: string; label: string };

interface BaseFilterConfig { label: string; operators: SelectOption[]; }
interface SelectFilterConfig extends BaseFilterConfig { valueType: 'select'; options: SelectOption[]; }
interface NumberFilterConfig extends BaseFilterConfig { valueType: 'number'; unit: string; }
type FilterConfig = SelectFilterConfig | NumberFilterConfig;

const MOCK_QA_SCHEMES = ['服务规范通用方案', '金融产品销售合规方案', '客户体验优化方案', '历史数据测试方案'];
const MOCK_TEAMS = [
    { value: 'team_a', label: '贷后支持一组' },
    { value: 'team_b', label: '贷后支持二组' },
    { value: 'team_c', label: '售前咨询团队' },
    { value: 'team_d', label: 'VIP客户服务部' },
];
const MOCK_CHANNELS = [
    { value: 'voice', label: '呼叫中心' },
    { value: 'chat', label: '在线客服' },
    { value: 'app', label: 'App内聊天' },
];

const FILTER_FIELDS: Record<string, FilterConfig> = {
    team: { label: '所属团队', operators: [{ value: 'in', label: '属于' }], valueType: 'select', options: MOCK_TEAMS },
    channel: { label: '数据渠道', operators: [{ value: 'in', label: '属于' }], valueType: 'select', options: MOCK_CHANNELS },
    duration: { label: '通话时长', operators: [{ value: 'gt', label: '大于' }, { value: 'lt', label: '小于' }], valueType: 'number', unit: '秒' },
};

const RELATIVE_DATE_RANGES = [
    { value: 'last_day', label: '上一个自然日' },
    { value: 'last_24_hours', label: '过去24小时' },
    { value: 'last_week', label: '上一个自然周' },
    { value: 'last_month', label: '上一个自然月' },
];

const RECURRING_FREQUENCIES = [
    { value: 'daily', label: '每天' },
    { value: 'weekly', label: '每周' },
    { value: 'monthly', label: '每月' },
];

interface FilterCondition { id: string; field: keyof typeof FILTER_FIELDS | ''; operator: string; value: any; }
interface SamplingOptions { strategy: 'all' | 'percentage' | 'fixed'; value: number; }
interface ScheduleOptions {
    type: 'now' | 'scheduled';
    datetime?: string;
    frequency?: string;
    time?: string;
}

interface TaskFormData {
    id?: string;
    taskType: 'one-time' | 'recurring';
    name: string;
    description: string;
    qaScheme: string;
    dateRange: { from: string; to: string };
    relativeDateRange?: string;
    filters: FilterCondition[];
    sampling: SamplingOptions;
    schedule: ScheduleOptions;
}

interface CreateTaskFormProps {
    onClose: () => void;
    onSubmit: (task: TaskFormData) => void;
    initialData?: Partial<TaskFormData>;
}

// --- Sub-components ---

interface FilterBuilderProps {
    initialFilters: FilterCondition[];
    onChange: (filters: FilterCondition[]) => void;
}

const FilterBuilder: React.FC<FilterBuilderProps> = ({ initialFilters, onChange }) => {
    const [filters, setFilters] = useState<FilterCondition[]>(initialFilters);

    useEffect(() => {
        onChange(filters);
    }, [filters, onChange]);
    
    const handleUpdate = (id: string, updates: Partial<FilterCondition>) => {
        const resetValue = updates.field ? { operator: '', value: null } : {};
        setFilters(currentFilters => currentFilters.map(f => f.id === id ? { ...f, ...updates, ...resetValue } : f));
    };
    
    const handleAdd = () => setFilters(fs => [...fs, { id: Date.now().toString(), field: '', operator: '', value: null }]);
    const handleRemove = (id: string) => setFilters(fs => fs.filter(f => f.id !== id));

    const renderValueInput = (filter: FilterCondition) => {
        if (!filter.field) return null;
        const config = FILTER_FIELDS[filter.field];
        if (config.valueType === 'select') {
            return <Select isMulti options={config.options} className="w-full" onChange={v => handleUpdate(filter.id, { value: v })} value={filter.value} />;
        }
        if (config.valueType === 'number') {
            return <div className="relative w-full"><input type="number" className="w-full px-3 py-2 border rounded-lg pr-12" onChange={e => handleUpdate(filter.id, { value: e.target.value })} value={filter.value || ''} /><span className="absolute right-3 top-2.5 text-gray-500">{config.unit}</span></div>;
        }
        return null;
    };

    return (
        <div className="mt-4 space-y-3">
            {filters.map(filter => (
                <div key={filter.id} className="flex items-center gap-2 p-3 bg-white border rounded-md">
                    <select value={filter.field} onChange={e => handleUpdate(filter.id, { field: e.target.value as any })} className="border-gray-300 rounded-lg px-2 py-2 text-sm bg-white">
                        <option value="" disabled>选择字段</option>
                        {Object.entries(FILTER_FIELDS).map(([k, { label }]) => <option key={k} value={k}>{label}</option>)}
                    </select>
                    <select value={filter.operator} onChange={e => handleUpdate(filter.id, { operator: e.target.value })} className="border-gray-300 rounded-lg px-2 py-2 text-sm bg-white w-28" disabled={!filter.field}>
                        <option value="" disabled>条件</option>
                        {filter.field && FILTER_FIELDS[filter.field].operators.map(op => <option key={op.value} value={op.value}>{op.label}</option>)}
                    </select>
                    <div className="flex-grow">{renderValueInput(filter)}</div>
                    <button type="button" onClick={() => handleRemove(filter.id)} className="p-2 text-red-500 hover:bg-red-100 rounded-full"><Trash2 className="w-4 h-4" /></button>
                </div>
            ))}
            <button type="button" onClick={handleAdd} className="mt-3 flex items-center text-sm font-semibold text-blue-600 hover:text-blue-800"><Plus className="w-4 h-4 mr-1" /> 添加筛选条件</button>
        </div>
    );
};

interface DataScopePickerProps {
    taskType: 'one-time' | 'recurring';
    formData: TaskFormData;
    handleDateChange: (field: 'from' | 'to', value: string) => void;
    setFormData: React.Dispatch<React.SetStateAction<TaskFormData>>;
}

const DataScopePicker: React.FC<DataScopePickerProps> = ({ taskType, formData, handleDateChange, setFormData }) => (
    <div className="p-4 border rounded-lg bg-gray-50/50">
        <h3 className="text-lg font-semibold mb-3">数据范围</h3>
        {taskType === 'one-time' ? (
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">通话/会话时间 <span className="text-red-500">*</span></label>
                <div className="flex items-center gap-4">
                    <div className="relative w-full">
                        <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input type="date" value={formData.dateRange.from} onChange={e => handleDateChange('from', e.target.value)} className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg" required />
                    </div>
                    <span className="text-gray-500">至</span>
                    <div className="relative w-full">
                        <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input type="date" value={formData.dateRange.to} onChange={e => handleDateChange('to', e.target.value)} className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg" required />
                    </div>
                </div>
            </div>
        ) : (
            <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">动态时间范围 <span className="text-red-500">*</span></label>
                 <select value={formData.relativeDateRange} onChange={e => setFormData(p => ({...p, relativeDateRange: e.target.value}))} className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white" required>
                     {RELATIVE_DATE_RANGES.map(r => <option key={r.value} value={r.value}>{r.label}</option>)}
                 </select>
            </div>
        )}
        <FilterBuilder initialFilters={formData.filters} onChange={filters => setFormData(p => ({...p, filters: filters}))} />
    </div>
);

interface ScheduleConfigProps {
    taskType: 'one-time' | 'recurring';
    schedule: ScheduleOptions;
    setSchedule: (newSchedule: Partial<ScheduleOptions>) => void;
}

const ScheduleConfig: React.FC<ScheduleConfigProps> = ({ taskType, schedule, setSchedule }) => (
     <div className="p-4 border rounded-lg bg-gray-50/50">
        <h3 className="text-lg font-semibold mb-3">执行方式</h3>
        {taskType === 'one-time' ? (
            <div className="flex items-center gap-6">
                <label className="flex items-center gap-2">
                    <input type="radio" name="one-time-schedule" value="now" checked={schedule.type === 'now'} onChange={() => setSchedule({ type: 'now' })} className="w-4 h-4" /> 立即执行
                </label>
                <label className="flex items-center gap-2">
                    <input type="radio" name="one-time-schedule" value="scheduled" checked={schedule.type === 'scheduled'} onChange={() => setSchedule({ type: 'scheduled', datetime: '' })} className="w-4 h-4" /> 定时执行
                </label>
                 {schedule.type === 'scheduled' && (
                    <input type="datetime-local" value={schedule.datetime} onChange={e => setSchedule({ datetime: e.target.value })} className="border border-gray-300 rounded-lg px-2 py-1.5 text-sm"/>
                )}
            </div>
        ) : (
             <div className="flex items-center gap-4">
                 <label className="text-sm font-medium">执行频率:</label>
                 <select value={schedule.frequency} onChange={e => setSchedule({ frequency: e.target.value })} className="border border-gray-300 rounded-lg px-2 py-1.5 text-sm bg-white">
                      {RECURRING_FREQUENCIES.map(f => <option key={f.value} value={f.value}>{f.label}</option>)}
                 </select>
                 <label className="text-sm font-medium">执行时间:</label>
                 <input type="time" value={schedule.time} onChange={e => setSchedule({ time: e.target.value })} className="border border-gray-300 rounded-lg px-2 py-1.5 text-sm"/>
            </div>
        )}
    </div>
);

// --- Main Component ---

export const CreateTaskForm: React.FC<CreateTaskFormProps> = ({ onClose, onSubmit, initialData }) => {
    const [formData, setFormData] = useState<TaskFormData>({
        id: undefined,
        taskType: 'one-time',
        name: '',
        description: '',
        qaScheme: '',
        dateRange: { from: '', to: '' },
        relativeDateRange: 'last_day',
        filters: [],
        sampling: { strategy: 'all', value: 100 },
        schedule: { type: 'now' },
        ...initialData,
    });
    const [showAdvanced, setShowAdvanced] = useState(false);

    const setSchedule = (newSchedule: Partial<ScheduleOptions>) => {
        setFormData(p => ({ ...p, schedule: { ...p.schedule, ...newSchedule } as ScheduleOptions }));
    };

    const handleTaskTypeChange = (type: 'one-time' | 'recurring') => {
        setFormData(p => ({
            ...p,
            taskType: type,
            schedule: type === 'one-time' ? { type: 'now' } : { type: 'scheduled', frequency: 'daily', time: '09:00' } as any
        }));
    };

    const handleDateChange = (field: 'from' | 'to', value: string) => {
        setFormData(prev => ({ ...prev, dateRange: { ...prev.dateRange, [field]: value } }));
    };

    const setFormValue = (key: keyof TaskFormData, value: any) => {
        setFormData(p => ({...p, [key]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        console.log('Submitting Task Data:', formData);
        onSubmit(formData);
    };
    
    return (
        <motion.div className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <motion.div className="bg-white h-full w-full max-w-3xl shadow-2xl flex flex-col" initial={{ x: '100%' }} animate={{ x: 0 }} exit={{ x: '100%' }} transition={{ type: 'spring', stiffness: 300, damping: 30 }}>
                <header className="flex-shrink-0 flex items-center justify-between p-6 border-b">
                    <h2 className="text-xl font-semibold">{formData.id ? '编辑任务' : '创建新任务'}</h2>
                    <button onClick={onClose} className="p-2 text-gray-400 hover:text-gray-600 rounded-full"><X /></button>
                </header>

                <form onSubmit={handleSubmit} className="flex-grow flex flex-col overflow-hidden">
                    <main className="flex-grow p-6 space-y-6 overflow-y-auto">
                        
                        {/* Task Type Switcher */}
                        <div className="flex justify-center p-1 bg-gray-200 rounded-lg">
                            <button type="button" onClick={() => handleTaskTypeChange('one-time')} className={`w-1/2 px-4 py-2 text-sm font-semibold rounded-md transition-colors ${formData.taskType === 'one-time' ? 'bg-white text-gray-800 shadow' : 'text-gray-600'}`}>
                                <Clock className="w-4 h-4 mr-2 inline-block" /> 一次性任务
                            </button>
                            <button type="button" onClick={() => handleTaskTypeChange('recurring')} className={`w-1/2 px-4 py-2 text-sm font-semibold rounded-md transition-colors ${formData.taskType === 'recurring' ? 'bg-white text-gray-800 shadow' : 'text-gray-600'}`}>
                                <Repeat className="w-4 h-4 mr-2 inline-block" /> 周期性任务
                            </button>
                        </div>

                        {/* Basic Info */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">任务名称 <span className="text-red-500">*</span></label>
                            <input type="text" value={formData.name} onChange={e => setFormValue('name', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
                        </div>
                         <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">所用质检方案 <span className="text-red-500">*</span></label>
                            <select value={formData.qaScheme} onChange={e => setFormValue('qaScheme', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white" required>
                                <option value="" disabled>请选择一个方案</option>
                                {MOCK_QA_SCHEMES.map(scheme => <option key={scheme} value={scheme}>{scheme}</option>)}
                            </select>
                        </div>
                        
                        {/* Data Scope & Filters */}
                        <DataScopePicker taskType={formData.taskType} formData={formData} handleDateChange={handleDateChange} setFormData={setFormData} />
                        
                        {/* Advanced Options: Sampling & Schedule */}
                        <div className="space-y-4">
                             <ScheduleConfig taskType={formData.taskType} schedule={formData.schedule} setSchedule={setSchedule} />

                            <div className="p-4 border rounded-lg bg-gray-50/50">
                                <h3 className="text-lg font-semibold mb-3">抽样设置</h3>
                                <div className="flex items-center gap-6">
                                    <label className="flex items-center gap-2"><input type="radio" name="sampling" value="all" checked={formData.sampling.strategy === 'all'} onChange={() => setFormValue('sampling', { strategy: 'all', value: 100 })} className="w-4 h-4" /> 全量质检</label>
                                    <label className="flex items-center gap-2"><input type="radio" name="sampling" value="percentage" checked={formData.sampling.strategy === 'percentage'} onChange={() => setFormValue('sampling', { strategy: 'percentage', value: 10 })} className="w-4 h-4" /> 按百分比</label>
                                    <label className="flex items-center gap-2"><input type="radio" name="sampling" value="fixed" checked={formData.sampling.strategy === 'fixed'} onChange={() => setFormValue('sampling', { strategy: 'fixed', value: 100 })} className="w-4 h-4" /> 按固定数量</label>
                                </div>
                                {formData.sampling.strategy !== 'all' && (
                                    <div className="mt-3 relative w-48"><input type="number" value={formData.sampling.value} onChange={e => setFormValue('sampling', { ...formData.sampling, value: parseInt(e.target.value) || 0 })} className="w-full px-3 py-2 border border-gray-300 rounded-lg pr-12" /><span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">{formData.sampling.strategy === 'percentage' ? '%' : '条'}</span></div>
                                )}
                            </div>
                        </div>

                    </main>
                    <footer className="flex-shrink-0 px-6 py-4 border-t bg-gray-50 flex justify-end gap-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
                        <button type="submit" className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">提交任务</button>
                    </footer>
                </form>
            </motion.div>
        </motion.div>
    );
}; 