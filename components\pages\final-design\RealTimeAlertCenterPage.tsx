import React, { useState, useEffect, useMemo } from 'react';
import { Card, Badge, Progress, Tooltip, Drawer, Form, Input, Select, DatePicker, Button, Avatar, Popover } from 'antd';
import { Siren, Activity, AlertTriangle, Clock, Users, TrendingUp, Eye, CheckCircle, PlayCircle, UserPlus, Volume2, ExternalLink, Zap, Shield, Target, Heart, Settings } from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertDetailDrawer, AlertDetail, ContextLine } from './components/AlertDetailDrawer';

// 预警等级类型
type AlertLevel = 'critical' | 'high' | 'medium' | 'low';

// 预警状态类型
type AlertStatus = 'unread' | 'read' | 'processing' | 'resolved';

// 预警项接口
interface AlertItem {
  id: string;
  timestamp: string;
  level: AlertLevel;
  ruleName: string;
  agentName: string;
  teamName: string;
  customerPhone: string;
  contextSnippet: ContextLine[];
  status: AlertStatus;
  callId: string;
}

/**
 * 跟进任务接口
 */
interface FollowUpTask {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'inprogress' | 'done';
  priority: 'high' | 'medium' | 'low';
  assignee: {
    name: string;
    avatar: string;
  };
  relatedAgent: {
    name: string;
    team: string;
  };
  triggeringRule: string;
  relatedCallId: string;
  createdAt: string;
  dueDate: string;
  result?: string;
}

// KPI指标接口
interface KPIMetrics {
  totalAlerts: number;
  pendingAlerts: number;
  highestRiskLevel: AlertLevel;
  activeCalls: number;
}

// 规则热力图数据接口
interface RuleHeatmapItem {
  ruleName: string;
  triggerCount: number;
  percentage: number;
}

/**
 * 模拟负责人数据
 */
const mockAssignees = [
    { name: '王班长', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang-leader' },
    { name: '李主管', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li-supervisor' },
];

const RealTimeAlertCenterPage: React.FC = () => {
  const [alertsEnabled, setAlertsEnabled] = useState(true);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetrics>({
    totalAlerts: 0,
    pendingAlerts: 0,
    highestRiskLevel: 'low',
    activeCalls: 0
  });
  const [ruleHeatmap, setRuleHeatmap] = useState<RuleHeatmapItem[]>([]);
  const [isTaskDrawerVisible, setIsTaskDrawerVisible] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<AlertItem | null>(null);
  const [activeTab, setActiveTab] = useState<'new' | 'processed'>('new');
  const [retentionPeriod, setRetentionPeriod] = useState<number>(7);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [selectedAlertForDetail, setSelectedAlertForDetail] = useState<AlertDetail | null>(null);

  const filteredAlerts = useMemo(() => {
    if (activeTab === 'new') {
        return alerts.filter(a => a.status === 'unread' || a.status === 'processing');
    }
    // activeTab === 'processed'
    return alerts.filter(a => a.status === 'read' || a.status === 'resolved');
  }, [alerts, activeTab]);

  // 模拟数据初始化
  useEffect(() => {
    // 模拟KPI数据
    setKpiMetrics({
      totalAlerts: 23,
      pendingAlerts: 8,
      highestRiskLevel: 'critical',
      activeCalls: 156
    });

    // 模拟预警数据
    const mockAlerts: AlertItem[] = [
      {
        id: '1',
        timestamp: '14:30:15',
        level: 'critical',
        ruleName: '客户提及"投诉"关键词',
        agentName: '张三',
        teamName: 'A组',
        customerPhone: '138****5678',
        contextSnippet: [
          { speaker: 'agent', text: '您好，这里是XX服务中心。', timestamp: 120 },
          { speaker: 'customer', text: '我要投诉你们的服务态度，这个问题已经反映了三次了！', timestamp: 135, isHighlight: true },
          { speaker: 'agent', text: '先生您别着急，我来帮您处理...', timestamp: 145 }
        ],
        status: 'unread',
        callId: 'CALL_001'
      },
      {
        id: '2',
        timestamp: '14:28:42',
        level: 'high',
        ruleName: '客户情绪激动检测',
        agentName: '李四',
        teamName: 'B组',
        customerPhone: '139****1234',
        contextSnippet: [
            { speaker: 'agent', text: '请问还有什么可以帮您？', timestamp: 80 },
            { speaker: 'customer', text: '你们这是什么服务！我等了半个小时了！', timestamp: 92, isHighlight: true },
            { speaker: 'agent', text: '非常抱歉让您久等了...', timestamp: 101 }
        ],
        status: 'processing',
        callId: 'CALL_002'
      },
      {
        id: '3',
        timestamp: '14:25:18',
        level: 'medium',
        ruleName: '通话静音超时',
        agentName: '王五',
        teamName: 'A组',
        customerPhone: '137****9876',
        contextSnippet: [
            { speaker: 'customer', text: '请问我的订单什么时候能到？', timestamp: 20 },
            { speaker: 'agent', text: '[静音35秒]', timestamp: 55, isHighlight: true },
            { speaker: 'agent', text: '不好意思，我查一下...', timestamp: 65 }
        ],
        status: 'read',
        callId: 'CALL_003'
      },
      { 
        id: '4', 
        timestamp: '14:22:05', 
        level: 'low', 
        ruleName: '坐席未主动核身', 
        agentName: '赵六', 
        teamName: 'C组', 
        customerPhone: '150****4321', 
        contextSnippet: [
            { speaker: 'agent', text: '您好，这里是xx客服。', timestamp: 5, isHighlight: true },
            { speaker: 'customer', text: '你好，我问一下...', timestamp: 12 }
        ], 
        status: 'resolved', 
        callId: 'CALL_004'
      },
      { 
        id: '5', 
        timestamp: '14:20:11', 
        level: 'high', 
        ruleName: '客户情绪激动检测', 
        agentName: '孙七', 
        teamName: 'B组', 
        customerPhone: '139****1234', 
        contextSnippet: [
            { speaker: 'customer', text: '我已经说了很多遍了！你们到底能不能解决！', timestamp: 78, isHighlight: true }
        ], 
        status: 'unread', 
        callId: 'CALL_005'
      },
    ];
    setAlerts(mockAlerts);

    // 模拟规则热力图数据
    setRuleHeatmap([
      { ruleName: '客户提及"投诉"关键词', triggerCount: 8, percentage: 35 },
      { ruleName: '客户情绪激动检测', triggerCount: 6, percentage: 26 },
      { ruleName: '通话静音超时', triggerCount: 4, percentage: 17 },
      { ruleName: '坐席未主动核身', triggerCount: 3, percentage: 13 },
      { ruleName: '语速过快检测', triggerCount: 2, percentage: 9 }
    ]);
  }, []);

  // 获取预警等级样式
  const getAlertLevelStyle = (level: AlertLevel) => {
    switch (level) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取预警等级文本
  const getAlertLevelText = (level: AlertLevel) => {
    switch (level) {
      case 'critical': return '严重';
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '未知';
    }
  };

  // 获取状态样式
  const getStatusStyle = (status: AlertStatus) => {
    switch (status) {
      case 'unread':
        return 'bg-red-50 text-red-700';
      case 'read':
        return 'bg-gray-50 text-gray-700';
      case 'processing':
        return 'bg-blue-50 text-blue-700';
      case 'resolved':
        return 'bg-green-50 text-green-700';
      default:
        return 'bg-gray-50 text-gray-700';
    }
  };

  // 获取状态文本
  const getStatusText = (status: AlertStatus) => {
    switch (status) {
      case 'unread': return '未读';
      case 'read': return '已读';
      case 'processing': return '处理中';
      case 'resolved': return '已解决';
      default: return '未知';
    }
  };

  // 处理预警操作
  const handleAlertAction = (alertId: string, action: 'mark-read' | 'mark-processing' | 'create-task' | 'real-time-listen' | 'view-details') => {
    console.log(`处理预警 ${alertId}，操作：${action}`);

    if (action === 'view-details') {
        handleViewDetails(alertId);
        return;
    }

    if (action === 'create-task') {
        const alertToProcess = alerts.find(a => a.id === alertId);
        if (alertToProcess) {
            setSelectedAlert(alertToProcess);
            setIsTaskDrawerVisible(true);
        }
        return;
    }

    let newStatus: AlertStatus | undefined;
    if (action === 'mark-read') newStatus = 'read';
    if (action === 'mark-processing') newStatus = 'processing';
    
    if (newStatus) {
        setAlerts(prevAlerts =>
            prevAlerts.map(alert =>
                alert.id === alertId ? { ...alert, status: newStatus! } : alert
            )
        );
    }
  };

  const handleDrawerAction = (alertId: string, action: 'mark-read' | 'mark-processing' | 'create-task') => {
    setIsDetailDrawerVisible(false);
    // Use a timeout to ensure the detail drawer has closed before opening the task drawer
    setTimeout(() => {
        handleAlertAction(alertId, action);
    }, 300);
  };
  
  const handleViewDetails = (alertId: string) => {
    const record = alerts.find(a => a.id === alertId);
    if (!record) return;

    // Map AlertItem to AlertDetail
    const alertDetail: AlertDetail = {
        id: record.id,
        triggerTime: record.timestamp,
        ruleName: record.ruleName,
        ruleDescription: `这是关于 "${record.ruleName}" 规则的详细描述和触发条件说明。`,
        level: record.level,
        agent: { name: record.agentName, id: `AGENT-${record.agentName}`, team: record.teamName },
        customer: { phone: record.customerPhone },
        callId: record.callId,
        disposition: record.status,
        contextSnippet: record.contextSnippet,
    };
    setSelectedAlertForDetail(alertDetail);
    setIsDetailDrawerVisible(true);
  };

  // 跳转到规则配置
  const handleRuleConfig = () => {
    window.location.href = '/final-design/rule-library';
  };

  const handleTaskFormSubmit = (values: any) => {
    if (!selectedAlert) return;

    const assignee = mockAssignees.find(a => a.name === values.assignee)
    if (!assignee) return;

    const newTask: FollowUpTask = {
        id: `FT-${Date.now()}`,
        title: values.title,
        description: values.description,
        status: 'todo',
        priority: values.priority,
        assignee: assignee,
        relatedAgent: {
            name: selectedAlert.agentName,
            team: selectedAlert.teamName
        },
        triggeringRule: selectedAlert.ruleName,
        relatedCallId: selectedAlert.callId,
        createdAt: new Date().toISOString().split('T')[0],
        dueDate: values.dueDate.format('YYYY-MM-DD'),
    };

    console.log('创建的新任务:', newTask);
    // In a real app, you'd call an API here.

    setAlerts(prevAlerts =>
        prevAlerts.map(alert =>
            alert.id === selectedAlert.id ? { ...alert, status: 'resolved' } : alert
        )
    );

    setIsTaskDrawerVisible(false);
    setSelectedAlert(null);
  };

  const retentionSettingsContent = (
    <div style={{ width: 200 }}>
        <p className="text-sm font-medium text-gray-800 mb-2">预警保留时长</p>
        <Select
            value={retentionPeriod}
            onChange={setRetentionPeriod}
            style={{ width: '100%' }}
        >
            <Select.Option value={1}>1 天</Select.Option>
            <Select.Option value={3}>3 天</Select.Option>
            <Select.Option value={7}>7 天</Select.Option>
            <Select.Option value={15}>15 天</Select.Option>
            <Select.Option value={30}>30 天</Select.Option>
        </Select>
        <p className="text-xs text-gray-500 mt-2">只显示最近 {retentionPeriod} 天内的预警。</p>
    </div>
  );

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 动态背景层 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px',
            animation: 'grid-move 20s linear infinite'
          }}></div>
        </div>
        
        {/* 动态粒子效果 */}
        <div className="absolute inset-0">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `particle-float ${3 + Math.random() * 4}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            ></div>
          ))}
        </div>
        
        {/* 光效背景 */}
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 right-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-1/3 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>
      
      <style>{`
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(139, 92, 246, 0.5) rgba(15, 23, 42, 0.1);
        }
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(15, 23, 42, 0.1);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(139, 92, 246, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(139, 92, 246, 0.7);
        }
        
        .gradient-border {
          background: linear-gradient(45deg, #f56565, #ed8936, #ecc94b, #48bb78, #38b2ac, #4299e1, #667eea, #9f7aea);
          background-size: 400% 400%;
          animation: gradient 3s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        .pulse-ring {
          animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        @keyframes pulse-ring {
          0% { transform: scale(0.33); }
          40%, 50% { opacity: 0; }
          100% { opacity: 0; transform: scale(1.2); }
        }
        
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(20px, 20px); }
        }
        
        @keyframes particle-float {
          0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
          50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
        }
        
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        
        .animate-blob {
          animation: blob 7s infinite;
        }
        
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        
        .animation-delay-4000 {
          animation-delay: 4s;
        }
        
        .glass-morphism {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .neon-glow {
          box-shadow: 
            0 0 5px rgba(99, 102, 241, 0.5),
            0 0 20px rgba(99, 102, 241, 0.3),
            0 0 35px rgba(99, 102, 241, 0.2),
            0 0 50px rgba(99, 102, 241, 0.1);
        }
        
        .cyber-border {
          position: relative;
          background: linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.1) 50%, transparent 70%);
          background-size: 200% 200%;
          animation: cyber-scan 3s linear infinite;
        }
        
        @keyframes cyber-scan {
          0% { background-position: 0% 0%; }
          100% { background-position: 200% 200%; }
        }
        
        .hologram-effect {
          background: linear-gradient(
            45deg,
            rgba(0, 255, 255, 0.1) 0%,
            rgba(255, 0, 255, 0.1) 25%,
            rgba(255, 255, 0, 0.1) 50%,
            rgba(0, 255, 255, 0.1) 75%,
            rgba(255, 0, 255, 0.1) 100%
          );
          background-size: 400% 400%;
          animation: hologram 4s ease-in-out infinite;
        }
        
        @keyframes hologram {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        
        .data-stream {
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(59, 130, 246, 0.5) 50%,
            transparent 100%
          );
          background-size: 200% 100%;
          animation: data-flow 2s linear infinite;
        }
        
        @keyframes data-flow {
          0% { background-position: -200% 0%; }
          100% { background-position: 200% 0%; }
        }
        
        .breathing-glow {
          animation: breathing 2s ease-in-out infinite;
        }
        
        @keyframes breathing {
          0%, 100% { 
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
            transform: scale(1);
          }
          50% { 
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
            transform: scale(1.05);
          }
        }
      `}</style>
      {/* 相对定位的内容层 */}
      <div className="relative z-10">
        <UnifiedPageHeader 
          title="实时预警中心"
          subtitle="实时监控高风险通话，实现主动式服务质量管理与干预"
          icon={Siren}
          actions={[
            // {
            //   label: '预警规则配置',
            //   onClick: handleRuleConfig,
            //   variant: 'outline'
            // },
            {
              label: '接收预警',
              onClick: () => setAlertsEnabled(!alertsEnabled),
              type: 'switch',
              checked: alertsEnabled,
            }
          ]}
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* KPI指标卡片 - 炫酷版 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 0.1, type: "spring", stiffness: 100 }}
            whileHover={{ y: -10, scale: 1.05 }}
            className="group"
          >
            <Card className="relative overflow-hidden border-0 shadow-2xl hover:shadow-red-500/20 transition-all duration-500 glass-morphism cyber-border">
              {/* 全息背景效果 */}
              <div className="absolute inset-0 hologram-effect opacity-30"></div>
              
              {/* 数据流效果 */}
              <div className="absolute top-0 left-0 w-full h-1 data-stream"></div>
              
              {/* 主要内容 */}
              <div className="relative z-10 p-6">
                <div className="flex items-center justify-between mb-4">
                  <motion.div 
                    className="p-4 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl shadow-2xl neon-glow group-hover:shadow-red-500/50"
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <AlertTriangle className="h-8 w-8 text-white drop-shadow-lg" />
                  </motion.div>
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: "spring" }}
                  >
                    <Badge count={kpiMetrics.totalAlerts > 20 ? '高' : '正常'} 
                           className={kpiMetrics.totalAlerts > 20 ? 'breathing-glow' : ''}
                           style={{ 
                             backgroundColor: kpiMetrics.totalAlerts > 20 ? '#ff4d4f' : '#52c41a',
                             boxShadow: kpiMetrics.totalAlerts > 20 ? '0 0 15px rgba(255, 77, 79, 0.6)' : '0 0 15px rgba(82, 196, 26, 0.6)'
                           }} />
                  </motion.div>
                </div>
                <motion.div 
                  className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  {kpiMetrics.totalAlerts}
                </motion.div>
                <div className="text-sm text-gray-300 font-medium mb-3">今日预警总数</div>
                <motion.div 
                  className="flex items-center text-xs text-red-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <TrendingUp className="h-4 w-4 mr-2 animate-bounce" />
                  <span className="font-semibold">较昨日 +12%</span>
                </motion.div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
            whileHover={{ y: -10, scale: 1.05 }}
            className="group"
          >
            <Card className="relative overflow-hidden border-0 shadow-2xl hover:shadow-orange-500/20 transition-all duration-500 glass-morphism cyber-border">
              {/* 全息背景效果 */}
              <div className="absolute inset-0 hologram-effect opacity-30"></div>
              
              {/* 数据流效果 */}
              <div className="absolute top-0 left-0 w-full h-1 data-stream" style={{ animationDelay: '0.5s' }}></div>
              
              {/* 主要内容 */}
              <div className="relative z-10 p-6">
                <div className="flex items-center justify-between mb-4">
                  <motion.div 
                    className="p-4 bg-gradient-to-br from-orange-500 to-amber-600 rounded-2xl shadow-2xl neon-glow group-hover:shadow-orange-500/50"
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Clock className="h-8 w-8 text-white drop-shadow-lg" />
                  </motion.div>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Heart className="h-6 w-6 text-orange-400 drop-shadow-lg" />
                  </motion.div>
                </div>
                <motion.div 
                  className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  {kpiMetrics.pendingAlerts}
                </motion.div>
                <div className="text-sm text-gray-300 font-medium mb-3">待处理预警</div>
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ delay: 0.5, duration: 1 }}
                  className="bg-gray-700 rounded-full h-2 overflow-hidden"
                >
                  <motion.div
                    className="h-full bg-gradient-to-r from-orange-400 to-amber-400 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.round((kpiMetrics.pendingAlerts / kpiMetrics.totalAlerts) * 100)}%` }}
                    transition={{ delay: 0.7, duration: 1.5, ease: "easeOut" }}
                  />
                </motion.div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 100 }}
            whileHover={{ y: -10, scale: 1.05 }}
            className="group"
          >
            <Card className="relative overflow-hidden border-0 shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 glass-morphism cyber-border">
              {/* 全息背景效果 */}
              <div className="absolute inset-0 hologram-effect opacity-30"></div>
              
              {/* 数据流效果 */}
              <div className="absolute top-0 left-0 w-full h-1 data-stream" style={{ animationDelay: '1s' }}></div>
              
              {/* 主要内容 */}
              <div className="relative z-10 p-6">
                <div className="flex items-center justify-between mb-4">
                  <motion.div 
                    className="p-4 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl shadow-2xl neon-glow group-hover:shadow-purple-500/50"
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Shield className="h-8 w-8 text-white drop-shadow-lg" />
                  </motion.div>
                  <motion.div 
                    className={`px-3 py-2 rounded-full text-xs font-bold border-2 ${
                      kpiMetrics.highestRiskLevel === 'critical' ? 'bg-red-500/20 text-red-300 border-red-500' :
                      kpiMetrics.highestRiskLevel === 'high' ? 'bg-orange-500/20 text-orange-300 border-orange-500' :
                      'bg-yellow-500/20 text-yellow-300 border-yellow-500'
                    }`}
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.4, type: "spring" }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {getAlertLevelText(kpiMetrics.highestRiskLevel)}
                  </motion.div>
                </div>
                <motion.div 
                  className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  {getAlertLevelText(kpiMetrics.highestRiskLevel)}
                </motion.div>
                <div className="text-sm text-gray-300 font-medium mb-3">最高风险等级</div>
                <motion.div 
                  className="flex items-center text-xs text-purple-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Target className="h-4 w-4 mr-2 animate-pulse" />
                  <span className="font-semibold">需要重点关注</span>
                </motion.div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 0.4, type: "spring", stiffness: 100 }}
            whileHover={{ y: -10, scale: 1.05 }}
            className="group"
          >
            <Card className="relative overflow-hidden border-0 shadow-2xl hover:shadow-blue-500/20 transition-all duration-500 glass-morphism cyber-border">
              {/* 全息背景效果 */}
              <div className="absolute inset-0 hologram-effect opacity-30"></div>
              
              {/* 数据流效果 */}
              <div className="absolute top-0 left-0 w-full h-1 data-stream" style={{ animationDelay: '1.5s' }}></div>
              
              {/* 主要内容 */}
              <div className="relative z-10 p-6">
                <div className="flex items-center justify-between mb-4">
                  <motion.div 
                    className="p-4 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl shadow-2xl neon-glow group-hover:shadow-blue-500/50"
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Users className="h-8 w-8 text-white drop-shadow-lg" />
                  </motion.div>
                  <motion.div 
                    className="flex items-center bg-green-500/20 px-3 py-2 rounded-full border border-green-500"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: "spring" }}
                  >
                    <motion.div 
                      className="w-3 h-3 bg-green-400 rounded-full mr-2"
                      animate={{ scale: [1, 1.3, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    />
                    <span className="text-xs text-green-300 font-bold">在线</span>
                  </motion.div>
                </div>
                <motion.div 
                  className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  {kpiMetrics.activeCalls}
                </motion.div>
                <div className="text-sm text-gray-300 font-medium mb-3">实时通话并发数</div>
                <motion.div 
                  className="flex items-center text-xs text-blue-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Activity className="h-4 w-4 mr-2" />
                  </motion.div>
                  <span className="font-semibold">峰值: 189</span>
                </motion.div>
              </div>
            </Card>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 实时预警流 */}
          <motion.div 
            className="lg:col-span-2"
            initial={{ opacity: 0, x: -20, scale: 0.95 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            transition={{ delay: 0.5, type: "spring", stiffness: 50 }}
          >
            <Card 
              className="h-full shadow-2xl border-0 overflow-hidden relative glass-morphism cyber-border"
              bodyStyle={{ padding: '0' }}
            >
              {/* 科技背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-slate-800/50 via-purple-900/30 to-slate-800/50 pointer-events-none"></div>
              
              {/* 动态网格背景 */}
              <div className="absolute inset-0 opacity-10">
                <div 
                  className="w-full h-full"
                  style={{
                    backgroundImage: `
                      linear-gradient(rgba(99, 102, 241, 0.3) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(99, 102, 241, 0.3) 1px, transparent 1px)
                    `,
                    backgroundSize: '30px 30px',
                    animation: 'grid-move 25s linear infinite reverse'
                  }}
                />
              </div>
              
              {/* 全息光效 */}
              <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-cyan-500/20 via-purple-500/20 to-pink-500/20 rounded-full blur-xl -mr-20 -mt-20 animate-blob"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-blue-500/20 via-purple-500/20 to-indigo-500/20 rounded-full blur-xl -ml-16 -mb-16 animate-blob animation-delay-2000"></div>
              
              <div className="relative z-10 p-6">
                {/* 标题区域 */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <motion.div className="relative">
                      <motion.div 
                        className="w-16 h-16 bg-gradient-to-r from-red-500 via-pink-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-2xl neon-glow"
                        animate={{ 
                          boxShadow: [
                            "0 0 20px rgba(239, 68, 68, 0.5)",
                            "0 0 40px rgba(239, 68, 68, 0.8)",
                            "0 0 20px rgba(239, 68, 68, 0.5)"
                          ]
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                        >
                          <Zap className="h-8 w-8 text-white drop-shadow-lg" />
                        </motion.div>
                      </motion.div>
                      
                      {/* 多层脉冲环 */}
                      <motion.div 
                        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      >
                        <motion.div 
                          className="w-2 h-2 bg-white rounded-full"
                          animate={{ opacity: [1, 0, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                        />
                      </motion.div>
                      
                      {/* 外圈动画 */}
                      <motion.div 
                        className="absolute inset-0 w-16 h-16 border-2 border-red-500/30 rounded-3xl"
                        animate={{ scale: [1, 1.3, 1], opacity: [0.7, 0, 0.7] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <motion.div 
                        className="absolute inset-0 w-16 h-16 border-2 border-orange-500/30 rounded-3xl"
                        animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                        transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                      />
                    </motion.div>
                    <div>
                      <motion.h3 
                        className="text-2xl font-bold text-white flex items-center space-x-2 bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        <span>实时预警流</span>
                      </motion.h3>
                      <motion.p 
                        className="text-sm text-gray-300 font-medium flex items-center space-x-2"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <span className="text-cyan-400">智能监控</span>
                        <span className="text-gray-500">·</span>
                        <span className="text-purple-400">实时响应</span>
                        <span className="text-gray-500">·</span>
                        <span className="text-pink-400">主动干预</span>
                      </motion.p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <motion.div 
                      className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full shadow-lg border border-green-500/30 backdrop-blur-sm"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      <motion.div 
                        className="w-3 h-3 bg-green-400 rounded-full shadow-lg"
                        animate={{ 
                          scale: [1, 1.3, 1],
                          boxShadow: [
                            "0 0 5px rgba(34, 197, 94, 0.5)",
                            "0 0 15px rgba(34, 197, 94, 0.8)",
                            "0 0 5px rgba(34, 197, 94, 0.5)"
                          ]
                        }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      />
                      <span className="text-sm font-bold text-green-300">实时更新中</span>
                    </motion.div>
                    
                    <motion.div 
                      className="text-xs text-gray-400 bg-slate-800/50 px-3 py-2 rounded-xl border border-gray-700 backdrop-blur-sm"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                    >
                      最近更新: {new Date().toLocaleTimeString()}
                    </motion.div>
                    
                    <Tooltip title="刷新数据">
                      <motion.button 
                        whileHover={{ scale: 1.1, rotate: 360 }}
                        whileTap={{ scale: 0.9 }}
                        className="p-3 rounded-2xl bg-slate-800/50 hover:bg-slate-700/50 shadow-lg hover:shadow-cyan-500/20 text-cyan-400 border border-cyan-500/30 transition-all duration-300 backdrop-blur-sm"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7 }}
                      >
                        <Activity className="h-5 w-5" />
                      </motion.button>
                    </Tooltip>
                    
                    <Popover
                      content={retentionSettingsContent}
                      title="显示设置"
                      trigger="click"
                      placement="bottomRight"
                    >
                      <Tooltip title="设置">
                        <motion.button 
                          whileHover={{ scale: 1.1, rotate: 180 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-3 rounded-2xl bg-slate-800/50 hover:bg-slate-700/50 shadow-lg hover:shadow-purple-500/20 text-purple-400 border border-purple-500/30 transition-all duration-300 backdrop-blur-sm"
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.8 }}
                        >
                          <Settings className="h-5 w-5" />
                        </motion.button>
                      </Tooltip>
                    </Popover>
                  </div>
                </div>
                
                {/* 预警分类Tab - 炫酷版 */}
                <div className="flex items-center border-b border-gray-700/50 mb-6">
                  <motion.button 
                    onClick={() => setActiveTab('new')} 
                    className={`relative flex items-center gap-3 px-6 py-4 text-sm font-bold transition-all duration-300 rounded-t-2xl ${
                        activeTab === 'new' 
                        ? 'text-cyan-300 bg-gradient-to-r from-red-500/20 to-orange-500/20 border-b-2 border-cyan-400' 
                        : 'text-gray-400 hover:text-white hover:bg-slate-800/30'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {activeTab === 'new' && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-red-500/10 via-orange-500/10 to-red-500/10 rounded-t-2xl"
                        animate={{ opacity: [0.3, 0.6, 0.3] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    )}
                    <span className="relative z-10">新预警</span>
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="relative z-10"
                    >
                      <Badge 
                        count={alerts.filter(a => a.status === 'unread' || a.status === 'processing').length} 
                        style={{ 
                          backgroundColor: '#ef4444',
                          boxShadow: '0 0 10px rgba(239, 68, 68, 0.6)',
                          border: '1px solid rgba(239, 68, 68, 0.3)'
                        }} 
                        className="breathing-glow"
                      />
                    </motion.div>
                  </motion.button>
                  
                  <motion.button 
                    onClick={() => setActiveTab('processed')} 
                    className={`relative flex items-center gap-3 px-6 py-4 text-sm font-bold transition-all duration-300 rounded-t-2xl ${
                        activeTab === 'processed' 
                        ? 'text-green-300 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-b-2 border-green-400' 
                        : 'text-gray-400 hover:text-white hover:bg-slate-800/30'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {activeTab === 'processed' && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-green-500/10 via-emerald-500/10 to-green-500/10 rounded-t-2xl"
                        animate={{ opacity: [0.3, 0.6, 0.3] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    )}
                    <span className="relative z-10">已处置</span>
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="relative z-10"
                    >
                      <Badge 
                        count={alerts.filter(a => a.status === 'read' || a.status === 'resolved').length} 
                        style={{ 
                          backgroundColor: '#22c55e',
                          boxShadow: '0 0 10px rgba(34, 197, 94, 0.6)',
                          border: '1px solid rgba(34, 197, 94, 0.3)'
                        }} 
                      />
                    </motion.div>
                  </motion.button>
                </div>
                
                {/* 预警列表 */}
                <div className="space-y-4 max-h-[800px] overflow-y-auto custom-scrollbar pr-2 pt-4">
                  <AnimatePresence>
                    {filteredAlerts.map((alert, index) => (
                      <motion.div 
                        key={alert.id} 
                        initial={{ opacity: 0, y: 30, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -30, scale: 0.95, height: 0 }}
                        transition={{ 
                          duration: 0.4, 
                          delay: index * 0.1,
                          type: "spring",
                          stiffness: 100
                        }}
                        whileHover={{ scale: 1.02, y: -4 }}
                        className={`relative p-6 rounded-2xl border-l-4 overflow-hidden cursor-pointer group ${
                          alert.status === 'unread' 
                            ? 'bg-gradient-to-r from-red-50 via-red-25 to-white border-red-400 shadow-lg hover:shadow-xl' 
                            : 'bg-gradient-to-r from-white to-gray-50 border-gray-300 shadow-md hover:shadow-lg'
                        } transition-all duration-300`}
                      >
                        {/* 状态指示器 */}
                        {alert.status === 'unread' && (
                          <div className="absolute top-4 right-4">
                            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse shadow-lg"></div>
                            <div className="absolute inset-0 w-3 h-3 bg-red-500/30 rounded-full animate-ping"></div>
                          </div>
                        )}
                        
                        <div className="relative z-10">
                          {/* 头部信息 */}
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center space-x-4">
                              <motion.div 
                                className="flex items-center justify-center w-12 h-12 rounded-2xl shadow-md"
                                style={{
                                  background: alert.level === 'critical' ? 'linear-gradient(135deg, #ef4444, #dc2626)' :
                                             alert.level === 'high' ? 'linear-gradient(135deg, #f97316, #ea580c)' :
                                             alert.level === 'medium' ? 'linear-gradient(135deg, #eab308, #ca8a04)' :
                                             'linear-gradient(135deg, #3b82f6, #2563eb)'
                                }}
                                whileHover={{ scale: 1.1, rotate: 5 }}
                              >
                                <AlertTriangle className="h-6 w-6 text-white" />
                              </motion.div>
                              <div>
                                <div className="flex items-center space-x-3 mb-2">
                                  <span className="text-sm font-bold text-gray-900 bg-gray-100 px-2 py-1 rounded-lg">
                                    {alert.timestamp}
                                  </span>
                                  <motion.span 
                                    className={`px-3 py-1 text-xs font-bold rounded-full border-2 ${getAlertLevelStyle(alert.level)} shadow-sm`}
                                    whileHover={{ scale: 1.05 }}
                                  >
                                    {getAlertLevelText(alert.level)}
                                  </motion.span>
                                  <motion.span 
                                    className={`px-3 py-1 text-xs font-bold rounded-full ${getStatusStyle(alert.status)} shadow-sm`}
                                    whileHover={{ scale: 1.05 }}
                                  >
                                    {getStatusText(alert.status)}
                                  </motion.span>
                                </div>
                                <div className="font-bold text-gray-900 text-lg">{alert.ruleName}</div>
                              </div>
                            </div>
                          </div>
                          
                          {/* 人员信息 */}
                          <div className="mb-4">
                            <div className="flex items-center justify-between text-sm">
                              <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-xl">
                                <Users className="h-4 w-4 text-blue-500" />
                                <span className="font-semibold text-blue-700">坐席：{alert.agentName}</span>
                                <span className="text-xs text-blue-500 bg-blue-100 px-2 py-1 rounded-full">({alert.teamName})</span>
                              </div>
                              <div className="flex items-center space-x-2 bg-purple-50 px-3 py-2 rounded-xl">
                                <span className="font-semibold text-purple-700">客户：{alert.customerPhone}</span>
                              </div>
                            </div>
                          </div>
                          
                          {/* 对话内容 */}
                          <div className="mb-5">
                            <motion.div 
                              className="text-sm text-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-2xl border border-gray-200 relative shadow-inner"
                              whileHover={{ scale: 1.01 }}
                            >
                              <div className="absolute top-0 left-6 w-4 h-4 bg-gray-50 border-t border-l border-gray-200 transform -translate-y-2 rotate-45"></div>
                              <div className="leading-relaxed">{alert.contextSnippet.map(l => l.text).join(' ')}</div>
                            </motion.div>
                          </div>
                          
                          {/* 操作按钮 */}
                          <div className="flex flex-wrap gap-3">
                            <Tooltip title="将预警标记为已读">
                              <motion.button 
                                onClick={() => handleAlertAction(alert.id, 'mark-read')}
                                className="inline-flex items-center px-4 py-2 text-xs font-semibold text-gray-700 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-sm hover:shadow-md"
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                标记已读
                              </motion.button>
                            </Tooltip>
                            <Tooltip title="标记为处理中状态">
                              <motion.button 
                                onClick={() => handleAlertAction(alert.id, 'mark-processing')}
                                className="inline-flex items-center px-4 py-2 text-xs font-semibold text-blue-700 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl hover:from-blue-200 hover:to-blue-300 transition-all duration-200 shadow-sm hover:shadow-md"
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                标记处理中
                              </motion.button>
                            </Tooltip>
                            <Tooltip title="创建跟进任务以便后续处理">
                              <motion.button 
                                onClick={() => handleAlertAction(alert.id, 'create-task')}
                                className="inline-flex items-center px-4 py-2 text-xs font-semibold text-purple-700 bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl hover:from-purple-200 hover:to-purple-300 transition-all duration-200 shadow-sm hover:shadow-md"
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <UserPlus className="h-4 w-4 mr-2" />
                                创建跟进任务
                              </motion.button>
                            </Tooltip>
                            <Tooltip title="实时监听当前通话">
                              <motion.button 
                                onClick={() => handleAlertAction(alert.id, 'real-time-listen')}
                                className="inline-flex items-center px-4 py-2 text-xs font-semibold text-green-700 bg-gradient-to-r from-green-100 to-green-200 rounded-xl hover:from-green-200 hover:to-green-300 transition-all duration-200 shadow-sm hover:shadow-md"
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <Volume2 className="h-4 w-4 mr-2" />
                                实时监听
                              </motion.button>
                            </Tooltip>
                            <Tooltip title="查看详细信息">
                              <motion.button 
                                onClick={() => handleAlertAction(alert.id, 'view-details')}
                                className="inline-flex items-center px-4 py-2 text-xs font-semibold text-indigo-700 bg-gradient-to-r from-indigo-100 to-indigo-200 rounded-xl hover:from-indigo-200 hover:to-indigo-300 transition-all duration-200 shadow-sm hover:shadow-md"
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <ExternalLink className="h-4 w-4 mr-2" />
                                查看详情
                              </motion.button>
                            </Tooltip>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                  
                  {/* 空状态 */}
                  {filteredAlerts.length === 0 && (
                    <motion.div 
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="flex flex-col items-center justify-center h-64 text-gray-400"
                    >
                      <motion.div
                        animate={{ 
                          scale: [1, 1.1, 1],
                          rotate: [0, 5, -5, 0]
                        }}
                        transition={{ 
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <Shield className="h-20 w-20 mb-4 text-green-400" />
                      </motion.div>
                      <p className="text-xl font-bold text-gray-600 mb-2">暂无预警事件</p>
                      <p className="text-sm text-gray-500">
                        {activeTab === 'new' ? '系统运行正常，所有通话质量良好' : '暂无已处置的预警记录'}
                      </p>
                      <div className="mt-4 flex items-center space-x-2 text-green-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">实时监控中</span>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>

          {/* 右侧面板 */}
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, x: 20, scale: 0.95 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            transition={{ delay: 0.6, type: "spring", stiffness: 50 }}
          >
            {/* 预警规则热力图 */}
            <Card className="shadow-2xl border-0 glass-morphism cyber-border overflow-hidden relative">
              {/* 科技背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-slate-800/50 via-orange-900/20 to-slate-800/50 pointer-events-none"></div>
              
              {/* 动态光效 */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 via-red-500/20 to-pink-500/20 rounded-full blur-xl -mr-16 -mt-16 animate-blob"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-500/20 via-indigo-500/20 to-blue-500/20 rounded-full blur-xl -ml-12 -mb-12 animate-blob animation-delay-2000"></div>
              
              <div className="relative z-10 p-6">
                <motion.div 
                  className="flex items-center justify-between mb-6"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <div className="flex items-center space-x-4">
                    <motion.div 
                      className="w-14 h-14 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-2xl neon-glow"
                      whileHover={{ scale: 1.1, rotate: 10 }}
                      animate={{ 
                        boxShadow: [
                          "0 0 20px rgba(249, 115, 22, 0.5)",
                          "0 0 40px rgba(239, 68, 68, 0.8)",
                          "0 0 20px rgba(249, 115, 22, 0.5)"
                        ]
                      }}
                      transition={{ duration: 3, repeat: Infinity }}
                    >
                      <motion.div
                        animate={{ rotate: [0, 180, 360] }}
                        transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                      >
                        <Target className="h-7 w-7 text-white drop-shadow-lg" />
                      </motion.div>
                    </motion.div>
                    <div>
                      <motion.h3 
                        className="text-xl font-bold text-white bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8 }}
                      >
                        预警规则热力图
                      </motion.h3>
                      <motion.p 
                        className="text-sm text-gray-300 font-medium"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.9 }}
                      >
                        规则触发频率监控
                      </motion.p>
                    </div>
                  </div>
                </motion.div>
                
                <div className="space-y-4">
                  {ruleHeatmap.map((item, index) => (
                    <motion.div 
                      key={index} 
                      className="relative p-4 rounded-2xl border-2 cursor-pointer transition-all duration-300 overflow-hidden group bg-gradient-to-br from-white to-gray-50 border-gray-200 hover:from-gray-50 hover:to-gray-100 shadow-lg hover:shadow-xl"
                      initial={{ opacity: 0, x: 20, scale: 0.95 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      transition={{ delay: 0.7 + index * 0.1, type: "spring" }}
                      whileHover={{ scale: 1.02, y: -2 }}
                    >
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-3">
                          <Tooltip title={`触发次数: ${item.triggerCount}, 占比: ${item.percentage}%`}>
                            <div className="text-sm font-bold text-gray-900 truncate max-w-[180px]">{item.ruleName}</div>
                          </Tooltip>
                          <motion.div
                            whileHover={{ scale: 1.1 }}
                            className={`px-3 py-1 text-xs font-bold rounded-full ${
                              index === 0 ? 'text-red-700 bg-red-100' :
                              index === 1 ? 'text-orange-700 bg-orange-100' :
                              'text-yellow-700 bg-yellow-100'
                            }`}
                          >
                            {item.triggerCount}
                          </motion.div>
                        </div>
                        <div className="mb-3">
                          <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                            <motion.div 
                              className={`h-3 rounded-full ${
                                index === 0 ? 'bg-gradient-to-r from-red-400 to-red-500' :
                                index === 1 ? 'bg-gradient-to-r from-orange-400 to-orange-500' :
                                'bg-gradient-to-r from-yellow-400 to-yellow-500'
                              }`}
                              initial={{ width: 0 }}
                              animate={{ width: `${item.percentage}%` }}
                              transition={{ duration: 1.2, delay: 0.8 + index * 0.1 }}
                            ></motion.div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="text-xs font-semibold text-gray-600">触发率: {item.percentage}%</div>
                          <div className={`text-xs font-bold px-2 py-1 rounded-full ${
                            index === 0 ? 'text-red-700 bg-red-200' :
                            index === 1 ? 'text-orange-700 bg-orange-200' :
                            'text-yellow-700 bg-yellow-200'
                          }`}>
                            {index === 0 ? '高频' : index === 1 ? '中频' : '低频'}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </Card>
          </motion.div>
                </div>
                
        {selectedAlert && (
          <Drawer
            title="创建跟进任务"
            width={720}
            onClose={() => {
              setIsTaskDrawerVisible(false);
              setSelectedAlert(null);
            }}
            open={isTaskDrawerVisible}
            bodyStyle={{ paddingBottom: 80 }}
            destroyOnClose
            footer={
              <div style={{ textAlign: 'right' }}>
                <Button onClick={() => {
                  setIsTaskDrawerVisible(false);
                  setSelectedAlert(null);
                }} style={{ marginRight: 8 }}>
                  取消
                </Button>
                <Button form="followUpTaskForm" type="primary" htmlType="submit">
                  创建任务
                </Button>
                    </div>
            }
          >
            <Form layout="vertical" id="followUpTaskForm" onFinish={handleTaskFormSubmit}>
              <Form.Item name="title" label="任务标题" rules={[{ required: true, message: '请输入任务标题' }]}>
                <Input />
              </Form.Item>
              <Form.Item name="description" label="任务描述">
                <Input.TextArea rows={4} />
              </Form.Item>
              <Form.Item name="assignee" label="指派给" rules={[{ required: true, message: '请选择负责人' }]}>
                <Select placeholder="请选择负责人">
                  {mockAssignees.map(a => (
                    <Select.Option key={a.name} value={a.name}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar src={a.avatar} size="small" style={{ marginRight: 8 }}/>
                        {a.name}
                    </div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="priority" label="优先级" rules={[{ required: true, message: '请选择优先级' }]}>
                <Select placeholder="请选择优先级">
                  <Select.Option value="high">高</Select.Option>
                  <Select.Option value="medium">中</Select.Option>
                  <Select.Option value="low">低</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="dueDate" label="截止日期" rules={[{ required: true, message: '请选择截止日期' }]}>
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Form>
          </Drawer>
        )}

        <AlertDetailDrawer
          alert={selectedAlertForDetail}
          visible={isDetailDrawerVisible}
          onClose={() => setIsDetailDrawerVisible(false)}
          onAction={handleDrawerAction}
          isReadOnly={false}
        />
        </div>
      </div>
    </div>
  );
};

export default RealTimeAlertCenterPage;