import React from 'react';

/**
 * 文本相似度使用提示组件
 */
const TextSimilarityTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">模板设计</span>
                    <span className="text-xs text-gray-500 block">使用标准、规范的表述，避免口语化和冗余</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">阈值设置</span>
                    <span className="text-xs text-gray-500 block">建议80%相似度，过高过低都会影响检测效果</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 高级功能提示 */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">高级功能</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">单句话内生效</span>
                    <span className="text-xs text-gray-500 block">适用于长句中包含多个语义片段的场景</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">多模板配置</span>
                    <span className="text-xs text-gray-500 block">覆盖不同表述方式，提高匹配成功率</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">检测范围</span>
                    <span className="text-xs text-gray-500 block">针对特定对话位置进行精准检测</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">模板质量</span>
                    <span className="text-xs text-gray-500 block">确保模板语句简洁明确，一个意思一个模板</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">测试验证</span>
                    <span className="text-xs text-gray-500 block">使用真实对话数据进行测试，调整配置参数</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">性能考虑</span>
                    <span className="text-xs text-gray-500 block">模板过多会影响性能，建议控制在合理范围内</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速开始 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-sm">🚀</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">步骤1</span>
                <span>选择案例</span>
                <span>→</span>
                <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">步骤2</span>
                <span>点击加载</span>
                <span>→</span>
                <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">步骤3</span>
                <span>执行测试</span>
              </div>
            </div>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg p-4 border border-yellow-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-sm">❓</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">常见问题</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">相似度过低</span>
                    <span className="text-xs text-gray-500 block">检查模板是否过于具体，尝试使用更通用的表述</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">误检过多</span>
                    <span className="text-xs text-gray-500 block">适当提高相似度阈值，或优化模板精确度</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">检测不到</span>
                    <span className="text-xs text-gray-500 block">检查角色和范围配置，确认目标文本在检测范围内</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextSimilarityTips; 