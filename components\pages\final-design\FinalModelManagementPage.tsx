import React, { useState } from 'react';
import { Card, Button, Typography, Space, Modal, Drawer, Form, Input, Select, Switch, Tag, Tooltip, message } from 'antd';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Settings, 
  Cloud, 
  Server, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Cpu,
  Globe,
  PowerOff,
  Power
} from 'lucide-react';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';

const { Title } = Typography;
const { Option } = Select;

// 模型类型定义
interface ModelConfig {
  id: string;
  name: string;
  type: 'cloud' | 'local';
  provider: 'deepseek' | 'volcengine' | 'alibaba' | 'siliconflow' | 'ollama';
  model: string;
  apiKey?: string;
  endpoint?: string;
  status: 'active' | 'inactive' | 'error';
  description?: string;
  maxTokens?: number;
  temperature?: number;
  createdAt: string;
  lastUsed?: string;
}

// 模拟数据
const mockModels: ModelConfig[] = [
  {
    id: '1',
    name: 'DeepSeek Chat',
    type: 'cloud',
    provider: 'deepseek',
    model: 'deepseek-chat',
    apiKey: 'sk-*********************',
    endpoint: 'https://api.deepseek.com',
    status: 'active',
    description: '用于智能质检规则生成和对话分析',
    maxTokens: 4096,
    temperature: 0.7,
    createdAt: '2024-01-15',
    lastUsed: '2024-01-21'
  },
  {
    id: '2',
    name: '火山引擎豆包',
    type: 'cloud',
    provider: 'volcengine',
    model: 'doubao-pro-4k',
    apiKey: 'ak-*********************',
    endpoint: 'https://ark.cn-beijing.volces.com',
    status: 'active',
    description: '用于文本分析和情感识别',
    maxTokens: 4096,
    temperature: 0.5,
    createdAt: '2024-01-10',
    lastUsed: '2024-01-20'
  },
  {
    id: '3',
    name: 'Qwen-Plus',
    type: 'cloud',
    provider: 'alibaba',
    model: 'qwen-plus',
    apiKey: 'sk-*********************',
    endpoint: 'https://dashscope.aliyuncs.com',
    status: 'inactive',
    description: '阿里云通义千问模型',
    maxTokens: 8192,
    temperature: 0.8,
    createdAt: '2024-01-08',
    lastUsed: '2024-01-18'
  },
  {
    id: '4',
    name: 'Llama3-8B',
    type: 'local',
    provider: 'ollama',
    model: 'llama3:8b',
    endpoint: 'http://localhost:11434',
    status: 'active',
    description: '本地部署的Llama3模型',
    maxTokens: 2048,
    temperature: 0.6,
    createdAt: '2024-01-12',
    lastUsed: '2024-01-21'
  }
];

const FinalModelManagementPage: React.FC = () => {
  const [models, setModels] = useState<ModelConfig[]>(mockModels);
  const [filteredModels, setFilteredModels] = useState<ModelConfig[]>(mockModels);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingModel, setEditingModel] = useState<ModelConfig | null>(null);
  const [form] = Form.useForm();
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  // 云端服务商配置
  const cloudProviders = [
    { value: 'deepseek', label: 'DeepSeek', icon: '🧠' },
    { value: 'volcengine', label: '火山引擎', icon: '🌋' },
    { value: 'alibaba', label: '阿里云', icon: '☁️' },
    { value: 'siliconflow', label: '硅基流动', icon: '💎' }
  ];

  // 本地服务商配置
  const localProviders = [
    { value: 'ollama', label: 'Ollama', icon: '🦙' }
  ];

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'green', icon: CheckCircle, text: '运行中' },
      inactive: { color: 'orange', icon: AlertTriangle, text: '未启用' },
      error: { color: 'red', icon: XCircle, text: '错误' }
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    const IconComponent = config.icon;
    
    return (
      <div className="flex items-center space-x-1 whitespace-nowrap">
        <Tag color={config.color} icon={<IconComponent className="w-3 h-3" />} className="flex items-center space-x-1">
          {config.text}
        </Tag>
      </div>
    );
  };

  // 获取提供商图标
  const getProviderIcon = (provider: string, type: string) => {
    if (type === 'cloud') {
      return <Cloud className="w-4 h-4 text-blue-500" />;
    }
    return <Server className="w-4 h-4 text-green-500" />;
  };

  // 切换API Key显示
  const toggleApiKeyVisibility = (modelId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  // 格式化API Key显示
  const formatApiKey = (apiKey: string | undefined, modelId: string) => {
    if (!apiKey) return '-';
    if (showApiKey[modelId]) {
      return apiKey;
    }
    return apiKey.substring(0, 8) + '*'.repeat(apiKey.length - 8);
  };

  // 打开新增/编辑模态框
  const openModal = (model?: ModelConfig) => {
    setEditingModel(model || null);
    if (model) {
      form.setFieldsValue(model);
    } else {
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  // 关闭模态框
  const closeModal = () => {
    setIsModalVisible(false);
    setEditingModel(null);
    form.resetFields();
  };

  // 保存模型配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const newModel: ModelConfig = {
        ...values,
        id: editingModel?.id || Date.now().toString(),
        createdAt: editingModel?.createdAt || new Date().toISOString().split('T')[0],
        status: values.status || 'inactive'
      };

      let updatedModels;
      if (editingModel) {
        updatedModels = models.map(m => m.id === editingModel.id ? newModel : m);
        message.success('模型配置更新成功');
      } else {
        updatedModels = [...models, newModel];
        message.success('模型配置添加成功');
      }
      setModels(updatedModels);
      applyFilters(updatedModels, filters);
      closeModal();
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 删除模型
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个模型配置吗？此操作不可恢复。',
      onOk: () => {
        const updatedModels = models.filter(m => m.id !== id);
        setModels(updatedModels);
        applyFilters(updatedModels, filters);
        message.success('模型配置删除成功');
      }
    });
  };

  // 切换模型状态
  const toggleModelStatus = (id: string) => {
    const updatedModels = models.map(m => 
      m.id === id 
        ? { ...m, status: m.status === 'active' ? 'inactive' : 'active' as any }
        : m
    );
    setModels(updatedModels);
    applyFilters(updatedModels, filters);
  };

  // 测试连接
  const testConnection = async (model: ModelConfig) => {
    message.loading('正在测试连接...', 0);
    // 模拟测试连接
    setTimeout(() => {
      message.destroy();
      const updatedModels = models.map(m => 
        m.id === model.id 
          ? { ...m, status: Math.random() > 0.3 ? 'active' : 'error' as any }
          : m
      );
      setModels(updatedModels);
      applyFilters(updatedModels, filters);
      
      if (Math.random() > 0.3) {
        message.success('连接测试成功');
      } else {
        message.error('连接测试失败，请检查配置');
      }
    }, 2000);
  };

  // 筛选字段配置
  const filterFields: FilterField[] = [
    {
      key: 'name',
      label: '模型名称',
      type: 'text',
      placeholder: '请输入模型名称',
      width: 'w-full'
    },
    {
      key: 'type',
      label: '模型类型',
      type: 'select',
      options: [
        { value: 'cloud', label: '云端模型' },
        { value: 'local', label: '本地模型' }
      ],
      width: 'w-full'
    },
    {
      key: 'provider',
      label: '服务商',
      type: 'select',
      options: [
        { value: 'deepseek', label: 'DeepSeek' },
        { value: 'volcengine', label: '火山引擎' },
        { value: 'alibaba', label: '阿里云' },
        { value: 'siliconflow', label: '硅基流动' },
        { value: 'ollama', label: 'Ollama' }
      ],
      width: 'w-full'
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      options: [
        { value: 'active', label: '运行中' },
        { value: 'inactive', label: '未启用' },
        { value: 'error', label: '错误' }
      ],
      width: 'w-full'
    }
  ];

  // 应用筛选条件
  const applyFilters = (modelList: ModelConfig[], filterValues: Record<string, any>) => {
    let filtered = [...modelList];

    // 按名称筛选
    if (filterValues.name) {
      filtered = filtered.filter(model => 
        model.name.toLowerCase().includes(filterValues.name.toLowerCase()) ||
        model.model.toLowerCase().includes(filterValues.name.toLowerCase())
      );
    }

    // 按类型筛选
    if (filterValues.type) {
      filtered = filtered.filter(model => model.type === filterValues.type);
    }

    // 按服务商筛选
    if (filterValues.provider) {
      filtered = filtered.filter(model => model.provider === filterValues.provider);
    }

    // 按状态筛选
    if (filterValues.status) {
      filtered = filtered.filter(model => model.status === filterValues.status);
    }

    setFilteredModels(filtered);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理筛选条件变化
  const handleFiltersChange = (newFilters: Record<string, any>) => {
    setFilters(newFilters);
  };

  // 执行搜索
  const handleSearch = () => {
    applyFilters(models, filters);
  };

  // 重置筛选条件
  const handleReset = () => {
    setFilters({});
    setFilteredModels(models);
    setCurrentPage(1);
  };

  // 获取当前页数据
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredModels.slice(startIndex, endIndex);
  };

  // 渲染模型表格行
  const renderModelRow = (model: ModelConfig, index: number) => {
    const providerMap: Record<string, string> = {
      deepseek: 'DeepSeek',
      volcengine: '火山引擎',
      alibaba: '阿里云',
      siliconflow: '硅基流动',
      ollama: 'Ollama'
    };

    return (
      <tr key={model.id} className="hover:bg-gray-50">
        {/* 序号 */}
        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
          {(currentPage - 1) * pageSize + index + 1}
        </td>
        
        {/* 模型名称 */}
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center space-x-3">
            {getProviderIcon(model.provider, model.type)}
            <div>
              <div className="text-sm font-medium text-gray-900">{model.name}</div>
              <div className="text-sm text-gray-500">{model.model}</div>
            </div>
          </div>
        </td>
        
        {/* 类型 */}
        <td className="px-6 py-4 whitespace-nowrap">
          <Tag color={model.type === 'cloud' ? 'blue' : 'green'}>
            {model.type === 'cloud' ? '云端' : '本地'}
          </Tag>
        </td>
        
        {/* 服务商 */}
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {providerMap[model.provider] || model.provider}
        </td>
        
        {/* API Key */}
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center space-x-2">
            <span className="font-mono text-sm text-gray-600">
              {formatApiKey(model.apiKey, model.id)}
            </span>
            {model.apiKey && (
              <button
                onClick={() => toggleApiKeyVisibility(model.id)}
                className="text-gray-400 hover:text-gray-600"
              >
                {showApiKey[model.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            )}
          </div>
        </td>
        
        {/* 状态 */}
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center justify-start">
            {getStatusTag(model.status)}
          </div>
        </td>
        
        {/* 最后使用 */}
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {model.lastUsed || '-'}
        </td>
        
        {/* 操作 */}
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center justify-end space-x-2">
            <Tooltip title="编辑">
              <button
                onClick={() => openModal(model)}
                className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
              </button>
            </Tooltip>
            <Tooltip title="测试连接">
              <button
                onClick={() => testConnection(model)}
                className="text-green-600 hover:text-green-900 p-1.5 hover:bg-green-50 rounded-lg transition-colors"
              >
                <Globe className="w-4 h-4" />
              </button>
            </Tooltip>
            <Tooltip title={model.status === 'active' ? '禁用' : '启用'}>
              <button
                onClick={() => toggleModelStatus(model.id)}
                className={`p-1.5 rounded-lg transition-colors mx-1 ${
                  model.status === 'active' 
                    ? 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50' 
                    : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                }`}
              >
                {model.status === 'active' ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
              </button>
            </Tooltip>
            <Tooltip title="删除">
              <button
                onClick={() => handleDelete(model.id)}
                className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </Tooltip>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <UnifiedPageHeader
        title="大模型管理"
        subtitle="配置和管理云端及本地大语言模型"
        icon={Cpu}
        showDesignGuide={false}
        designGuideContent={
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">页面概述</h3>
              <p className="text-gray-700 leading-relaxed">
                大模型管理页面用于配置和管理智能质检系统中使用的大语言模型，支持云端模型和本地模型的统一管理。
                通过此页面可以添加、编辑、测试和监控各种模型的运行状态。
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">支持的模型</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>云端模型：</strong>DeepSeek、火山引擎豆包、阿里云通义千问、硅基流动等主流大模型服务</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>本地模型：</strong>通过Ollama部署的开源模型，如Llama、Qwen、ChatGLM等</span>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">主要功能</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>模型配置：</strong>添加和编辑模型的基本信息、API密钥、端点地址等配置</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>连接测试：</strong>验证模型配置的正确性和可用性</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>状态管理：</strong>启用/停用模型，监控模型运行状态</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>安全保护：</strong>API密钥加密显示，防止敏感信息泄露</span>
                </li>
              </ul>
            </div>
          </div>
        }
        actions={[
          {
            label: "添加模型",
            icon: Plus,
            variant: "primary",
            onClick: () => openModal()
          }
        ]}
      />

      <main className="p-6 md:p-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
        {/* 筛选区域 */}
        <UnifiedSearchFilter
          fields={filterFields}
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onSearch={handleSearch}
          onReset={handleReset}
          isExpanded={isFilterExpanded}
          onToggleExpanded={() => setIsFilterExpanded(!isFilterExpanded)}
        />

        {/* 模型列表 */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-600">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th className="px-4 py-3">序号</th>
                <th className="px-6 py-3">模型名称</th>
                <th className="px-6 py-3">类型</th>
                <th className="px-6 py-3">服务商</th>
                <th className="px-6 py-3">API Key</th>
                <th className="px-6 py-3">状态</th>
                <th className="px-6 py-3">最后使用</th>
                <th className="px-6 py-3 text-right">操作</th>
              </tr>
            </thead>
            <tbody>
              {getCurrentPageData().length > 0 ? (
                getCurrentPageData().map((model, index) => renderModelRow(model, index))
              ) : (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                    <div className="flex flex-col items-center space-y-2">
                      <Cpu className="w-12 h-12 text-gray-300" />
                      <span>暂无模型数据</span>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        
        {/* 分页 */}
        {filteredModels.length > 0 && (
          <UnifiedPagination
            current={currentPage}
            total={filteredModels.length}
            pageSize={pageSize}
            onChange={setCurrentPage}
          />
        )}
        </motion.div>
      </main>

      {/* 添加/编辑模型抽屉 */}
      <Drawer
        title={editingModel ? '编辑模型配置' : '添加模型配置'}
        open={isModalVisible}
        onClose={closeModal}
        width={600}
        destroyOnClose
        footer={
          <div className="flex justify-end space-x-2 p-4 border-t">
            <Button onClick={closeModal}>取消</Button>
            <Button type="primary" onClick={handleSave}>保存</Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'cloud',
            status: 'inactive',
            maxTokens: 4096,
            temperature: 0.7
          }}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="请输入模型名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="模型类型"
            rules={[{ required: true, message: '请选择模型类型' }]}
          >
            <Select placeholder="请选择模型类型">
              <Option value="cloud">云端模型</Option>
              <Option value="local">本地模型</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="provider"
            label="服务商"
            rules={[{ required: true, message: '请选择服务商' }]}
          >
            <Select placeholder="请选择服务商">
              <Select.OptGroup label="云端服务商">
                {cloudProviders.map(provider => (
                  <Option key={provider.value} value={provider.value}>
                    {provider.icon} {provider.label}
                  </Option>
                ))}
              </Select.OptGroup>
              <Select.OptGroup label="本地服务商">
                {localProviders.map(provider => (
                  <Option key={provider.value} value={provider.value}>
                    {provider.icon} {provider.label}
                  </Option>
                ))}
              </Select.OptGroup>
            </Select>
          </Form.Item>

          <Form.Item
            name="model"
            label="模型标识"
            rules={[{ required: true, message: '请输入模型标识' }]}
          >
            <Input placeholder="如：deepseek-chat, llama3:8b" />
          </Form.Item>

          <Form.Item
            name="endpoint"
            label="API端点"
            rules={[{ required: true, message: '请输入API端点' }]}
          >
            <Input placeholder="如：https://api.deepseek.com" />
          </Form.Item>

          <Form.Item
            name="apiKey"
            label="API密钥"
            rules={[
              { required: true, message: '请输入API密钥' },
              { min: 10, message: 'API密钥长度至少10位' }
            ]}
          >
            <Input.Password placeholder="请输入API密钥" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入模型描述" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="maxTokens"
              label="最大Token数"
            >
              <Input type="number" placeholder="4096" />
            </Form.Item>

            <Form.Item
              name="temperature"
              label="温度参数"
            >
              <Input type="number" step="0.1" min="0" max="2" placeholder="0.7" />
            </Form.Item>
          </div>
        </Form>
      </Drawer>
    </div>
  );
};

export default FinalModelManagementPage;