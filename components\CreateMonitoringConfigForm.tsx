import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Users, Tag } from 'lucide-react';
import Select from 'react-select';
import { UserSelectionModal, User } from './UserSelectionModal';

// --- Types ---

export interface MonitoringConfigFormData {
    id?: string;
    name: string;
    qaScheme: { value: string; label: string } | null;
    scopeType: 'all' | 'teams';
    scopeTeams: { value: string; label: string }[];
    triggerActionType: { value: string; label: string } | null;
    notify: boolean;
    notifiedUsers: { value: string; label: string }[];
}

interface CreateMonitoringConfigFormProps {
    onClose: () => void;
    onSubmit: (data: MonitoringConfigFormData) => void;
    initialData?: MonitoringConfigFormData;
}

// --- Mock Data for Selects ---
const qaSchemeOptions = [
    { value: '客户体验优化方案', label: '客户体验优化方案' },
    { value: '金融产品销售合规方案', label: '金融产品销售合规方案' },
    { value: '新员工考核方案', label: '新员工考核方案' },
];

const teamOptions = [
    { value: 'team_a', label: '贷后支持一组' },
    { value: 'team_b', label: '贷后支持二组' },
    { value: 'team_c', label: '售前咨询' },
    { value: 'team_d', label: 'VIP客户服务' },
];

const triggerActionTypeOptions = [
    { value: 'create_high_priority_alert', label: '创建高优预警' },
    { value: 'create_normal_alert', label: '创建普通预警' },
];


export const CreateMonitoringConfigForm: React.FC<CreateMonitoringConfigFormProps> = ({ onClose, onSubmit, initialData }) => {
    const [formData, setFormData] = useState<MonitoringConfigFormData>(
        initialData || {
            name: '',
            qaScheme: null,
            scopeType: 'all',
            scopeTeams: [],
            triggerActionType: null,
            notify: false,
            notifiedUsers: [],
        }
    );
    
    const [isUserModalOpen, setIsUserModalOpen] = useState(false);

    const handleSelectChange = (field: keyof MonitoringConfigFormData) => (option: any) => {
        setFormData({ ...formData, [field]: option });
    };

    const handleMultiSelectChange = (field: keyof MonitoringConfigFormData) => (options: any) => {
        setFormData({ ...formData, [field]: options || [] });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(formData);
    };

    const handleUserSelect = (users: User[]) => {
        setFormData({ ...formData, notifiedUsers: users.map(u => ({ value: u.id, label: u.name }))});
        setIsUserModalOpen(false);
    };
    
    const removeUser = (userToRemove: { value: string; label: string }) => {
        setFormData({
            ...formData,
            notifiedUsers: formData.notifiedUsers.filter(user => user.value !== userToRemove.value)
        });
    };

    return (
        <>
            <motion.div
                className="fixed inset-0 bg-black bg-opacity-30 z-40"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={onClose}
            />
            <motion.div
                className="fixed top-0 right-0 h-full bg-white shadow-2xl w-full max-w-2xl flex flex-col z-50"
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
                {/* Header */}
                <div className="flex justify-between items-center p-6 border-b border-gray-200 flex-shrink-0">
                    <h2 className="text-xl font-bold text-gray-900">
                        {initialData ? '编辑监控方案' : '新建监控方案'}
                    </h2>
                    <button onClick={onClose} className="p-1 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600">
                        <X className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="flex-grow flex flex-col overflow-hidden">
                    <main className="flex-grow p-6 space-y-8 overflow-y-auto">
                        {/* Section 1: Basic Info */}
                        <div className="space-y-4">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">监控方案名称 <span className="text-red-500">*</span></label>
                                <input
                                    id="name"
                                    type="text"
                                    value={formData.name}
                                    onChange={e => setFormData({ ...formData, name: e.target.value })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="例如：客户投诉风险监控"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">所用质检方案 <span className="text-red-500">*</span></label>
                                <Select
                                    options={qaSchemeOptions}
                                    value={formData.qaScheme}
                                    onChange={handleSelectChange('qaScheme')}
                                    placeholder="选择一个质检方案..."
                                />
                            </div>
                        </div>
                        
                        {/* Section 2: Scope */}
                        <div className="p-4 border rounded-lg bg-gray-50/50">
                             <h3 className="text-lg font-semibold mb-3">监控范围</h3>
                             <div className="flex items-center gap-6 mb-4">
                                <label className="flex items-center gap-2 cursor-pointer">
                                    <input type="radio" name="scope-type" value="all" checked={formData.scopeType === 'all'} onChange={() => setFormData(prev => ({ ...prev, scopeType: 'all', scopeTeams: [] }))} className="w-4 h-4 text-blue-600 focus:ring-blue-500" />
                                    <span>全部团队</span>
                                </label>
                                <label className="flex items-center gap-2 cursor-pointer">
                                    <input type="radio" name="scope-type" value="teams" checked={formData.scopeType === 'teams'} onChange={() => setFormData({ ...formData, scopeType: 'teams'})} className="w-4 h-4 text-blue-600 focus:ring-blue-500" />
                                    <span>指定团队</span>
                                </label>
                            </div>
                            {formData.scopeType === 'teams' && (
                                <Select
                                    isMulti
                                    options={teamOptions}
                                    value={formData.scopeTeams}
                                    onChange={handleMultiSelectChange('scopeTeams')}
                                    placeholder="选择一个或多个团队..."
                                />
                            )}
                        </div>

                        {/* Section 3: Trigger Action */}
                        <div className="p-4 border rounded-lg bg-gray-50/50">
                            <h3 className="text-lg font-semibold mb-3">触发动作</h3>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">预警级别</label>
                                    <Select
                                        options={triggerActionTypeOptions}
                                        value={formData.triggerActionType}
                                        onChange={handleSelectChange('triggerActionType')}
                                        placeholder="选择预警级别..."
                                    />
                                </div>
                                <div className="flex items-center">
                                    <input
                                        id="notify"
                                        type="checkbox"
                                        checked={formData.notify}
                                        onChange={(e) => setFormData({ ...formData, notify: e.target.checked })}
                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                    <label htmlFor="notify" className="ml-2 block text-sm text-gray-900 cursor-pointer">
                                        触发时通知相关人员
                                    </label>
                                </div>
                                {formData.notify && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">通知人</label>
                                        <div className="flex items-center flex-wrap gap-2">
                                            {formData.notifiedUsers.map(user => (
                                                <div key={user.value} className="flex items-center bg-gray-200 text-gray-800 text-sm font-medium pl-3 pr-2 py-1 rounded-full">
                                                    <Tag className="w-4 h-4 mr-1.5" />
                                                    <span>{user.label}</span>
                                                    <button type="button" onClick={() => removeUser(user)} className="ml-1.5 text-gray-500 hover:text-gray-800">
                                                        <X size={14} />
                                                    </button>
                                                </div>
                                            ))}
                                            <button 
                                                type="button"
                                                onClick={() => setIsUserModalOpen(true)}
                                                className="flex items-center text-sm bg-blue-100 text-blue-700 hover:bg-blue-200 font-semibold px-4 py-1.5 rounded-full"
                                            >
                                                <Users className="w-4 h-4 mr-2" />
                                                选择用户
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </main>
                    
                    {/* Footer */}
                    <div className="flex-shrink-0 bg-white px-6 py-4 flex justify-end items-center gap-3 border-t">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-semibold text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50"
                        >
                            取消
                        </button>
                        <button
                            type="button"
                            onClick={handleSubmit}
                            className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 border border-transparent rounded-lg shadow-sm hover:bg-blue-700"
                        >
                            {initialData ? '保存更改' : '创建方案'}
                        </button>
                    </div>
                </form>
            </motion.div>
            
            {isUserModalOpen && (
                <UserSelectionModal
                    onClose={() => setIsUserModalOpen(false)}
                    onSelect={handleUserSelect}
                    initialSelectedUsers={formData.notifiedUsers.map(u => ({ id: u.value, name: u.label }))}
                    multiSelect={true}
                />
            )}
        </>
    );
}; 