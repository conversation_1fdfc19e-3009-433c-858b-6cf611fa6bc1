import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, AlertTriangle } from 'lucide-react';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  children: React.ReactNode;
  actionText?: string;
  cancelText?: string;
  actionColor?: 'red' | 'green' | 'blue';
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  children,
  actionText = '确认',
  cancelText = '取消',
  actionColor = 'red',
}) => {
  const colors = {
    red: { bg: 'bg-red-600', hover: 'hover:bg-red-700', iconBg: 'bg-red-100', iconText: 'text-red-600' },
    green: { bg: 'bg-green-600', hover: 'hover:bg-green-700', iconBg: 'bg-green-100', iconText: 'text-green-600' },
    blue: { bg: 'bg-blue-600', hover: 'hover:bg-blue-700', iconBg: 'bg-blue-100', iconText: 'text-blue-600' },
  }
  const selectedColor = colors[actionColor];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md p-6"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="flex justify-between items-start">
              <div className="flex items-start">
                <div className={`mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full ${selectedColor.iconBg} sm:mx-0 sm:h-10 sm:w-10`}>
                    <AlertTriangle className={`h-6 w-6 ${selectedColor.iconText}`} aria-hidden="true" />
                </div>
                <div className="ml-4 text-left">
                  <h3 className="text-lg leading-6 font-semibold text-gray-900" id="modal-title">
                    {title}
                  </h3>
                  <div className="mt-2">
                    {children}
                  </div>
                </div>
              </div>
               <button onClick={onClose} className="-mt-2 -mr-2 p-2 text-gray-400 hover:text-gray-600 rounded-full"><X size={20} /></button>
            </div>
            
            <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white ${selectedColor.bg} ${selectedColor.hover} focus:outline-none sm:ml-3 sm:w-auto sm:text-sm`}
                onClick={onConfirm}
              >
                {actionText}
              </button>
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:w-auto sm:text-sm"
                onClick={onClose}
              >
                {cancelText}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}; 