import React, { useState, useEffect } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from './icons';
import type { Condition } from '../types';
import { ConditionType } from '../types';

interface RuleTesterProps {
  conditions: Condition[];
  detectionLogic: string;
}

interface TestResult {
  conditionId: string;
  matched: boolean;
  matchedText?: string[];
  reason?: string;
}

interface DialogueEntry {
  role: '客服' | '客户';
  content: string;
  sentences: string[];
}

/**
 * 规则测试组件
 * 允许用户输入测试对话文本，实时验证规则执行结果
 */
export const RuleTester: React.FC<RuleTesterProps> = ({ 
  conditions, 
  detectionLogic 
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [testText, setTestText] = useState('');
  const [dialogueEntries, setDialogueEntries] = useState<DialogueEntry[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [finalResult, setFinalResult] = useState<boolean>(false);

  // 预设测试案例
  const testCases = [
    {
      name: '违规示例一：未询问发卡行额度',
      text: `客服：您好，请问您是否拥有信用卡？
客户：我办过一张信用卡。
客服：好的，那我们为您推荐我们银行的信用卡产品。`
    },
    {
      name: '违规示例二：收入信息收集不完整',
      text: `客服：您好，请问您是否持有信用卡？
客户：有的，我办过。
客服：请问您的工作单位是什么？
客户：我在XX科技公司工作。
客服：您年收入大概多少？
客户：15万左右。
客服：请问您名下有房产吗？
客户：有一套住房。
客服：请您如实填写申请信息，确保准确无误。`
    },
    {
      name: '合规示例：完整询问流程',
      text: `客服：您好，请问您是否拥有信用卡？
客户：我办过一张。
客服：请问是哪家银行的发卡行？额度是多少？
客户：建设银行的，额度2万。
客服：好的，我们为您推荐更适合的产品。`
    }
  ];

  // 解析对话文本
  const parseDialogue = (text: string): DialogueEntry[] => {
    const lines = text.split('\n').filter(line => line.trim());
    const entries: DialogueEntry[] = [];
    
    lines.forEach(line => {
      const trimmed = line.trim();
      if (trimmed.startsWith('客服：')) {
        const content = trimmed.substring(3);
        entries.push({
          role: '客服',
          content,
          sentences: content.split(/[。！？.!?]/).filter(s => s.trim())
        });
      } else if (trimmed.startsWith('客户：')) {
        const content = trimmed.substring(3);
        entries.push({
          role: '客户',
          content,
          sentences: content.split(/[。！？.!?]/).filter(s => s.trim())
        });
      }
    });
    
    return entries;
  };

  // 测试单个条件
  const testCondition = (condition: Condition, entries: DialogueEntry[]): TestResult => {
    const roleText = entries
      .filter(entry => {
        if (condition.detectionRole === '客服') return entry.role === '客服';
        if (condition.detectionRole === '客户') return entry.role === '客户';
        return true; // 全员
      })
      .map(entry => entry.content)
      .join(' ');

    const result: TestResult = {
      conditionId: condition.id,
      matched: false,
      matchedText: [],
      reason: ''
    };

    if (condition.type === ConditionType.REGEX) {
      try {
        const regex = new RegExp(condition.hitPattern || '', 'gi');
        const matches = roleText.match(regex);
        if (matches) {
          result.matched = true;
          result.matchedText = matches;
          result.reason = `正则匹配成功：${condition.hitPattern}`;
        } else {
          result.reason = `正则匹配失败：${condition.hitPattern}`;
        }
      } catch (e) {
        result.reason = `正则表达式错误：${condition.hitPattern}`;
      }
    } else if (condition.type === ConditionType.KEYWORD) {
      const keywords = condition.keywords || [];
      const matchedKeywords: string[] = [];
      
      keywords.forEach(keyword => {
        if (roleText.includes(keyword)) {
          matchedKeywords.push(keyword);
        }
      });

      if (condition.keywordDetectionType === '包含任意一个关键词') {
        result.matched = matchedKeywords.length > 0;
        result.matchedText = matchedKeywords;
        result.reason = result.matched 
          ? `匹配关键词：${matchedKeywords.join(', ')}`
          : `未找到任何关键词：${keywords.join(', ')}`;
      } else if (condition.keywordDetectionType === '包含全部上述关键词') {
        result.matched = matchedKeywords.length === keywords.length;
        result.matchedText = matchedKeywords;
        result.reason = result.matched
          ? `匹配所有关键词：${matchedKeywords.join(', ')}`
          : `缺少关键词：${keywords.filter(k => !matchedKeywords.includes(k)).join(', ')}`;
      }
    }

    return result;
  };

  // 评估逻辑表达式
  const evaluateLogic = (logic: string, results: TestResult[]): boolean => {
    let expression = logic.toLowerCase();
    
    console.log('原始逻辑表达式:', logic);
    console.log('条件结果:', results.map(r => `${r.conditionId}: ${r.matched}`));
    
    // 替换条件为实际结果 - 使用单词边界确保精确匹配
    results.forEach(result => {
      const value = result.matched ? 'true' : 'false';
      // 使用单词边界\b确保只匹配完整的条件ID
      const regex = new RegExp(`\\b${result.conditionId}\\b`, 'g');
      expression = expression.replace(regex, value);
    });

    console.log('替换后的表达式:', expression);

    // 替换逻辑操作符
    expression = expression.replace(/&&/g, ' && ');
    expression = expression.replace(/\|\|/g, ' || ');
    expression = expression.replace(/!/g, '!');

    console.log('最终表达式:', expression);

    try {
      // 安全评估表达式
      const result = Function(`"use strict"; return (${expression})`)();
      console.log('表达式计算结果:', result);
      return result;
    } catch (e) {
      console.error('逻辑表达式评估错误:', e);
      return false;
    }
  };

  // 执行测试
  const runTest = () => {
    if (!testText.trim()) return;

    const entries = parseDialogue(testText);
    setDialogueEntries(entries);

    const results: TestResult[] = [];
    
    // 测试每个条件
    conditions.forEach(condition => {
      const result = testCondition(condition, entries);
      results.push(result);
    });

    setTestResults(results);

    // 评估最终结果
    const final = evaluateLogic(detectionLogic, results);
    setFinalResult(final);
  };

  // 自动测试（当文本改变时）
  useEffect(() => {
    if (testText.trim()) {
      const timer = setTimeout(runTest, 500); // 防抖
      return () => clearTimeout(timer);
    }
  }, [testText, conditions, detectionLogic]);

  // 加载测试案例
  const loadTestCase = (caseText: string) => {
    setTestText(caseText);
  };

  // 高亮显示匹配文本
  const highlightMatches = (text: string, matches: string[] = []) => {
    if (!matches.length) return text;
    
    let highlighted = text;
    matches.forEach((match) => {
      const regex = new RegExp(`(${match.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      highlighted = highlighted.replace(regex, `<mark class="bg-yellow-200 px-1 rounded">$1</mark>`);
    });
    
    return highlighted;
  };

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 shadow">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <span className="text-2xl">🧪</span>
          <h3 className="text-lg font-semibold text-green-800">规则测试</h3>
        </div>
        <button className="flex items-center space-x-1 text-green-600 hover:text-green-800">
          <span className="text-sm">{isExpanded ? '收起' : '展开'}</span>
          {isExpanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      </div>

      {isExpanded && (
        <div className="mt-4 space-y-4">
          {/* 快速测试案例 */}
          <div>
            <h4 className="font-medium text-gray-800 mb-2">📋 快速测试案例</h4>
            <div className="flex flex-wrap gap-2">
              {testCases.map((testCase, index) => (
                <button
                  key={index}
                  onClick={() => loadTestCase(testCase.text)}
                  className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors"
                >
                  {testCase.name}
                </button>
              ))}
            </div>
          </div>

          {/* 测试输入区 */}
          <div>
            <h4 className="font-medium text-gray-800 mb-2">💬 输入测试对话</h4>
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-sm"
              placeholder="请输入对话内容，格式如下：
客服：您好，我想了解一下您是否有信用卡？
客户：我办过工商银行的信用卡。
客服：好的，那我们为您推荐..."
            />
          </div>

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* 条件测试结果 */}
              <div>
                <h4 className="font-medium text-gray-800 mb-3">🔍 条件测试结果</h4>
                <div className="space-y-2">
                  {testResults.map((result) => {
                    const condition = conditions.find(c => c.id === result.conditionId);
                    return (
                      <div
                        key={result.conditionId}
                        className={`p-3 rounded-lg border-l-4 ${
                          result.matched
                            ? 'bg-green-50 border-green-400'
                            : 'bg-red-50 border-red-400'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-sm">
                            条件{result.conditionId}: {condition?.name}
                          </span>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              result.matched
                                ? 'bg-green-200 text-green-800'
                                : 'bg-red-200 text-red-800'
                            }`}
                          >
                            {result.matched ? '✓ 匹配' : '✗ 未匹配'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">
                          {result.reason}
                        </div>
                        {result.matchedText && result.matchedText.length > 0 && (
                          <div className="mt-1 text-xs">
                            <span className="text-gray-500">匹配内容：</span>
                            <span className="text-blue-600 font-medium">
                              {result.matchedText.join(', ')}
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 最终结果和对话分析 */}
              <div>
                <h4 className="font-medium text-gray-800 mb-3">📊 测试总结</h4>
                
                {/* 最终结果 */}
                <div className={`p-4 rounded-lg mb-4 ${
                  finalResult
                    ? 'bg-red-100 border border-red-300'
                    : 'bg-green-100 border border-green-300'
                }`}>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">
                      {finalResult ? '⚠️' : '✅'}
                    </span>
                    <span className={`font-bold text-lg ${
                      finalResult ? 'text-red-800' : 'text-green-800'
                    }`}>
                      {finalResult ? '规则触发' : '规则未触发'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-700">
                    <div className="mb-1">
                      <span className="font-medium">逻辑表达式：</span>
                      <code className="bg-white px-2 py-1 rounded text-xs">
                        {detectionLogic}
                      </code>
                    </div>
                    <div>
                      <span className="font-medium">计算结果：</span>
                      {finalResult ? '表达式为真，触发违规' : '表达式为假，符合规范'}
                    </div>
                  </div>
                </div>

                {/* 对话角色分析 */}
                {dialogueEntries.length > 0 && (
                  <div className="bg-white p-3 rounded-lg border">
                    <h5 className="font-medium text-gray-800 mb-2">👥 对话分析</h5>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {dialogueEntries.map((entry, index) => (
                        <div key={index} className="text-sm">
                          <span className={`font-medium ${
                            entry.role === '客服' ? 'text-blue-600' : 'text-green-600'
                          }`}>
                            {entry.role}：
                          </span>
                          <span 
                            className="text-gray-700"
                            dangerouslySetInnerHTML={{
                              __html: highlightMatches(
                                entry.content,
                                testResults.flatMap(r => r.matchedText || [])
                              )
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <h5 className="font-medium text-blue-800 mb-2">💡 使用说明</h5>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 按照"客服：" "客户："的格式输入对话内容</li>
              <li>• 支持实时测试，输入后自动显示结果</li>
              <li>• 绿色表示条件匹配，红色表示条件未匹配</li>
              <li>• 匹配的关键词会在对话中高亮显示</li>
              <li>• 最终结果基于逻辑表达式计算得出</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}; 