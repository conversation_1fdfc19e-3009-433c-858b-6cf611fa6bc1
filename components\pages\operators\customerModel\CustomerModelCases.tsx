import React from 'react';
import { CustomerModelConfigType, DetectionType } from '../CustomerModelPage';

/**
 * @typedef {object} Case
 * @property {string} name
 * @property {string} description
 * @property {DetectionType} detectionType
 * @property {string} testText
 */
interface Case {
  name: string;
  description: string;
  detectionType: DetectionType;
  testText: string;
}

/**
 * @typedef {object} Props
 * @property {(config: React.SetStateAction<CustomerModelConfigType>) => void} setConfig
 * @property {(text: string) => void} setTestText
 */
interface Props {
  setConfig: React.Dispatch<React.SetStateAction<CustomerModelConfigType>>;
  setTestText: (text: string) => void;
}

const cases: Case[] = [
  {
    name: '客户投诉意图检测',
    description: '检测客户明确表达投诉意图的场景。',
    detectionType: '投诉',
    testText: '客户：你们这个产品有问题，我要投诉你们！\n客服：您先别着急，请问是什么问题呢？'
  },
  {
    name: '客户升级问题检测',
    description: '检测客户要求找更高级别人员处理问题的场景。',
    detectionType: '升级问题',
    testText: '客户：你解决不了，给我找你们负责人来。\n客服：好的，我马上为您转接我的主管。'
  },
  {
    name: '客户表扬意图检测',
    description: '检测客户对客服服务表示满意的场景。',
    detectionType: '表扬',
    testText: '客户：太感谢你了，你的服务真好，必须给你点个赞！\n客服：不客气，这是我应该做的。'
  },
  {
    name: '客户愤怒情绪检测',
    description: '检测客户带有明显愤怒情绪的场景。',
    detectionType: '愤怒',
    testText: '客户：你们怎么搞的！太让我生气了！\n客服：您消消气，我非常理解您现在的心情。'
  }
];

/**
 * 客户模型检测案例库组件
 * @param {Props} props
 * @returns {React.ReactElement}
 */
export const CustomerModelCases: React.FC<Props> = ({ setConfig, setTestText }) => {
  const loadCase = (c: Case) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      detectionType: c.detectionType
    }));
    setTestText(c.testText);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
              </div>
              <button 
                onClick={() => loadCase(c)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 