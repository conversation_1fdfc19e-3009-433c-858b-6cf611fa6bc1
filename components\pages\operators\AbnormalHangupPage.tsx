import React, { useState } from 'react';
import AbnormalHangupConcepts from './abnormalHangup/AbnormalHangupConcepts';
import AbnormalHangupConfig from './abnormalHangup/AbnormalHangupConfig';
import AbnormalHangupTester from './abnormalHangup/AbnormalHangupTester';
import AbnormalHangupCases from './abnormalHangup/AbnormalHangupCases';
import AbnormalHangupTips from './abnormalHangup/AbnormalHangupTips';

/**
 * 非正常挂机主页面
 * @constructor
 */
const AbnormalHangupPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState({
    operator: '>' as '>' | '<' | '>=' | '<=' | '=',
    duration: 5,
    role: 'any' as 'any' | 'customer' | 'agent',
  });

  // 测试文本状态
  const [testText, setTestText] = useState(
    '[00:01] 客服：您好，请问有什么可以帮您？\n[00:05] 客户：我想咨询一下这个产品。\n[00:10] 客服：好的，这个产品我们有现货。\n[00:15] 客户：那太好了，谢谢。\n[HANGUP_TIME: 22.5]'
  );

  // 预设案例
  const cases = [
    {
      name: '客服挂机过慢',
      description: '检测客服在客户说完话后，是否等待过久才挂机。',
      config: { operator: '>' as const, duration: 5, role: 'any' as const },
      text: '[00:01] 客服：好的，那先这样，再见。\n[00:03] 客户：再见。\n[HANGUP_TIME: 10]',
      expectedResult: '应该命中 (等待7秒 > 5秒)',
    },
    {
      name: '客户说完立即挂机',
      description: '检测挂机时间是否过短，可能导致服务体验不佳。',
      config: { operator: '<' as const, duration: 2, role: 'any' as const },
      text: '[00:50] 客户：好的，我知道了，谢谢。\n[HANGUP_TIME: 50.5]',
      expectedResult: '应该命中 (等待0.5秒 < 2秒)',
    },
    {
        name: '指定角色为客服',
        description: '检测最后一句话是客服时，挂机是否过慢。',
        config: { operator: '>' as const, duration: 5, role: 'agent' as const },
        text: '[01:10] 客户：好的，谢谢你。\n[01:12] 客服：不客气，再见。\n[HANGUP_TIME: 20]',
        expectedResult: '应该命中 (最后是客服，等待8秒 > 5秒)',
    },
    {
        name: '指定角色不匹配',
        description: '最后一句话是客户，不满足角色为客服的条件。',
        config: { operator: '>' as const, duration: 5, role: 'agent' as const },
        text: '[01:10] 客服：还有其他问题吗？\n[01:12] 客户：没有了，再见。\n[HANGUP_TIME: 20]',
        expectedResult: '不应命中 (角色不匹配)',
    },
  ];

  /**
   * 加载案例
   * @param caseConfig
   * @param caseText
   */
  const loadCase = (caseConfig: any, caseText: string) => {
    setConfig({ ...config, ...caseConfig });
    setTestText(caseText);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
              📞 非正常挂机
              </h1>
              <p className="text-gray-600 mt-1">
                检测通话结束后的挂机行为是否符合规范
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                ⭐ 进阶功能
              </span>
              <span className="text-sm text-gray-500">
                算子类型：语音检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            <AbnormalHangupConcepts />
            <AbnormalHangupConfig config={config} setConfig={setConfig} />
            <AbnormalHangupTester config={config} text={testText} setText={setTestText} />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            <AbnormalHangupCases cases={cases} loadCase={loadCase} />
            <AbnormalHangupTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>掌握数据格式</strong>：务必使用 `[mm:ss]` 和 `[HANGUP_TIME: ...]` 的标准格式以确保测试成功。</p>
                <p>• <strong>理解业务场景</strong>：思考不同业务场景下（如售后、咨询），合理的挂机等待时长应如何设定。</p>
                <p>• <strong>结合角色判断</strong>：当最后一句话的角色不同时，挂机策略可能也不同，注意结合角色配置进行测试。</p>
                <p>• <strong>从案例开始</strong>：先加载预设案例，理解其配置和文本，再根据实际需求进行修改和测试。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AbnormalHangupPage; 