
---

### 页面七：质检成绩管理 (FinalPersonalPerformancePage.tsx)

#### 1. 核心定位与目标
这个页面是一个**多角色复用**的质检成绩查询中心。它的核心目标是根据当前登录用户的角色（坐席、班组长、主管），提供一个**权限范围内、可追溯、可筛选**的所有历史质检成绩记录列表。它比首页的“近期成绩”列表更全面，是一个用于正式查询和回顾的工具。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题与副标题 (动态变化)**:
    *   **坐席视角**: "我的质检成绩" - "查看我的历史质检成绩、得分详情和申诉记录"
    *   **班组长视角**: "团队质检成绩" - "查看您所在班组成员的质检成绩..."
    *   **主管视角**: "质检成绩管理" - "查看所有团队及坐席的质检成绩..."
*   **图标**: `BarChart3` (条形图图标)，象征着对绩效和数据的分析。
*   **徽章 (动态变化)**: 根据当前角色显示不同的标签，如“坐席视角”、“班组长视角”、“主管视角”，明确告知用户当前所处的数据权限范围。

**b. 角色切换器 (Supervisor/Admin Only)**
*   **位置**: 设计在页面头部下方。
*   **功能**: 允许高权限用户（如质检主管）在**坐席、班组长、主管**三种视角之间自由切换，以模拟不同角色的数据视图，便于全局管理和问题排查。这是一个非常实用和贴心的设计。

**c. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 提供一个强大的、多维度的查询工具，让用户能精准地找到任何一条历史记录。
*   **筛选字段 (根据角色动态显示)**:
    *   **通用字段**: 记录编号、所属任务、客户号码、通话开始时间范围、最终得分范围、质检结果、申诉状态、申诉结果。
    *   **班组长/主管可见字段**: 坐席（姓名/工号）。
    *   **主管可见字段**: 所属班组。
*   **交互**:
    *   提供了丰富的筛选条件，满足各种复杂的查询场景。
    *   支持展开/收起，保持界面整洁。

**d. 成绩记录表格 (`Table`)**
这是页面的核心内容，以详细的表格形式展示所有符合条件的质检记录。

*   **表格列 (根据角色动态显示)**:
    *   `序号`
    *   `记录编号`
    *   `所属任务`
    *   `坐席`: 班组长和主管视角下显示。
    *   `所属班组`: 主管视角下显示。
    *   `客户号码`
    *   `通话开始时间`
    *   `通话时长`
    *   `最终得分`: **核心指标**，鼠标悬浮在旁边的信息图标上，会弹出一个Tooltip，清晰地展示**分数演进过程**（AI初检得分 → 人工复核得分 → 申诉得分），增加了分数的可解释性。
    *   `质检结果`: “合格”或“不合格”的徽章。
    *   `申诉信息`: 一个复合信息列，用不同颜色的徽章和文字描述了当前记录的申诉状态（可申诉/申诉中/已处理/已过期）、申诉结果（成功/失败）以及相关的关键时间点（如申诉有效期、处理时间）。
    *   `操作`:
        *   **查看详情 (`Eye`图标)**: 所有角色都有此权限，点击跳转到 **[多模式会话详情页]**，回顾该次通话的所有细节。
        *   **发起申诉 (仅坐席)**: 在“可申诉”状态下，坐席会看到一个额外的按钮，用于发起申诉流程。

*   **表格特点**:
    *   **信息丰富且结构化**: 将复杂的申诉状态和分数构成通过巧妙的UI设计（徽章、Tooltip）清晰地呈现出来。
    *   **权限控制**: 表格的列会根据用户角色动态增减，保护了数据隐私并提供了与角色相匹配的信息。

**e. 分页组件 (`UnifiedPagination`)**
*   位于表格下方，用于浏览大量的历史成绩数据。

#### 3. 核心交互与操作
*   **多维度查询**: 用户可以通过组合不同的筛选条件，实现非常灵活的数据查询，例如“查询A组上个月所有得分低于80分且申诉失败的记录”。
*   **数据追溯**: 任何一条记录都可以点击“查看详情”进行深度追溯，了解其从AI初检到最终定论的全过程。
*   **角色模拟**: 主管可以通过角色切换器，快速代入班组长或坐席的视角，更好地理解他们看到的数据，便于沟通和管理。
*   **申诉入口**: 为坐席提供了在查看成绩后直接发起申诉的便捷入口，形成了“查看-申诉”的闭环操作。

---