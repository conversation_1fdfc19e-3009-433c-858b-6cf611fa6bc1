
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 文字检查 > 上下文重复检查**

产品详情 | ❤️ 我的收藏

# **上下文重复检查**
更新时间: 2025-02-07 14:51:07

本文介绍上下文重复检查如何进行配置。

*   **功能介绍**：检测当前句子与之前的句子内容是否重复。
*   **配置方法**：
    *   **检测角色**：角色分为：客服、客户、所用角色。
    *   **检测范围**：全文、指定范围
        *   全文：检测全文。
        *   指定范围：可指定第 a~b 句。
    *   **检查逻辑**：在 xx 句以内重复，句子的相似度大于等于 xx（0.1-1.0，分数越高，相似度越高）。
        并且在第 xx 次重复出现时算违规，当一句话少于或等于 xx 个字时不检测。
        *   设置在几句话内重复：一般是设置在 2-3 句话内。
        *   设置语句重复的相似度的阈值：大于等于多少则命中，一般是设置 0.8。
        *   设置重复的话句在第几次属于违规，并且设置不检测条件。
    *   **例外句子**：可以设置例外外的句子，即这些句子重复出现时不算上下文重复。
*   **使用示例**：假设客户对客服强烈不满，现在要检测客户是否有对客服连续表示投诉，可设置客户在 3 句话内是否有重复，具体应用可参考下图：按 ENTER 键添加例外的句子。

**(图片为UI配置界面截图)**

**d ^ 上下文重复检查**

| | |
| :--- | :--- |
| **检测角色** | 所有角色 |
| **前置条件** | 无 |
| **检测范围** | 指定范围 |
| **范围** | 第 ` ` ~ ` ` 句 |
| **检查逻辑** | 在 **3** 句以内重复，重复句子的相似度大于等于 **0.8** (0.1-1.0, 分数越高, 相似度越高)。<br>并且在第 **2** 次重复出现时算违规，当一句话少于或等于 **2** 个字时不检测。 |
| **例外句子** | 可以设置例外外的句子，即这些句子重复出现时不算上下文重复：<br> [ 输入按enter键添加例外的句子 ] |
| | **+ 新增条件** |