import React from 'react';

/**
 * 信息实体检查核心概念组件
 */
const EntityCheckConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">核心概念</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>
      
      <div className="space-y-6">
        {/* 功能介绍 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                检测对话中指定类型的实体信息（如日期、时间、人名、地址等），并与通话的随路参数（元数据）进行比较验证。
                主要用于核验对话内容与业务记录的一致性，发现信息不匹配、身份异常等质量问题。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客服身份一致性验证</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客户地址信息核验</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">预约日期冲突检测</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">业务线归属确认</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">敏感信息泄露防护</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">技能组匹配验证</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">智能实体识别</span>
                    <span className="text-xs text-gray-500 ml-1">- 支持多种格式和表达方式</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">多种逻辑运算</span>
                    <span className="text-xs text-gray-500 ml-1">- 等于、不等于、包含、不包含</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">高准确率识别</span>
                    <span className="text-xs text-gray-500 ml-1">- 基于NLP技术的智能识别</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">灵活配置条件</span>
                    <span className="text-xs text-gray-500 ml-1">- 支持多条件组合判断</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 算法原理 */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🔬</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">算法原理</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-orange-200 rounded-full flex items-center justify-center text-orange-800 text-xs font-bold">
                    1
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">实体识别</span>
                    <p className="text-xs text-gray-600 mt-1">在对话中识别指定类型的实体（如人名"张三"、城市"上海"）</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-orange-200 rounded-full flex items-center justify-center text-orange-800 text-xs font-bold">
                    2
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">获取随路参数</span>
                    <p className="text-xs text-gray-600 mt-1">从通话元数据中获取对应字段值（如客服姓名"李四"）</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-orange-200 rounded-full flex items-center justify-center text-orange-800 text-xs font-bold">
                    3
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">比较验证</span>
                    <p className="text-xs text-gray-600 mt-1">按逻辑运算符比较实体值与元数据值（"张三" = "李四"？）</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 与其他算子对比 */}
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-gray-600 text-lg">📊</span>
            <h3 className="font-semibold text-gray-900">与其他算子对比</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-3 font-medium text-gray-700">算子类型</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">检测对象</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">技术复杂度</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">应用场景</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">准确率</th>
                </tr>
              </thead>
              <tbody className="text-xs">
                <tr className="border-b border-gray-100 bg-blue-50">
                  <td className="py-2 px-3 font-medium text-blue-800">信息实体检查</td>
                  <td className="py-2 px-3 text-gray-700">结构化实体信息</td>
                  <td className="py-2 px-3 text-gray-700">中等</td>
                  <td className="py-2 px-3 text-gray-700">信息合规、数据验证</td>
                  <td className="py-2 px-3 text-green-600 font-medium">90%+</td>
                </tr>
                <tr className="border-b border-gray-100">
                  <td className="py-2 px-3 text-gray-700">关键词检查</td>
                  <td className="py-2 px-3 text-gray-700">关键词字符串</td>
                  <td className="py-2 px-3 text-gray-700">简单</td>
                  <td className="py-2 px-3 text-gray-700">基础词汇监控</td>
                  <td className="py-2 px-3 text-gray-600">95%+</td>
                </tr>
                <tr className="border-b border-gray-100">
                  <td className="py-2 px-3 text-gray-700">正则表达式</td>
                  <td className="py-2 px-3 text-gray-700">模式匹配</td>
                  <td className="py-2 px-3 text-gray-700">复杂</td>
                  <td className="py-2 px-3 text-gray-700">复杂格式验证</td>
                  <td className="py-2 px-3 text-gray-600">85%+</td>
                </tr>
                <tr className="border-b border-gray-100">
                  <td className="py-2 px-3 text-gray-700">文本相似度</td>
                  <td className="py-2 px-3 text-gray-700">语义相似性</td>
                  <td className="py-2 px-3 text-gray-700">复杂</td>
                  <td className="py-2 px-3 text-gray-700">内容相似性判断</td>
                  <td className="py-2 px-3 text-gray-600">80%+</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntityCheckConcepts; 