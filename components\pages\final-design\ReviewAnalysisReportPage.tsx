import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
    Users,
    BarChart3,
    <PERSON>ert<PERSON>riangle,
    Award,
    Download,
    Refresh<PERSON>w,
    Brain,
    UserCheck,
    MessageSquareX,
    Target,
    Zap,
    CheckCircle,
    XCircle,
    Eye,
    Clock,
    Filter,
    ChevronDown,
    Search,
    TrendingUp
} from 'lucide-react';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { EnhancedKPICard } from './common/EnhancedKPICard';
import { EnhancedChartContainer } from './common/EnhancedChartContainer';
import { EnhancedRankingCard } from './common/EnhancedRankingCard';
import { EnhancedFilterSection } from './common/EnhancedFilterSection';
import { EnhancedSectionHeader } from './common/EnhancedSectionHeader';
import { EnhancedTooltip } from './common/EnhancedTooltip';
import { chartTheme, chartColors, chartTypeConfigs } from './common/ChartTheme';
import { 
    ComposedChart, 
    Line, 
    Bar, 
    XAxis, 
    YAxis, 
    CartesianGrid, 
    Tooltip, 
    Legend, 
    ResponsiveContainer,
    BarChart,
    Cell,
    PieChart,
    Pie
} from 'recharts';
import UnifiedPagination from './components/UnifiedPagination';



interface TrendData {
    date: string;
    reviewCount: number;
}

interface RankingItem {
    name: string;
    value: number;
    percentage?: number;
    count?: number;
    reviewer?: string;
}

interface ReviewerConsistencyData {
    reviewer: string;
    reviewCount: number;
    avgScore: number;
    correctionRate: number;
}

interface CallRecord {
    id: string;
    customer: string;
    agent: string;
    duration: string;
    aiScore: number;
    reviewScore: number;
    difference: number;
    reviewer: string;
}





/**
 * 复核工作分析报告页面
 */
export const ReviewAnalysisReportPage: React.FC = () => {
    const [timeRange, setTimeRange] = useState('本月');
    const [selectedReviewer, setSelectedReviewer] = useState('全部复核员');
    const [selectedTeam, setSelectedTeam] = useState('全部班组');
    const [reviewReason, setReviewReason] = useState('全部原因');

    // 复核工作分析报告页面设计说明 (业务角度)
    const designGuideContent = (
        <div className="space-y-4 text-slate-700 text-sm leading-relaxed">
            <h2 className="text-xl font-bold text-slate-800">页面：复核工作分析报告 (ReviewAnalysisReportPage.tsx) - 业务设计说明</h2>

            <h3 className="text-lg font-semibold text-slate-700 mt-4">1. 核心定位与目标</h3>
            <p>
                复核工作分析报告页面是一个专注于<b>人工复核环节</b>的专题分析报表。
                其核心目标是评估和监控<b>复核团队的工作状态和质量</b>，并深入分析
                <b>AI初检结果与人工复核结果之间的差异</b>。这份报告对于衡量AI的准确率、
                优化AI模型、以及统一复核员之间的打分尺度至关重要。
            </p>

            <h3 className="text-lg font-semibold text-slate-700 mt-4">2. 主要功能模块与内容</h3>

            <h4 className="text-md font-semibold text-slate-700 mt-3">a. 页面顶部区域</h4>
            <p>此区域提供页面的核心概览信息和全局操作入口：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>标题</b>: "复核工作分析报告"，明确页面的主题和功能。</li>
                <li><b>副标题</b>: "从工作量、复核结果、AI差异三个核心维度，全面评估和监控复核团队的工作状态和质量"，阐述页面提供的核心业务价值。</li>
                <li><b>角色标识</b>: 使用带对勾的用户图标 (<code className="bg-slate-100 px-1 py-0.5 rounded text-red-500">UserCheck</code>)，直观代表人工审核的关注点。</li>
                <li><b>核心操作</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>刷新数据</b>: 允许管理者获取最新的复核工作数据，确保信息的实时性。</li>
                        <li><b>导出报表</b>: 提供将当前报表数据导出为可离线分析的格式，方便后续深入分析或汇报。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">b. 统一搜索筛选器</h4>
            <p>此模块提供灵活的数据筛选功能，允许管理者聚焦特定业务维度进行分析：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>目的</b>: 允许管理者对复核数据进行多维度筛选，以进行更具针对性的业务分析。</li>
                <li><b>筛选字段及业务含义</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>时间范围</b>: 定义报告的统计周期，便于观察复核工作在不同时间段的表现和趋势。</li>
                        <li><b>复核员</b>: 可以查看单个复核员的工作量、打分习惯和纠错率，也可以查看全体复核员的汇总数据，用于绩效评估和能力提升。</li>
                        <li><b>被检班组/团队</b>: 可以分析针对特定团队的复核情况，评估不同团队的服务质量问题在复核环节的体现。</li>
                        <li><b>触发复核原因</b>: 可以筛选不同原因（如"低分触发"、"关键词触发"、"随机抽检"、"申诉复核"）进入复核流程的数据，用于评估不同复核策略的效果和效率。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">c. 核心KPI指标卡片（复核工作概览）</h4>
            <p>此模块以直观的卡片形式展示了复核工作的核心绩效指标，旨在提供复核团队的工作概览。所有指标的数据均为系统自动聚合，反映复核环节的业务表现。指标内容、业务含义及计算实现方式如下：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>总复核量</b>:
                    <p>业务含义：衡量复核团队在选定时间范围内的总工作量。</p>
                    <p>计算/实现：统计在选定时间范围内，人工复核员完成的复核任务总数。</p>
                </li>
                <li><b>待复核量</b>:
                    <p>业务含义：当前积压的、已分配但尚未完成的复核任务数量，反映工作负荷和处理效率。</p>
                    <p>计算/实现：统计当前系统中状态为"待复核"的任务总数。</p>
                </li>
                <li><b>日均复核量</b>:
                    <p>业务含义：复核团队的平均工作效率。</p>
                    <p>计算/实现：<code>总复核量 / 报告周期内的实际工作天数</code>。</p>
                </li>
                <li><b>复核率</b>:
                    <p>业务含义：人工审核任务占总质检量的比例，是评估人工干预程度的关键指标。</p>
                    <p>计算/实现：<code>(总复核量 / 总质检量) × 100%</code>。</p>
                </li>
                <li><b>AI结果纠错率</b>:
                    <p>业务含义：<b>核心价值指标</b>，指复核分数与AI初检分数不一致的比例，直接衡量了人工复核环节对AI结果的修正价值，是评估AI准确性的重要依据。</p>
                    <p>计算/实现：<code>(AI初检分数与人工复核分数不一致的通话数量 / 总复核量) × 100%</code>。</p>
                </li>
                <li><b>复核后合格率</b>:
                    <p>业务含义：经过人工复核确认后的最终合格率，通常被认为是更准确、更真实的团队服务质量指标。</p>
                    <p>计算/实现：<code>(人工复核后达到合格标准的通话数量 / 总复核量) × 100%</code>。</p>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">d. 复核工作量概览（左右双图表布局）</h4>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>左侧图表 - 复核任务完成趋势</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 以柱状图展示每日或每周复核团队完成的任务数量，直观反映复核工作量的波动情况。</li>
                        <li><b>业务价值与实现</b>: 帮助管理者评估复核团队的工作负荷是否均衡，是否存在周期性高峰或低谷，从而合理调配人力资源。数据来源于系统每日/每周的复核任务完成量统计。</li>
                    </ul>
                </li>
                <li><b>右侧图表 - 复核后成绩分布</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 以饼图或环形图形式，展示经过人工复核后，最终得分的分布情况（例如90-100分占比、80-89分占比等）。</li>
                        <li><b>业务价值与实现</b>: 帮助管理者了解经过人工校准和确认后，实际的服务质量水平分布。这比单纯的AI初检结果更具指导意义，能够反映最终的服务质量达成情况。数据来源于所有人工复核通话的最终得分汇总和按分数段的统计。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">e. 复核结果分析（左右双列表布局）</h4>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>左侧列表 - 复核后高频失分规则 TOP 10</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 列出在<b>人工复核确认</b>的案例中，最常导致扣分的规则。</li>
                        <li><b>业务价值与实现</b>: 这些规则反映的是经过人工验证后的真实问题，比单纯的AI初检统计结果更可靠，更能反映当前服务中普遍存在的、需要改进的短板。数据来源于人工复核结果中扣分规则的命中统计与聚合。</li>
                    </ul>
                </li>
                <li><b>右侧列表 - 复核后严重错误规则 TOP 10</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 列出在人工复核中被确认的、最常见的"严重违规"级别规则。</li>
                        <li><b>业务价值与实现</b>: 这类问题是业务运营中的"红线"，必须高度关注并迅速处理。此列表直接暴露了经过人工验证的、最严重的合规和服务风险。数据来源于人工复核结果中严重违规规则的命中统计。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">f. AI与人工复核差异洞察（左右双卡片布局）</h4>
            <p>这是本报告最核心的部分，旨在深入分析AI初检结果与人工复核结果之间的差异，从而指导AI模型优化和人工复核标准统一：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>左侧卡片 - 分数调整分析</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 统计在所有复核案例中，"分数调增"（人工复核分数高于AI初检）、"分数调减"（人工复核分数低于AI初检）、"分数不变"的案例数量、占比，以及平均调整的分值。</li>
                        <li><b>业务价值与实现</b>: 如果"分数调增"或"分数调减"的比例很高，说明AI的评分标准可能与人的期望存在系统性偏差（AI判断过严或过松）。通过分析调整类型和幅度，可以为AI模型的迭代优化提供明确的方向。数据来源于对比AI初检分和人工复核分。</li>
                    </ul>
                </li>
                <li><b>右侧卡片 - 最大差异规则排行 TOP 10</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>内容</b>: 列出那些<b>AI评分与人工评分差异最大</b>的规则。</li>
                        <li><b>业务价值与实现</b>: 直接定位了AI最不擅长、最容易出错的质检点。例如，如果"服务热情度评估"这条规则差异最大，说明AI在理解人类微妙情绪、主观判断等方面的能力还有待提升。这些数据是AI模型迭代优化的最宝贵输入。数据来源于计算每条规则AI评分与人工复核评分的差异绝对值并进行排序。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">g. 复核员一致性快照（表格）</h4>
            <p>此模块旨在对比不同复核员的打分习惯和判断标准，评估复核团队内部标准的一致性，从而促进复核质量的标准化：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>表格列</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>复核员</b>: 参与复核工作的具体人员。</li>
                        <li><b>复核量</b>: 该复核员在选定时间范围内完成的复核任务数量。</li>
                        <li><b>平均复核分数</b>: 该复核员所复核通话的平均分数。如果某个复核员的平均分显著高于或低于其他人，可能说明其打分尺度存在偏差（过松或过严），需要进行校准。</li>
                        <li><b>AI结果纠错率</b>: 该复核员复核的通话中，AI初检结果被其修正的比例。如果纠错率特别高或特别低，也需要关注其打分标准是否与AI系统或其他复核员存在较大差异。</li>
                        <li><b>一致性评估</b>: 系统可以根据上述指标综合给出一个评估（如"优秀"、"良好"、"需关注"），作为对其复核质量一致性的初步判断。</li>
                    </ul>
                </li>
                <li><b>业务价值与实现</b>: 此快照有助于管理者发现团队内部打分尺度不一致的问题，并为针对性培训和复核标准统一提供数据支持，最终提升整个复核团队的专业性和公正性。数据来源于各复核员的复核任务统计、分数聚合以及与AI分数的对比。</li>
            </ul>

            <h4 className="text-md font-semibold text-slate-700 mt-3">h. 典型差异通话记录（表格）</h4>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>功能</b>: 列出AI评分与人工复核评分差异最大的几个典型案例。</li>
                <li><b>内容</b>: 包含通话记录ID、客户信息、坐席名称、通话时长、AI初检得分、人工复核得分、分数差异以及复核员信息。</li>
                <li><b>交互</b>: 提供"查看详情"操作，点击可跳转到会话详情页，让管理者可以直接研究这些"疑难杂症"通话的具体内容，从而深入分析差异产生的原因，以决策是优化AI质检规则，还是对复核员进行针对性的认知统一培训。</li>
                <li><b>业务价值与实现</b>: 这些差异较大的案例是优化AI模型和统一复核员标准的宝贵素材。通过对这些案例的复盘分析，可以不断提升AI质检的准确性和人工复核的客观性。数据来源于AI初检分与人工复核分差异绝对值最大的通话记录。</li>
            </ul>

            <h3 className="text-lg font-semibold text-slate-700 mt-4">3. 核心交互与操作</h3>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>聚焦人工环节</b>: 整个报告的所有数据都围绕"人工复核"这一核心环节展开，为管理者提供了对复核工作全面而深入的洞察。</li>
                <li><b>评估AI效能</b>: 通过对比AI初检结果与人工复核结果的差异，报告能够量化地评估当前AI质检系统的准确性和可靠性，识别AI在哪些方面还有提升空间。</li>
                <li><b>驱动双向优化</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>优化AI</b>: "最大差异规则排行"和"典型差异通话记录"等模块直接为AI模型训练提供了高质量的标注数据和明确的优化方向，推动AI质检能力的持续进步。</li>
                        <li><b>优化人</b>: "复核员一致性快照"帮助管理者发现复核团队内部的打分尺度不一问题，通过有针对性的培训和讨论，逐步统一复核标准，提升团队整体的专业水平和公正性。</li>
                    </ul>
                </li>
                <li><b>数据闭环</b>: 将人工复核这一投入成本，有效转化为优化AI质检系统和提升人工复核管理水平的数据资产，从而形成了"AI初检 → 人工复核 → 数据分析 → AI优化 & 人员培训"的良性循环和价值闭环。</li>
            </ul>
        </div>
    );

    // Enhanced KPI数据
    const kpiData = useMemo(() => [
        {
            title: '总复核量',
            value: '2,847',
            unit: '通',
            trend: 'up' as const,
            trendValue: '+8.3%',
            icon: UserCheck,
            gradient: 'bg-gradient-to-br from-blue-500 to-blue-600',
            description: '周期内完成的复核任务总数'
        },
        {
            title: '待复核量',
            value: '156',
            unit: '通',
            trend: 'down' as const,
            trendValue: '-12.5%',
            icon: Clock,
            gradient: 'bg-gradient-to-br from-orange-500 to-orange-600',
            description: '当前分配且未完成的任务数'
        },
        {
            title: '日均复核量',
            value: '127',
            unit: '通/天',
            trend: 'up' as const,
            trendValue: '+5.2%',
            icon: BarChart3,
            gradient: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
            description: '平均每个工作日完成的复核量'
        },
        {
            title: '复核率',
            value: '18.7',
            unit: '%',
            trend: 'stable' as const,
            trendValue: '+0.1%',
            icon: Target,
            gradient: 'bg-gradient-to-br from-purple-500 to-purple-600',
            description: '人工审核的投入比例'
        },
        {
            title: 'AI结果纠错率',
            value: '23.4',
            unit: '%',
            trend: 'down' as const,
            trendValue: '-2.8%',
            icon: Brain,
            gradient: 'bg-gradient-to-br from-red-500 to-red-600',
            description: '复核分数与AI初检分数不一致的比例'
        },
        {
            title: '复核后合格率',
            value: '89.6',
            unit: '%',
            trend: 'up' as const,
            trendValue: '+1.9%',
            icon: CheckCircle,
            gradient: 'bg-gradient-to-br from-teal-500 to-teal-600',
            description: '经人工复核确认的最终合格率'
        }
    ], []);

    // 模拟复核任务完成趋势数据
    const reviewTrendData: TrendData[] = useMemo(() => [
        { date: '01-01', reviewCount: 98 },
        { date: '01-02', reviewCount: 125 },
        { date: '01-03', reviewCount: 142 },
        { date: '01-04', reviewCount: 118 },
        { date: '01-05', reviewCount: 156 },
        { date: '01-06', reviewCount: 134 },
        { date: '01-07', reviewCount: 89 },
        { date: '01-08', reviewCount: 167 },
        { date: '01-09', reviewCount: 145 },
        { date: '01-10', reviewCount: 127 }
    ], []);

    // 模拟复核后成绩分布数据
    const scoreDistributionData = useMemo(() => [
        { range: '0-59分', count: 89, percentage: 3.1, color: '#ef4444' },
        { range: '60-79分', count: 298, percentage: 10.5, color: '#f97316' },
        { range: '80-89分', count: 1456, percentage: 51.1, color: '#eab308' },
        { range: '90-100分', count: 1004, percentage: 35.3, color: '#22c55e' }
    ], []);

    // 模拟复核后高频失分规则数据
    const reviewedProblematicRules: RankingItem[] = useMemo(() => [
        { name: '未主动询问客户需求', value: 0, count: 156, percentage: 24.8 },
        { name: '服务用语不规范', value: 0, count: 134, percentage: 21.3 },
        { name: '通话结束未确认客户满意度', value: 0, count: 98, percentage: 15.6 },
        { name: '等待时间过长未道歉', value: 0, count: 87, percentage: 13.8 },
        { name: '未核实客户身份信息', value: 0, count: 76, percentage: 12.1 },
        { name: '转接前未告知客户', value: 0, count: 45, percentage: 7.2 },
        { name: '重复询问相同问题', value: 0, count: 34, percentage: 5.4 },
        { name: '未提供解决方案', value: 0, count: 28, percentage: 4.5 },
        { name: '语速过快影响理解', value: 0, count: 23, percentage: 3.7 },
        { name: '未记录客户关键信息', value: 0, count: 19, percentage: 3.0 }
    ], []);

    // 模拟复核后严重错误规则数据
    const reviewedSeriousErrors: RankingItem[] = useMemo(() => [
        { name: '泄露客户隐私信息', value: 0, count: 12, percentage: 31.6 },
        { name: '提供错误的产品信息', value: 0, count: 9, percentage: 23.7 },
        { name: '未经授权承诺优惠', value: 0, count: 7, percentage: 18.4 },
        { name: '恶意中断客户通话', value: 0, count: 4, percentage: 10.5 },
        { name: '使用不当言辞', value: 0, count: 3, percentage: 7.9 },
        { name: '违反合规操作流程', value: 0, count: 2, percentage: 5.3 },
        { name: '未经客户同意录音', value: 0, count: 1, percentage: 2.6 },
        { name: '擅自修改客户资料', value: 0, count: 0, percentage: 0 },
        { name: '恶意诱导客户消费', value: 0, count: 0, percentage: 0 },
        { name: '泄露公司机密信息', value: 0, count: 0, percentage: 0 }
    ], []);

    // 模拟最大差异规则排行数据
    const maxDiscrepancyRules: RankingItem[] = useMemo(() => [
        { name: '服务热情度评估', value: 0, count: 234, percentage: 42.1 },
        { name: '语音语调判断', value: 0, count: 198, percentage: 35.6 },
        { name: '客户满意度识别', value: 0, count: 167, percentage: 30.0 },
        { name: '情绪安抚效果', value: 0, count: 145, percentage: 26.1 },
        { name: '专业知识掌握', value: 0, count: 123, percentage: 22.1 },
        { name: '问题解决能力', value: 0, count: 98, percentage: 17.6 },
        { name: '沟通技巧运用', value: 0, count: 87, percentage: 15.6 },
        { name: '时间管理效率', value: 0, count: 76, percentage: 13.7 },
        { name: '规范流程执行', value: 0, count: 65, percentage: 11.7 },
        { name: '信息记录完整性', value: 0, count: 54, percentage: 9.7 }
    ], []);

    // 模拟复核员一致性数据
    const reviewerConsistencyData: ReviewerConsistencyData[] = useMemo(() => [
        { reviewer: '张主管', reviewCount: 456, avgScore: 87.2, correctionRate: 21.3 },
        { reviewer: '李主管', reviewCount: 423, avgScore: 89.1, correctionRate: 24.7 },
        { reviewer: '王主管', reviewCount: 398, avgScore: 85.8, correctionRate: 19.8 },
        { reviewer: '刘主管', reviewCount: 367, avgScore: 88.4, correctionRate: 26.1 },
        { reviewer: '陈主管', reviewCount: 342, avgScore: 86.9, correctionRate: 22.9 },
        { reviewer: '赵主管', reviewCount: 298, avgScore: 90.3, correctionRate: 28.4 },
        { reviewer: '孙主管', reviewCount: 276, avgScore: 84.7, correctionRate: 18.2 },
        { reviewer: '周主管', reviewCount: 287, avgScore: 87.8, correctionRate: 23.6 }
    ], []);

    // 模拟分数调整分析数据
    const scoreAdjustmentData = useMemo(() => [
        { type: '分数调增', count: 789, percentage: 27.7, avgAdjustment: '+5.3' },
        { type: '分数调减', count: 456, percentage: 16.0, avgAdjustment: '-4.8' },
        { type: '分数不变', count: 1602, percentage: 56.3, avgAdjustment: '0' }
    ], []);

    // 模拟典型通话记录数据
    const typicalCallRecords: CallRecord[] = useMemo(() => [
        {
            id: 'CALL001',
            customer: '138****1234',
            agent: '张小明',
            duration: '05:23',
            aiScore: 78,
            reviewScore: 85,
            difference: 7,
            reviewer: '李主管'
        },
        {
            id: 'CALL002',
            customer: '139****5678',
            agent: '李小红',
            duration: '03:45',
            aiScore: 92,
            reviewScore: 88,
            difference: -4,
            reviewer: '张主管'
        },
        {
            id: 'CALL003',
            customer: '137****9012',
            agent: '王小强',
            duration: '07:12',
            aiScore: 65,
            reviewScore: 72,
            difference: 7,
            reviewer: '王主管'
        },
        {
            id: 'CALL004',
            customer: '136****3456',
            agent: '刘小芳',
            duration: '04:56',
            aiScore: 89,
            reviewScore: 82,
            difference: -7,
            reviewer: '刘主管'
        },
        {
            id: 'CALL005',
            customer: '135****7890',
            agent: '陈小华',
            duration: '06:34',
            aiScore: 76,
            reviewScore: 84,
            difference: 8,
            reviewer: '陈主管'
        }
    ], []);

    // Filter fields configuration
    const filterFields = [
        {
            key: 'timeRange',
            label: '时间范围',
            type: 'select' as const,
            value: timeRange,
            options: [
                { value: '今日', label: '今日' },
                { value: '本周', label: '本周' },
                { value: '本月', label: '本月' },
                { value: '自定义', label: '自定义' }
            ],
            onChange: setTimeRange
        },
        {
            key: 'selectedReviewer',
            label: '复核员',
            type: 'select' as const,
            value: selectedReviewer,
            options: [
                { value: '全部复核员', label: '全部复核员' },
                { value: '张主管', label: '张主管' },
                { value: '李主管', label: '李主管' },
                { value: '王主管', label: '王主管' },
                { value: '刘主管', label: '刘主管' }
            ],
            onChange: setSelectedReviewer
        },
        {
            key: 'selectedTeam',
            label: '被检班组/团队',
            type: 'select' as const,
            value: selectedTeam,
            options: [
                { value: '全部班组', label: '全部班组' },
                { value: 'A班组', label: 'A班组' },
                { value: 'B班组', label: 'B班组' },
                { value: 'C班组', label: 'C班组' },
                { value: 'D班组', label: 'D班组' }
            ],
            onChange: setSelectedTeam
        },
        {
            key: 'reviewReason',
            label: '触发复核原因',
            type: 'select' as const,
            value: reviewReason,
            options: [
                { value: '全部原因', label: '全部原因' },
                { value: '低分触发', label: '低分触发' },
                { value: '关键词触发', label: '关键词触发' },
                { value: '随机抽检', label: '随机抽检' },
                { value: '申诉复核', label: '申诉复核' }
            ],
            onChange: setReviewReason
        }
    ];



    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
            <UnifiedPageHeader
                title="复核工作分析报告"
                subtitle="从工作量、复核结果、AI差异三个核心维度，全面评估和监控复核团队的工作状态和质量"
                icon={UserCheck}
                badge={{ text: "复核分析", color: "green" }}
                actions={[
                    {
                        label: "刷新数据",
                        icon: RefreshCw,
                        variant: "secondary",
                        onClick: () => console.log('刷新数据')
                    },
                    {
                        label: "导出报表",
                        icon: Download,
                        variant: "primary",
                        onClick: () => console.log('导出报表')
                    }
                ]}
                showDesignGuide={true}
                designGuideContent={designGuideContent}
            />

            <main className="p-6 space-y-8">
                {/* Enhanced Filter Section */}
                <EnhancedFilterSection
                    fields={filterFields}
                    onSearch={() => console.log('搜索')}
                    onReset={() => {
                        setTimeRange('本月');
                        setSelectedReviewer('全部复核员');
                        setSelectedTeam('全部班组');
                        setReviewReason('全部原因');
                    }}
                />

                {/* Enhanced KPI Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {kpiData.map((kpi, index) => (
                        <EnhancedKPICard
                            key={index}
                            delay={index * 0.1}
                            {...kpi}
                        />
                    ))}
                </div>

                {/* 复核工作量概览 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    {/* 复核任务完成趋势 */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-200"
                    >
                        <div className="flex items-center gap-3 mb-6">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <BarChart3 className="w-5 h-5 text-blue-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-slate-900">复核任务完成趋势</h3>
                        </div>
                        
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart
                                data={reviewTrendData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                            >
                                <defs>
                                    <linearGradient id="blueGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.9} />
                                        <stop offset="100%" stopColor="#1d4ed8" stopOpacity={0.7} />
                                    </linearGradient>
                                </defs>
                                <CartesianGrid
                                    strokeDasharray="3 3"
                                    stroke={chartTheme.grid.stroke}
                                    strokeWidth={chartTheme.grid.strokeWidth}
                                    opacity={0.6}
                                />
                                <XAxis
                                    dataKey="date"
                                    tick={chartTheme.axis.tick}
                                    tickLine={chartTheme.axis.tickLine}
                                    axisLine={chartTheme.axis.axisLine}
                                />
                                <YAxis
                                    tick={chartTheme.axis.tick}
                                    tickLine={chartTheme.axis.tickLine}
                                    axisLine={chartTheme.axis.axisLine}
                                    label={{
                                        value: '复核量',
                                        angle: -90,
                                        position: 'insideLeft',
                                        style: chartTheme.axis.label
                                    }}
                                />
                                <Tooltip
                                    content={<EnhancedTooltip
                                        labelFormatter={(label: string) => `日期: ${label}`}
                                        valueFormatter={(value: any, name?: string) => {
                                            return `${value}通`;
                                        }}
                                    />}
                                    cursor={{
                                        stroke: chartTheme.tooltip.cursor.stroke,
                                        strokeWidth: chartTheme.tooltip.cursor.strokeWidth,
                                        strokeDasharray: chartTheme.tooltip.cursor.strokeDasharray
                                    }}
                                />
                                <Bar
                                    dataKey="reviewCount"
                                    fill="url(#blueGradient)"
                                    radius={chartTypeConfigs.bar.radius as [number, number, number, number]}
                                    maxBarSize={chartTypeConfigs.bar.maxBarSize}
                                />
                            </BarChart>
                        </ResponsiveContainer>
                    </motion.div>

                    {/* 复核后成绩分布 */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-200"
                    >
                        <div className="flex items-center gap-3 mb-6">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <Award className="w-5 h-5 text-green-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-slate-900">复核后成绩分布</h3>
                        </div>

                        {/* 重新布局：左侧饼图，右侧图例 */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                            {/* 饼图部分 */}
                            <div className="flex justify-center">
                                <ResponsiveContainer width="100%" height={320}>
                                    <PieChart>
                                        <Pie
                                            data={scoreDistributionData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={60}
                                            outerRadius={120}
                                            paddingAngle={3}
                                            cornerRadius={6}
                                            dataKey="count"
                                        >
                                            {scoreDistributionData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip
                                            content={({ active, payload }) => {
                                                if (active && payload && payload.length) {
                                                    const data = payload[0].payload;
                                                    return (
                                                        <div className="bg-white/98 backdrop-blur-md border border-slate-200/60 rounded-2xl shadow-2xl shadow-slate-900/10 p-4 min-w-[200px]">
                                                            <div className="text-sm font-semibold text-slate-700 mb-3 pb-2 border-b border-slate-100">
                                                                成绩分布详情
                                                            </div>
                                                            <div className="space-y-2">
                                                                <div className="flex items-center justify-between gap-3">
                                                                    <div className="flex items-center gap-2">
                                                                        <div
                                                                            className="w-3 h-3 rounded-full"
                                                                            style={{ backgroundColor: data.color }}
                                                                        />
                                                                        <span className="text-sm font-medium text-slate-600">
                                                                            分数区间
                                                                        </span>
                                                                    </div>
                                                                    <span className="text-sm font-bold text-slate-900">
                                                                        {data.range}
                                                                    </span>
                                                                </div>
                                                                <div className="flex items-center justify-between gap-3">
                                                                    <span className="text-sm font-medium text-slate-600">
                                                                        复核数量
                                                                    </span>
                                                                    <span className="text-sm font-bold text-slate-900">
                                                                        {data.count}通
                                                                    </span>
                                                                </div>
                                                                <div className="flex items-center justify-between gap-3">
                                                                    <span className="text-sm font-medium text-slate-600">
                                                                        占比
                                                                    </span>
                                                                    <span className="text-sm font-bold text-slate-900">
                                                                        {data.percentage}%
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div className="mt-3 pt-2 border-t border-slate-100">
                                                                <div className="flex items-center justify-center">
                                                                    <div className="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    );
                                                }
                                                return null;
                                            }}
                                        />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>

                            {/* 图例和数据部分 */}
                            <div className="space-y-6">
                                {scoreDistributionData.map((item, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, x: 20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: index * 0.1 }}
                                        className="flex items-center justify-between p-4 rounded-lg bg-slate-50/50 hover:bg-slate-50 transition-colors duration-200"
                                    >
                                        <div className="flex items-center gap-4">
                                            <div
                                                className="w-6 h-6 rounded-lg shadow-sm"
                                                style={{ backgroundColor: item.color }}
                                            />
                                            <div>
                                                <div className="text-sm font-medium text-slate-700">{item.range}</div>
                                                <div className="text-xs text-slate-500">分数区间</div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-lg font-bold text-slate-900">
                                                {item.count}通
                                            </div>
                                            <div className="text-sm text-slate-500">
                                                占比 {item.percentage}%
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}

                                {/* 总计信息 */}
                                <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm font-medium text-blue-700">总计</div>
                                        <div className="text-lg font-bold text-blue-900">
                                            {scoreDistributionData.reduce((sum, item) => sum + item.count, 0)}通
                                        </div>
                                    </div>
                                    <div className="text-xs text-blue-600 mt-1">
                                        复核任务总数
                                    </div>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Enhanced Review Results Analysis */}
                <div className="space-y-8">
                    <EnhancedSectionHeader
                        title="复核结果分析"
                        subtitle="深度分析复核后的质检结果和问题分布"
                        icon={AlertTriangle}
                        iconColor="text-red-600"
                        iconBgColor="bg-gradient-to-br from-red-100 to-rose-100"
                        delay={0.3}
                    />

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <EnhancedRankingCard
                            title="复核后高频失分规则 TOP 10"
                            subtitle="经人工复核确认的主要问题"
                            data={reviewedProblematicRules}
                            type="rules"
                            icon={AlertTriangle}
                            iconColor="text-red-600"
                            iconBgColor="bg-gradient-to-br from-red-100 to-rose-100"
                            delay={0.4}
                        />

                        <EnhancedRankingCard
                            title="复核后严重错误规则 TOP 10"
                            subtitle="需要重点关注的严重违规"
                            data={reviewedSeriousErrors}
                            type="rules"
                            icon={XCircle}
                            iconColor="text-orange-600"
                            iconBgColor="bg-gradient-to-br from-orange-100 to-amber-100"
                            delay={0.5}
                        />
                    </div>
                </div>

                {/* AI与人工复核差异洞察 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    {/* 分数调整分析 */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-200"
                    >
                        <div className="flex items-center gap-3 mb-6">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <TrendingUp className="w-5 h-5 text-purple-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-slate-900">分数调整分析</h3>
                        </div>
                        
                        <div className="space-y-4">
                            {scoreAdjustmentData.map((item, index) => (
                                <div key={index} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className={`w-3 h-3 rounded-full ${
                                            item.type === '分数调增' ? 'bg-green-500' :
                                            item.type === '分数调减' ? 'bg-red-500' : 'bg-slate-400'
                                        }`} />
                                        <span className="text-sm font-medium text-slate-700">{item.type}</span>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-sm font-medium text-slate-900">
                                            {item.count}通 ({item.percentage}%)
                                        </div>
                                        <div className="text-xs text-slate-500">
                                            平均调整: {item.avgAdjustment}分
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </motion.div>

                    {/* Enhanced Maximum Discrepancy Rules Ranking */}
                    <EnhancedRankingCard
                        title="最大差异规则排行 TOP 10"
                        subtitle="AI与人工复核分歧最大的规则"
                        data={maxDiscrepancyRules}
                        type="rules"
                        icon={Brain}
                        iconColor="text-purple-600"
                        iconBgColor="bg-gradient-to-br from-purple-100 to-violet-100"
                        delay={0.6}
                    />
                </div>

                {/* 复核员一致性快照 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white rounded-xl shadow-sm border border-slate-200/60 p-6 mb-8 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-200"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-indigo-100 rounded-lg">
                            <Users className="w-5 h-5 text-indigo-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-slate-900">复核员一致性快照</h3>
                    </div>
                    
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b border-slate-200">
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">复核员</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">复核量</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">平均复核分数</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">AI结果纠错率</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">一致性评估</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reviewerConsistencyData.map((reviewer, index) => (
                                    <tr key={index} className="border-b border-slate-100 hover:bg-slate-50">
                                        <td className="py-3 px-4 font-medium text-slate-900">{reviewer.reviewer}</td>
                                        <td className="text-center py-3 px-4 text-slate-600">{reviewer.reviewCount}通</td>
                                        <td className="text-center py-3 px-4">
                                            <span className={`font-medium ${
                                                reviewer.avgScore >= 88 ? 'text-green-600' :
                                                reviewer.avgScore >= 85 ? 'text-yellow-600' : 'text-red-600'
                                            }`}>
                                                {reviewer.avgScore}分
                                            </span>
                                        </td>
                                        <td className="text-center py-3 px-4">
                                            <span className={`font-medium ${
                                                reviewer.correctionRate <= 20 ? 'text-green-600' :
                                                reviewer.correctionRate <= 25 ? 'text-yellow-600' : 'text-red-600'
                                            }`}>
                                                {reviewer.correctionRate}%
                                            </span>
                                        </td>
                                        <td className="text-center py-3 px-4">
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                reviewer.avgScore >= 88 && reviewer.correctionRate <= 25 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : reviewer.avgScore >= 85 && reviewer.correctionRate <= 30
                                                    ? 'bg-yellow-100 text-yellow-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {
                                                    reviewer.avgScore >= 88 && reviewer.correctionRate <= 25 
                                                        ? '优秀' 
                                                        : reviewer.avgScore >= 85 && reviewer.correctionRate <= 30
                                                        ? '良好'
                                                        : '需关注'
                                                }
                                            </span>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </motion.div>

                {/* 典型通话记录 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-200"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-orange-100 rounded-lg">
                            <Eye className="w-5 h-5 text-orange-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-slate-900">典型差异通话记录</h3>
                        <span className="text-sm text-slate-500">- AI与人工复核分数差异较大的通话</span>
                    </div>
                    
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b border-slate-200">
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">通话ID</th>
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">客户</th>
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">坐席</th>
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">通话时长</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">AI初检分数</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">复核分数</th>
                                    <th className="text-center py-3 px-4 font-medium text-slate-700">分数差异</th>
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">复核员</th>
                                    <th className="text-left py-3 px-4 font-medium text-slate-700">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {typicalCallRecords.map((call, index) => (
                                    <tr key={index} className="border-b border-slate-100 hover:bg-slate-50">
                                        <td className="py-3 px-4 font-medium text-blue-600">{call.id}</td>
                                        <td className="py-3 px-4 text-slate-900">{call.customer}</td>
                                        <td className="py-3 px-4 text-slate-900">{call.agent}</td>
                                        <td className="py-3 px-4 text-slate-600">{call.duration}</td>
                                        <td className="text-center py-3 px-4">
                                            <span className={`font-medium ${
                                                call.aiScore >= 90 ? 'text-green-600' :
                                                call.aiScore >= 80 ? 'text-yellow-600' : 'text-red-600'
                                            }`}>
                                                {call.aiScore}分
                                            </span>
                                        </td>
                                        <td className="text-center py-3 px-4">
                                            <span className={`font-medium ${
                                                call.reviewScore >= 90 ? 'text-green-600' :
                                                call.reviewScore >= 80 ? 'text-yellow-600' : 'text-red-600'
                                            }`}>
                                                {call.reviewScore}分
                                            </span>
                                        </td>
                                        <td className="text-center py-3 px-4">
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                call.difference > 0 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : call.difference < 0
                                                    ? 'bg-red-100 text-red-800'
                                                    : 'bg-slate-100 text-slate-800'
                                            }`}>
                                                {call.difference > 0 ? '+' : ''}{call.difference}分
                                            </span>
                                        </td>
                                        <td className="py-3 px-4 text-slate-900">{call.reviewer}</td>
                                        <td className="py-3 px-4">
                                            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                查看详情
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    
                    <UnifiedPagination
                        current={1}
                        pageSize={5}
                        total={89}
                        onChange={() => {}}
                    />
                </motion.div>
            </main>
        </div>
    );
};

export default ReviewAnalysisReportPage;