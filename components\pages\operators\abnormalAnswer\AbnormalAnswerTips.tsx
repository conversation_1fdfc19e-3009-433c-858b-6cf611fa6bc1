import React from 'react';

/**
 * 非正常接听提示组件
 * @constructor
 */
const AbnormalAnswerTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">使用提示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>
      <div className="space-y-4">
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <h4 className="font-semibold text-gray-800 mb-2">数据格式</h4>
          <p className="text-xs text-gray-600">
            测试文本需要包含 `[mm:ss]` 格式的时间戳，系统将自动解析第一句话的时间。
          </p>
        </div>
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <h4 className="font-semibold text-gray-800 mb-2">配置技巧</h4>
          <p className="text-xs text-gray-600">
            - **检查客服响应慢**: 设置 `时间 &gt; 5秒` 且 `角色` 为 `客服`。<br />
            - **确保客服先开口**: 设置 `角色` 为 `客服`，如果客户先说，即使时间符合条件也不会命中。
          </p>
        </div>
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <h4 className="font-semibold text-gray-800 mb-2">业务场景建议</h4>
          <p className="text-xs text-gray-600">
            对于呼入场景，建议设置一个较短的响应时间（如3-5秒）来考核客服。对于呼出场景，此规则意义不大。
          </p>
        </div>
      </div>
    </div>
  );
};

export default AbnormalAnswerTips; 