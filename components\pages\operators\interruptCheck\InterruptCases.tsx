import React from 'react';

interface InterruptCasesProps {
  onLoadCase: (config: any, text: string) => void;
}

/**
 * 抢话检查案例库组件
 */
const InterruptCases: React.FC<InterruptCasesProps> = ({ onLoadCase }) => {
  // 预设案例
  const cases = [
    {
      name: '客服抢话检测',
      description: '检测客服是否在客户说话时出现抢话现象',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        interruptTime: 2000,
        minWordCount: 4,
        delayTime: 0
      },
      testText: `[00:01] 客户：您好，我想咨询一下你们的产品
[00:02] 客服：好的，我来为您介绍一下我们的产品特点
[00:03] 客户：我主要想了解
[00:04] 客服：我们的产品有以下几个优势`,
      expectedResult: '应该命中（客服在客户未说完时就开始说话）'
    },
    {
      name: '延时判定检测',
      description: '使用延时判定来过滤短暂的语音重叠',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        interruptTime: 2500,
        minWordCount: 4,
        delayTime: 1000
      },
      testText: `[00:01] 客服：请问您需要什么帮助
[00:02] 客户：我想问一下
[00:03] 客服：抱歉打断您，我们现在有优惠活动`,
      expectedResult: '应该命中（说话重叠超过延时判定阈值）'
    },
    {
      name: '字数限制检测',
      description: '检测是否满足最小字数要求',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        interruptTime: 2000,
        minWordCount: 6,
        delayTime: 0
      },
      testText: `[00:01] 客户：我想了解产品价格
[00:02] 客服：好的
[00:03] 客户：具体是
[00:04] 客服：我们的产品价格是这样的`,
      expectedResult: '不应命中（抢话句子未达到最小字数要求）'
    },
    {
      name: '指定范围检测',
      description: '在对话的特定范围内检测抢话',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'range' as const,
        rangeStart: 2,
        rangeEnd: 4,
        interruptTime: 2000,
        minWordCount: 4,
        delayTime: 0
      },
      testText: `[00:01] 客服：您好，欢迎咨询
[00:02] 客户：我想了解一下
[00:03] 客服：好的，让我来为您介绍
[00:04] 客户：等一下，我还没说完
[00:05] 客服：抱歉，您请说`,
      expectedResult: '应该命中（指定范围内检测到抢话）'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
          <span>📂</span>
          <span>案例库</span>
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          参考示例
        </span>
      </div>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200 hover:border-blue-300 transition-colors">
            <div className="flex justify-between items-start">
              <div className="space-y-1">
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500">{c.description}</p>
                <p className="text-xs text-blue-500">预期结果：{c.expectedResult}</p>
              </div>
              <button 
                onClick={() => onLoadCase(c.config, c.testText)}
                className="flex-shrink-0 text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InterruptCases; 