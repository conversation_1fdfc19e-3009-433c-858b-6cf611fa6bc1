import React from 'react';

interface CallSilenceConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  silenceLogic: 'different' | 'any' | 'same';
  timeComparison: 'greater' | 'less' | 'between';
  timeValue: number;
  timeValueEnd: number;
}

interface SilenceSegment {
  startTime: number;
  endTime: number;
  duration: number;
  previousRole: string;
  nextRole: string;
  previousSentence: string;
  nextSentence: string;
}

interface TestResult {
  matched: boolean;
  silenceSegments: SilenceSegment[];
  matchedSegments: SilenceSegment[];
  details: string;
}

interface CallSilenceTesterProps {
  config: CallSilenceConfig;
  testText: string;
  setTestText: (text: string) => void;
  testResult: TestResult | null;
  onTest: () => void;
  onClearResult: () => void;
}

/**
 * 通话静音检查测试组件
 * 提供测试文本输入和结果显示
 */
const CallSilenceTester: React.FC<CallSilenceTesterProps> = ({ 
  config, 
  testText, 
  setTestText, 
  testResult, 
  onTest, 
  onClearResult 
}) => {
  const getConfigSummary = () => {
    const role = config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色';
    const logic = config.silenceLogic === 'different' ? '不同角色间' : config.silenceLogic === 'any' ? '不区分角色' : '相同角色';
    const comparison = config.timeComparison === 'greater' ? '大于' : config.timeComparison === 'less' ? '小于' : '区间';
    const timeDisplay = config.timeComparison === 'between' ? ` ${config.timeValue}-${config.timeValueEnd}秒` : ` ${config.timeValue}秒`;
    return { role, logic, comparison, timeDisplay };
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              setTestText('[00:05-00:12] 客服：您好，欢迎来电咨询\n[00:13-00:18] 客户：我想了解产品价格\n[00:22-00:30] 客服：好的，我来为您详细介绍\n[00:31-00:35] 客户：明白了，谢谢');
              onClearResult();
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* 测试文本输入区 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
              📝 测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {testText.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              id="test-text"
              rows={10}
              className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="请输入包含时间戳的对话文本...&#10;&#10;格式示例：&#10;[00:05-00:12] 客服：您好，欢迎来电咨询&#10;[00:15-00:20] 客户：我想了解产品价格&#10;[00:25-00:30] 客服：好的，我来为您介绍"
            />
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-4">
            <span>💡 提示：使用格式 [开始时间-结束时间] 角色：内容</span>
            <span>🔹 时间格式：[00:05-00:12] 或 [5-12]</span>
          </div>
        </div>

        {/* 快速测试按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={onTest}
            disabled={!testText.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>执行测试</span>
            </span>
          </button>
          {testResult && (
            <button
              onClick={onClearResult}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>
      </div>
      
      {/* 测试结果显示 */}
      {testResult && (
        <div className="mt-8 space-y-6">
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2 mb-4">
              <span>📊</span>
              <span>测试结果</span>
            </h3>
            
            {/* 主要结果卡片 */}
            <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                      {testResult.matched ? '✓' : '✗'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                      {testResult.matched ? '规则命中' : '规则未命中'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 详细统计 */}
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">🔇 静音段统计</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>总静音段数:</span>
                    <span className="font-medium">{testResult.silenceSegments.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>命中静音段数:</span>
                    <span className="font-medium">{testResult.matchedSegments.length}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">⚙️ 当前配置</h4>
                <div className="space-y-1 text-xs">
                  {(() => {
                    const summary = getConfigSummary();
                    return (
                      <>
                        <div>检测角色: {summary.role}</div>
                        <div>检查逻辑: {summary.logic}</div>
                        <div>时间条件: {summary.comparison}{summary.timeDisplay}</div>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CallSilenceTester; 