import React from 'react';
import { Card, Typography } from 'antd';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';
import { MetricHelpButton, MetricDescription } from './MetricHelpButton';

const { Title, Text } = Typography;

/**
 * 趋势类型
 */
export type TrendType = 'up' | 'down' | 'stable';

/**
 * 指标卡片属性接口
 */
interface MetricCardProps {
  /** 指标标题 */
  title: string;
  /** 指标值 */
  value: string | number;
  /** 图标组件 */
  icon: LucideIcon;
  /** 图标颜色 */
  iconColor: string;
  /** 图标背景颜色 */
  iconBgColor: string;
  /** 值的颜色 */
  valueColor: string;
  /** 趋势信息 */
  trend?: {
    type: TrendType;
    value: string;
    text: string;
  };
  /** 指标说明（可选） */
  helpInfo?: MetricDescription;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取趋势图标和颜色
 */
const getTrendDisplay = (trend: { type: TrendType; value: string; text: string }) => {
  const isPositive = trend.type === 'up';
  const isNegative = trend.type === 'down';
  
  if (trend.type === 'stable') {
    return {
      icon: null,
      color: 'text-gray-500',
      text: trend.text
    };
  }
  
  return {
    icon: isPositive ? TrendingUp : TrendingDown,
    color: isPositive ? 'text-green-500' : 'text-red-500',
    text: trend.text
  };
};

/**
 * 统一的指标卡片组件
 * 提供标准化的指标展示布局和样式
 */
export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon: Icon,
  iconColor,
  iconBgColor,
  valueColor,
  trend,
  helpInfo,
  className = ''
}) => {
  const trendDisplay = trend ? getTrendDisplay(trend) : null;
  const TrendIcon = trendDisplay?.icon;

  return (
    <Card className={`border-0 shadow-sm hover:shadow-md transition-shadow duration-200 ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Text className="text-sm text-gray-600 font-medium truncate">{title}</Text>
              {helpInfo && <MetricHelpButton metric={helpInfo} />}
            </div>
            <Title level={2} className={`!mb-0 ${valueColor} truncate`}>
              {value}
            </Title>
          </div>
          <div className={`w-12 h-12 ${iconBgColor} rounded-lg flex items-center justify-center flex-shrink-0 ml-4`}>
            <Icon className={`w-6 h-6 ${iconColor}`} />
          </div>
        </div>
        
        {/* 趋势信息 */}
        {trendDisplay && (
          <div className="flex items-center mt-3 text-sm">
            {TrendIcon && <TrendIcon className={`w-4 h-4 mr-1 ${trendDisplay.color}`} />}
            <Text className={trendDisplay.color}>{trendDisplay.text}</Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default MetricCard; 