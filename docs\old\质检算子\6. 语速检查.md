### “语速检查”算子核心用途

**语速检查**的核心功能是自动识别并标记出对话中**语速过快**或**语速过慢**的片段。这对于规范服务专业性、管理坐席情绪以及洞察客户状态非常有帮助。

*   **语速过快**：可能显得急躁、不耐烦，或者像在背书，让客户难以听清和理解关键信息。
*   **语速过慢**：可能显得懒散、不专业、缺乏自信，或者在故意拖延时间。

### 主要应用场景及示例

---

#### 场景一：规范客服专业形象（语速过快）

这是最常见的应用，确保客服在任何情况下都保持从容、清晰的表达。

*   **目标**：防止客服在介绍产品或宣读条款时，因过于熟练而语速飞快，导致客户无法接收信息。
*   **配置思路**：
    *   **检测角色**：客服
    *   **检查逻辑**：语速每分钟 **超过 300 字** 时检出
    *   **检查方式**：检测单句话的语速
*   **具体示例**：
    *   **不当语速**：客服用极快速度说出：“本次通话可能会被录音为了保证您的服务质量您的手机尾号是xxxx对吗我们需要和您核对下信息……”
    *   **适当语速**：“先生您好，为了保证服务质量，本次通话可能会被录音。接下来，我需要和您核对一下信息，请问您手机的尾号是xxxx吗？”
*   **说明**：当客服的语速超过设定的阈值（例如每分钟300字），系统就会自动标记。质检主管可以快速定位到这些“赶场式”的服务片段，进行针对性辅导，要求其在宣读重要信息时必须放慢语速，确保客户听得懂。

---

#### 场景二：识别客服的负面情绪或不自信（语速过慢）

语速异常放缓，往往是客服本人状态不佳的信号。

*   **目标**：发现那些可能因疲劳、不自信或对业务不熟而导致服务状态不佳的客服。
*   **配置思路**：
    *   **检测角色**：客服
    *   **检查逻辑**：语速每分钟 **低于 120 字** 时检出
    *   **检查方式**：检测整个对话的平均语速
*   **具体示例**：
    *   一个通常语速正常的客服，在某一通电话里平均语速异常地低。复盘录音后发现，客户咨询了一个非常规的复杂业务，客服全程回答得磕磕巴巴，非常缓慢，明显是对此业务不熟悉，缺乏自信。
*   **说明**：通过监控平均语速，管理者可以发现那些“状态不对”的客服。这不一定是为了处罚，更多是为了关怀和培训。比如，可以发现某位员工可能需要心理疏导，或者某个新业务的培训材料没有被大家充分掌握。

---

#### 场景三：洞察客户的情绪状态

客户的语速同样是判断其情绪的重要依据。

*   **目标**：快速识别情绪激动或愤怒的客户。
*   **配置思路**：
    *   **检测角色**：客户
    *   **检查逻辑**：语速每分钟 **超过 350 字** 时检出
    *   **检查方式**：检测单句话的语速
*   **具体示例**：
    *   客户非常生气，用极快的语速说：“你们怎么搞的我都投诉八遍了还没人解决问题是不是非要我上新闻你们才管！”
*   **说明**：客户在情绪激动时，语速通常会不自觉地加快。通过这个规则，可以自动为通话打上“客户情绪激动”的标签，便于质检员优先复核，或者用于统计分析，了解哪些业务问题最容易引发客户的激烈反应。