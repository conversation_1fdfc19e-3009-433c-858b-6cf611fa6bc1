import React, { useState, useEffect } from 'react';

/**
 * 关键词检查详细演示页面
 * 提供关键词检查算子的完整学习体验
 */
const KeywordCheckPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState({
    detectionRole: 'agent' as 'agent' | 'customer' | 'all',
    detectionScope: 'full' as 'full' | 'range',
    rangeStart: 1,
    rangeEnd: 5,
    keywords: ['您好', '欢迎'],
    analysisMethod: 'single' as 'single' | 'multiple',
    detectionType: 'any' as 'any' | 'all' | 'count' | 'none',
    keywordCount: 2,
    singleSentence: false,
    limitHitCount: false,
    hitCount: 1
  });

  // 当分析方法或关键词数量变化时，检查"包含任意N个关键词"选项的可用性
  useEffect(() => {
    // 如果当前选中"包含任意N个关键词"但在单句分析且关键词<=1时，自动切换到"包含任意一个关键词"
    if (config.detectionType === 'count' && 
        config.analysisMethod === 'single' && 
        config.keywords.length <= 1) {
      setConfig(prev => ({ ...prev, detectionType: 'any' }));
    }
  }, [config.analysisMethod, config.keywords.length, config.detectionType]);

  // 测试文本
  const [testText, setTestText] = useState(
    '客服：您好，欢迎来到我们公司，我是客服小王，很高兴为您服务。\n客户：你好，我想咨询一下你们的产品。\n客服：好的，请问您想了解哪方面的产品呢？'
  );

  // 测试结果
  const [testResult, setTestResult] = useState<{
    matched: boolean;
    matchedKeywords: string[];
    matchedSentences: string[];
    hitCount: number;
    details: string;
  } | null>(null);

  // 预设案例
  const cases = [
    {
      name: '客服欢迎语检测',
      description: '检测客服是否使用标准欢迎语（指定范围）',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'range' as const,
        rangeStart: 1,
        rangeEnd: 2,
        keywords: ['您好', '欢迎', '很高兴'],
        analysisMethod: 'single' as const,
        detectionType: 'any' as const,
        singleSentence: false,
        limitHitCount: false,
        keywordCount: 1,
        hitCount: 1
      },
      testText: '客服：您好，欢迎来到我们公司，我是客服小王。\n客户：你好。\n客服：请问有什么可以帮助您的吗？',
      expectedResult: '应该命中（前2句中检测到"您好"和"欢迎"）'
    },
    {
      name: '多句分析术语检测',
      description: '多句分析检测是否包含全部业务术语',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        keywords: ['产品', '服务', '方案'],
        analysisMethod: 'multiple' as const,
        detectionType: 'all' as const,
        singleSentence: false,
        limitHitCount: false,
        keywordCount: 3,
        hitCount: 1
      },
      testText: '客服：我们有优质的产品。\n客服：提供专业的服务。\n客服：为您制定最佳方案。\n客户：好的，我了解了。',
      expectedResult: '应该命中（多句合并后包含所有关键词）'
    },
    {
      name: '单句话内生效检测',
      description: '检测单句话内是否同时包含多个关键词',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        keywords: ['优质', '专业'],
        analysisMethod: 'single' as const,
        detectionType: 'all' as const,
        singleSentence: true,
        limitHitCount: false,
        keywordCount: 2,
        hitCount: 1
      },
      testText: '客服：我们提供优质，专业的服务。\n客服：优质的产品和专业的团队。\n客户：不错。',
      expectedResult: '应该命中（第二句在逗号分隔的子句内同时包含"优质"和"专业"）'
    },
    {
      name: '限定命中次数检测',
      description: '检测关键词累计出现次数',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        keywords: ['您好', '好的'],
        analysisMethod: 'multiple' as const,
        detectionType: 'any' as const,
        singleSentence: false,
        limitHitCount: true,
        keywordCount: 1,
        hitCount: 3
      },
      testText: '客服：您好，请问有什么需要帮助的？\n客户：我想咨询产品。\n客服：好的，您好，请稍等。\n客服：好的，为您介绍。',
      expectedResult: '应该命中（"您好"出现2次，"好的"出现2次，总计4次≥3次）'
    },
    {
      name: '包含任意N个关键词',
      description: '检测至少包含N个不同关键词',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        keywords: ['产品', '价格', '优惠', '服务', '质量'],
        analysisMethod: 'multiple' as const,
        detectionType: 'count' as const,
        singleSentence: false,
        limitHitCount: false,
        keywordCount: 3,
        hitCount: 1
      },
      testText: '客户：我想了解你们的产品价格和优惠政策。\n客服：好的，我来为您详细介绍。',
      expectedResult: '应该命中（包含"产品"、"价格"、"优惠"3个关键词≥3个）'
    },
    {
      name: '全部不包含检测',
      description: '检测对话中不包含禁用词汇',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        keywords: ['不知道', '不清楚', '不确定'],
        analysisMethod: 'single' as const,
        detectionType: 'none' as const,
        singleSentence: false,
        limitHitCount: false,
        keywordCount: 0,
        hitCount: 1
      },
      testText: '客服：我来为您查询相关信息。\n客户：好的。\n客服：经过查询，这个产品符合您的需求。',
      expectedResult: '应该命中（客服没有使用"不知道"等消极词汇）'
    }
  ];

  // 执行测试
  const runTest = () => {
    const sentences = testText.split('\n').filter(s => s.trim());
    let targetSentences: string[] = [];

    // 根据检测角色筛选句子
    if (config.detectionRole === 'agent') {
      targetSentences = sentences.filter(s => s.startsWith('客服：'));
    } else if (config.detectionRole === 'customer') {
      targetSentences = sentences.filter(s => s.startsWith('客户：'));
    } else {
      targetSentences = sentences;
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    // 关键词匹配逻辑
    const matchedKeywords: string[] = [];
    const matchedSentences: string[] = [];
    let totalHitCount = 0;

    if (config.analysisMethod === 'single') {
      // 单句分析：逐句检测
      targetSentences.forEach(sentence => {
        let sentenceMatched = false;
        const sentenceKeywords: string[] = [];
        
        if (config.singleSentence) {
          // 单句话内生效：按标点分割小句
          const subSentences = sentence.split(/[，。！？；：,.!?;:""'']/);
          subSentences.forEach(subSentence => {
            const subKeywords: string[] = [];
            config.keywords.forEach(keyword => {
              if (subSentence.includes(keyword)) {
                subKeywords.push(keyword);
                totalHitCount++;
                if (!matchedKeywords.includes(keyword)) {
                  matchedKeywords.push(keyword);
                }
              }
            });
            
            // 检查子句是否满足条件
            let subMatched = false;
            switch (config.detectionType) {
              case 'any':
                subMatched = subKeywords.length > 0;
                break;
              case 'all':
                subMatched = subKeywords.length === config.keywords.length;
                break;
              case 'count':
                subMatched = subKeywords.length >= config.keywordCount;
                break;
              case 'none':
                subMatched = subKeywords.length === 0;
                break;
            }
            
            if (subMatched) {
              sentenceMatched = true;
              sentenceKeywords.push(...subKeywords);
            }
          });
        } else {
          // 整句检测
          config.keywords.forEach(keyword => {
        if (sentence.includes(keyword)) {
              sentenceKeywords.push(keyword);
              totalHitCount++;
          if (!matchedKeywords.includes(keyword)) {
            matchedKeywords.push(keyword);
          }
            }
          });
          
          // 检查整句是否满足条件
          switch (config.detectionType) {
            case 'any':
              sentenceMatched = sentenceKeywords.length > 0;
              break;
            case 'all':
              sentenceMatched = sentenceKeywords.length === config.keywords.length;
              break;
            case 'count':
              sentenceMatched = sentenceKeywords.length >= config.keywordCount;
              break;
            case 'none':
              sentenceMatched = sentenceKeywords.length === 0;
              break;
          }
        }
        
        if (sentenceMatched && !matchedSentences.includes(sentence)) {
            matchedSentences.push(sentence);
          }
      });
    } else {
      // 多句分析：合并所有句子后检测
      const combinedText = targetSentences.join('');
      config.keywords.forEach(keyword => {
        const regex = new RegExp(keyword, 'g');
        const matches = combinedText.match(regex);
        if (matches) {
          totalHitCount += matches.length;
          if (!matchedKeywords.includes(keyword)) {
            matchedKeywords.push(keyword);
          }
          // 找出包含该关键词的句子
          targetSentences.forEach(sentence => {
            if (sentence.includes(keyword) && !matchedSentences.includes(sentence)) {
            matchedSentences.push(sentence);
          }
          });
        }
      });
    }

    // 判断是否命中
    let matched = false;
    switch (config.detectionType) {
      case 'any':
        matched = matchedKeywords.length > 0;
        break;
      case 'all':
        matched = matchedKeywords.length === config.keywords.length;
        break;
      case 'count':
        matched = matchedKeywords.length >= config.keywordCount;
        break;
      case 'none':
        matched = matchedKeywords.length === 0;
        break;
    }

    // 限定命中次数检查
    if (config.limitHitCount && matched) {
      matched = totalHitCount >= config.hitCount;
    }

    let details = '';
    if (matched) {
      details = `规则已命中 - 检测方法：${config.analysisMethod === 'single' ? '单句分析' : '多句分析'}`;
      if (config.limitHitCount) {
        details += `，总命中次数：${totalHitCount}`;
      }
    } else {
      details = `规则未命中 - 检测方法：${config.analysisMethod === 'single' ? '单句分析' : '多句分析'}`;
      if (config.limitHitCount) {
        details += `，总命中次数：${totalHitCount}（需要：${config.hitCount}）`;
      }
    }

    setTestResult({
      matched,
      matchedKeywords,
      matchedSentences,
      hitCount: totalHitCount,
      details
    });
  };

  // 加载案例
  const loadCase = (caseConfig: any, caseText: string) => {
    setConfig({ ...config, ...caseConfig });
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                📝 关键词检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测对话中是否包含指定关键词，支持多种检测模式和分析方法
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                🚀 基础入门
              </span>
              <span className="text-sm text-gray-500">
                算子类型：文字检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-6">
                <span className="text-2xl">💡</span>
                <h2 className="text-xl font-semibold text-gray-900">
                  核心概念
              </h2>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  基础知识
                </span>
              </div>
              
              <div className="space-y-6">
                {/* 功能介绍 */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 text-lg">🎯</span>
                    </div>
                <div>
                      <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
                      <p className="text-gray-700 text-sm leading-relaxed">
                        检测对应角色对话中是否出现了某一个或多个关键词。支持精确匹配、模糊匹配等多种检测策略，
                        可以灵活配置检测范围和分析方法，为质检工作提供强有力的支撑。
                      </p>
                    </div>
                  </div>
                </div>

                {/* 应用场景 */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-lg">🏢</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                          <span className="text-sm text-gray-700">客服标准用语检查</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                          <span className="text-sm text-gray-700">敏感词汇监控</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                          <span className="text-sm text-gray-700">业务术语规范检测</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                          <span className="text-sm text-gray-700">服务质量评估</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 优势特点 */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 text-lg">⭐</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-purple-600 text-xs">✓</span>
                </div>
                <div>
                            <span className="text-sm font-medium text-gray-800">配置简单，易于使用</span>
                            <span className="text-xs text-gray-500 ml-1">- 直观的界面操作</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-purple-600 text-xs">✓</span>
                </div>
                <div>
                            <span className="text-sm font-medium text-gray-800">支持多种检测模式</span>
                            <span className="text-xs text-gray-500 ml-1">- 灵活的匹配策略</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-purple-600 text-xs">✓</span>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-gray-800">实时检测，效率高</span>
                            <span className="text-xs text-gray-500 ml-1">- 毫秒级响应速度</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-purple-600 text-xs">✓</span>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-gray-800">可扩展性强</span>
                            <span className="text-xs text-gray-500 ml-1">- 支持复杂业务场景</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 配置演示 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-6">
                <span className="text-2xl">⚙️</span>
                <h2 className="text-xl font-semibold text-gray-900">
                  配置演示
              </h2>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  交互配置
                </span>
              </div>
              
              {/* 配置规则说明 */}
              <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 text-lg">📋</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="space-y-3">
                      <div>
                          <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                          <ul className="text-gray-600 space-y-1 ml-3">
                            <li>• <strong>客服</strong>：仅检测"客服："开头的对话</li>
                            <li>• <strong>客户</strong>：仅检测"客户："开头的对话</li>
                            <li>• <strong>所有角色</strong>：检测全部对话内容</li>
                          </ul>
                      </div>
                        <div>
                          <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                          <ul className="text-gray-600 space-y-1 ml-3">
                            <li>• <strong>全文</strong>：检测所有符合角色条件的句子</li>
                            <li>• <strong>指定范围</strong>：只检测第X~Y句之间的内容</li>
                          </ul>
                    </div>
                        <div>
                          <h5 className="font-medium text-gray-800 mb-1">🔤 关键词输入</h5>
                          <ul className="text-gray-600 space-y-1 ml-3">
                            <li>• 支持单个输入+回车添加</li>
                            <li>• 支持逗号分隔批量输入</li>
                            <li>• 自动过滤重复关键词</li>
                          </ul>
                  </div>
              </div>
                      <div className="space-y-3">
                        <div>
                          <h5 className="font-medium text-gray-800 mb-1">🔍 分析方式</h5>
                          <ul className="text-gray-600 space-y-1 ml-3">
                            <li>• <strong>单句分析</strong>：逐句检测，每句独立判断</li>
                            <li>• <strong>多句分析</strong>：合并后统一检测</li>
                          </ul>
            </div>
                                                 <div>
                           <h5 className="font-medium text-gray-800 mb-1">🎨 检测类型</h5>
                           <ul className="text-gray-600 space-y-1 ml-3">
                             <li>• <strong>包含任意一个</strong>：任意关键词出现即命中</li>
                             <li>• <strong>包含全部上述</strong>：所有关键词都要出现</li>
                             <li>• <strong>包含任意N个</strong>：至少包含N个不同关键词（单句分析时需关键词数{'>'} 1）</li>
                             <li>• <strong>全部不包含</strong>：所有关键词都不出现</li>
                           </ul>
          </div>
                <div>
                           <h5 className="font-medium text-gray-800 mb-1">⚡ 扩展功能限制</h5>
                           <ul className="text-gray-600 space-y-1 ml-3">
                             <li>• <strong>单句话内生效</strong>：仅在单句分析+特定检测类型时可用</li>
                             <li>• <strong>限定命中次数</strong>：仅在多句分析+包含任意一个时可用</li>
                           </ul>
                         </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                {/* 检测角色 */}
                <div className="flex items-center space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
                  <select 
                    className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={config.detectionRole}
                    onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
                  >
                    <option value="agent">客服</option>
                    <option value="customer">客户</option>
                    <option value="all">所有角色</option>
                  </select>
                </div>

                {/* 前置条件 */}
                <div className="flex items-center space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
                  <select 
                    className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="none">无</option>
                  </select>
                </div>

                {/* 检测范围 */}
                <div className="flex items-center space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
                  <select 
                    className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={config.detectionScope}
                    onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
                  >
                    <option value="full">全文</option>
                    <option value="range">指定范围</option>
                  </select>
                </div>

                {/* 指定范围配置 */}
                {config.detectionScope === 'range' && (
                  <div className="flex items-center space-x-4">
                    <label className="w-20 text-sm font-medium text-gray-700 text-right">范围</label>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-700">第</span>
                      <input
                        type="number"
                        min="1"
                        className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={config.rangeStart}
                        onChange={(e) => setConfig({ ...config, rangeStart: parseInt(e.target.value) || 1 })}
                      />
                      <span className="text-sm text-gray-700">~</span>
                      <input
                        type="number"
                        min="1"
                        className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={config.rangeEnd}
                        onChange={(e) => setConfig({ ...config, rangeEnd: parseInt(e.target.value) || 1 })}
                      />
                      <span className="text-sm text-gray-700">句</span>
                      <span 
                        className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                        title="指定检测的句子范围，例如第1~3句表示只检测前3句对话"
                      >
                        ?
                      </span>
              </div>
                  </div>
                )}

              {/* 关键词 */}
                <div className="flex items-start space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">关键词</label>
                  <div className="flex-1">
                    <div className="flex">
                  <input
                    type="text"
                        className="flex-1 max-w-lg px-3 py-2 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入Enter键添加关键词，支持逗号分隔批量输入"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                            const input = (e.target as HTMLInputElement).value.trim();
                            if (input) {
                              // 支持逗号分隔的批量输入
                              const keywords = input.split(/[，,]/).map(kw => kw.trim()).filter(kw => kw);
                              const newKeywords = keywords.filter(kw => !config.keywords.includes(kw));
                              if (newKeywords.length > 0) {
                                setConfig({ ...config, keywords: [...config.keywords, ...newKeywords] });
                              }
                          (e.target as HTMLInputElement).value = '';
                        }
                      }
                    }}
                  />
                      <button 
                        className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm rounded-r-md hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                        onClick={(e) => {
                          const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement;
                          const inputValue = input.value.trim();
                          if (inputValue) {
                            // 支持逗号分隔的批量输入
                            const keywords = inputValue.split(/[，,]/).map(kw => kw.trim()).filter(kw => kw);
                            const newKeywords = keywords.filter(kw => !config.keywords.includes(kw));
                            if (newKeywords.length > 0) {
                              setConfig({ ...config, keywords: [...config.keywords, ...newKeywords] });
                            }
                            input.value = '';
                          }
                        }}
                      >
                        🤖 AI优化
                      </button>
                </div>
                    {config.keywords.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                  {config.keywords.map((kw, i) => (
                          <span key={i} className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                      {kw}
                      <button
                        onClick={() => setConfig({ ...config, keywords: config.keywords.filter((_, idx) => idx !== i) })}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                              ×
                      </button>
                    </span>
                  ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* 分析方式 */}
                <div className="flex items-center space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right">分析方式</label>
                  <div className="flex items-center space-x-6">
                    <div className="flex space-x-6">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="analysis-method"
                          className="mr-2 text-blue-600 focus:ring-blue-500"
                          checked={config.analysisMethod === 'single'}
                          onChange={() => setConfig({ ...config, analysisMethod: 'single' })}
                        />
                        <span className="text-sm text-gray-700">单句分析</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="analysis-method"
                          className="mr-2 text-blue-600 focus:ring-blue-500"
                          checked={config.analysisMethod === 'multiple'}
                          onChange={() => setConfig({ ...config, analysisMethod: 'multiple' })}
                        />
                        <span className="text-sm text-gray-700">多句分析</span>
                      </label>
                    </div>
                    <span 
                      className="inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                      title="单句分析、多句分析针对的是当前条件所限定的句子。&#10;限定句子：通过当前条件的适用角色、前置条件及检测范围所限定的1个或多个句子。&#10;单句分析：对限定句子逐句进行分析，例如检测包含全部关键词，只有限定句子中的某一句包含了全部的关键词，才算命中。&#10;多句分析：将限定句子合并为一个段落进行一次分析，例如检测包含全部关键词，只要合并后的段落中包含全部的关键词就算命中。"
                    >
                      ?
                    </span>
                </div>
              </div>

              {/* 检测类型 */}
                <div className="flex items-center space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right">检测类型</label>
                  <div className="flex flex-wrap items-center gap-6">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="detection-type"
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                        checked={config.detectionType === 'any'}
                        onChange={() => setConfig({ ...config, detectionType: 'any' })}
                      />
                      <span className="text-sm text-gray-700">包含任意一个关键词</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="detection-type"
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                        checked={config.detectionType === 'all'}
                        onChange={() => setConfig({ ...config, detectionType: 'all' })}
                      />
                      <span className="text-sm text-gray-700">包含全部上述关键词</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="detection-type"
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                        checked={config.detectionType === 'count'}
                        onChange={() => setConfig({ ...config, detectionType: 'count' })}
                        disabled={
                          config.analysisMethod === 'single' && config.keywords.length <= 1
                        }
                      />
                      {config.detectionType === 'count' ? (
                        <>
                          <span className={`text-sm ${
                            config.analysisMethod === 'single' && config.keywords.length <= 1
                            ? 'text-gray-400' : 'text-gray-700'
                          }`}>包含任意</span>
                          <input
                            type="number"
                            min="1"
                            max={config.keywords.length}
                            className="ml-2 w-16 px-2 py-1 text-xs border border-gray-300 rounded"
                            value={config.keywordCount}
                            onChange={(e) => setConfig({ ...config, keywordCount: parseInt(e.target.value) || 1 })}
                          />
                          <span className="ml-1 text-sm text-gray-700">个关键词</span>
                        </>
                      ) : (
                        <span className={`text-sm ${
                          config.analysisMethod === 'single' && config.keywords.length <= 1
                          ? 'text-gray-400' : 'text-gray-700'
                        }`}>包含任意N个关键词</span>
                      )}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="detection-type"
                        className="mr-2 text-blue-600 focus:ring-blue-500"
                        checked={config.detectionType === 'none'}
                        onChange={() => setConfig({ ...config, detectionType: 'none' })}
                      />
                      <span className="text-sm text-gray-700">全部不包含</span>
                      <span 
                        className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                        title="检测对话中不包含任何设置的关键词才触发。注意：前置条件中的每次逻辑和全部不包含不可同时使用。"
                      >
                        ?
                      </span>
                    </label>
                  </div>
                </div>

                {/* 扩展功能 */}
                <div className="flex items-start space-x-4">
                  <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">扩展功能</label>
                  <div className="flex flex-col space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-2 text-blue-600 focus:ring-blue-500 rounded"
                        checked={config.singleSentence}
                        onChange={(e) => setConfig({ ...config, singleSentence: e.target.checked })}
                        disabled={
                          config.analysisMethod === 'multiple' || 
                          (config.detectionType !== 'all' && config.detectionType !== 'count')
                        }
                      />
                      <span className={`text-sm ${
                        config.analysisMethod === 'multiple' || 
                        (config.detectionType !== 'all' && config.detectionType !== 'count')
                        ? 'text-gray-400' : 'text-gray-700'
                      }`}>
                        单句话内生效
                      </span>
                      <span 
                        className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                        title="将句子按标点符号拆分为小段，关键词必须在同一小段中出现才算命中。多句分析时或某些检测类型下不可用。"
                      >
                        ?
                      </span>
                    </label>
                    {config.analysisMethod === 'multiple' && config.detectionType === 'any' && (
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            className="mr-2 text-blue-600 focus:ring-blue-500 rounded"
                            checked={config.limitHitCount}
                            onChange={(e) => setConfig({ ...config, limitHitCount: e.target.checked })}
                          />
                          <span className="text-sm text-gray-700">限定命中次数</span>
                          <span 
                            className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                            title="对关键词的命中次数进行统计，达到限定次数才算规则命中。单个关键词统计该词总次数，多个关键词累加所有词的总次数。"
                          >
                            ?
                          </span>
                        </label>
                        {config.limitHitCount && (
                          <div className="ml-6 flex items-center">
                            <span className="text-sm text-gray-700">关键词总共命中</span>
                            <input
                              type="number"
                              min="1"
                              className="ml-2 w-16 px-2 py-1 text-xs border border-gray-300 rounded"
                              value={config.hitCount}
                              onChange={(e) => setConfig({ ...config, hitCount: parseInt(e.target.value) || 1 })}
                            />
                            <span className="ml-1 text-sm text-gray-700">次</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
              </div>
            </div>
          </div>

            {/* 实时测试 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                🧪 实时测试
              </h2>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    即时验证
                  </span>
                  <button
                    onClick={() => {
                      setTestText('客服：您好，欢迎来到我们公司，我是客服小王，很高兴为您服务。\n客户：你好，我想咨询一下你们的产品。\n客服：好的，请问您想了解哪方面的产品呢？');
                      setTestResult(null);
                    }}
                    className="text-sm text-gray-500 hover:text-gray-700 underline"
                  >
                    重置示例
                  </button>
                </div>
              </div>
              
              <div className="space-y-6">
                {/* 测试文本输入区 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
                      📝 测试文本
                    </label>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
                      <span>•</span>
                      <span>字符: {testText.length}</span>
                    </div>
                  </div>
                  <div className="relative">
                  <textarea
                    id="test-text"
                      rows={10}
                      className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
                    value={testText}
                    onChange={(e) => setTestText(e.target.value)}
                      placeholder="请输入需要测试的对话文本，每行一句话...&#10;&#10;示例：&#10;客服：您好，欢迎来到我们公司&#10;客户：你好，我想咨询产品&#10;客服：好的，请问您想了解哪方面的产品呢？"
                    />
                    {testText.trim() && (
                      <button
                        onClick={() => {
                          setTestText('');
                          setTestResult(null);
                        }}
                        className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        title="清空文本"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    )}
                </div>
                  <div className="text-xs text-gray-500 flex items-center space-x-4">
                    <span>💡 提示：每行代表一句对话，使用"客服："或"客户："开头</span>
                  </div>
                </div>

                {/* 快速测试按钮 */}
                <div className="flex space-x-3">
                <button
                  onClick={runTest}
                    disabled={!testText.trim()}
                    className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <span className="flex items-center justify-center space-x-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span>执行测试</span>
                    </span>
                </button>
                  {testResult && (
                    <button
                      onClick={() => setTestResult(null)}
                      className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                    >
                      清除结果
                    </button>
                  )}
              </div>
            </div>
              
              {/* 测试结果 */}
            {testResult && (
                <div className="mt-8 space-y-6">
                  {/* 结果总览 */}
                  <div className="border-t border-gray-200 pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                        <span>📊</span>
                        <span>测试结果</span>
                      </h3>
                      <button
                        onClick={() => setTestResult(null)}
                        className="text-sm text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                    
                    {/* 主要结果卡片 */}
                    <div className={`relative overflow-hidden rounded-xl border-2 ${
                      testResult.matched 
                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                        : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
                    }`}>
                      <div className="p-6">
                        <div className="flex items-center space-x-4">
                          <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                            testResult.matched ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                      {testResult.matched ? '✓' : '✗'}
                    </span>
                  </div>
                          <div className="flex-1">
                            <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                              {testResult.matched ? '规则命中' : '规则未命中'}
                </div>
                            <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                              {testResult.details}
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                              testResult.matched 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {testResult.matched ? '✅ 通过' : '❌ 未通过'}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 详细结果 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* 关键词命中情况 */}
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-blue-500">🎯</span>
                        <h4 className="font-semibold text-gray-800">关键词命中</h4>
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          {testResult.matchedKeywords.length}/{config.keywords.length}
                        </span>
                      </div>
                      <div className="space-y-2">
                        {config.keywords.map((keyword, i) => (
                          <div key={i} className="flex items-center justify-between py-1">
                            <span className="text-sm text-gray-700">{keyword}</span>
                            <span className={`text-xs px-2 py-1 rounded ${
                              testResult.matchedKeywords.includes(keyword)
                                ? 'bg-green-100 text-green-700'
                                : 'bg-gray-100 text-gray-500'
                            }`}>
                              {testResult.matchedKeywords.includes(keyword) ? '✓ 命中' : '- 未命中'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-purple-500">📈</span>
                        <h4 className="font-semibold text-gray-800">统计信息</h4>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">总命中次数</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-mono text-lg font-bold text-purple-600">
                              {testResult.hitCount}
                            </span>
                            {config.limitHitCount && (
                              <span className="text-xs text-gray-500">
                                (需要≥{config.hitCount})
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">命中句子数</span>
                          <span className="font-mono text-lg font-bold text-purple-600">
                            {testResult.matchedSentences.length}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">检测覆盖率</span>
                          <span className="font-mono text-lg font-bold text-purple-600">
                            {((testResult.matchedKeywords.length / config.keywords.length) * 100).toFixed(0)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 命中句子详情 */}
                  {testResult.matchedSentences.length > 0 && (
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-orange-500">💬</span>
                        <h4 className="font-semibold text-gray-800">命中句子</h4>
                        <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                          {testResult.matchedSentences.length} 条
                        </span>
                      </div>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {testResult.matchedSentences.map((sentence, i) => (
                          <div key={i} className="p-3 bg-orange-50 border border-orange-100 rounded-md">
                            <div className="text-sm text-gray-800 font-medium">
                              {sentence}
                            </div>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {config.keywords.filter(kw => sentence.includes(kw)).map((kw, j) => (
                                <span key={j} className="inline-block bg-orange-200 text-orange-800 text-xs px-2 py-0.5 rounded">
                                  {kw}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 配置摘要 */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-gray-500">⚙️</span>
                      <h4 className="font-semibold text-gray-800">当前配置</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-gray-600">检测角色:</span>
                          <span className="font-medium">{config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">检测范围:</span>
                          <span className="font-medium">{config.detectionScope === 'full' ? '全文' : `第${config.rangeStart}-${config.rangeEnd}句`}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">分析方法:</span>
                          <span className="font-medium">{config.analysisMethod === 'single' ? '单句分析' : '多句分析'}</span>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-gray-600">检测类型:</span>
                          <span className="font-medium">{
                            config.detectionType === 'any' ? '包含任意' :
                            config.detectionType === 'all' ? '包含全部' :
                            config.detectionType === 'count' ? `包含${config.keywordCount}个` :
                            '全部不包含'
                          }</span>
                        </div>
                        {config.singleSentence && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">单句生效:</span>
                            <span className="font-medium text-green-600">✓ 启用</span>
                          </div>
                        )}
                        {config.limitHitCount && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">命中次数:</span>
                            <span className="font-medium text-blue-600">≥{config.hitCount}次</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                📂 案例库
              </h2>
              <div className="space-y-4">
                {cases.map((c, index) => (
                  <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
                    <div className="flex justify-between items-start">
                  <div>
                        <h4 className="font-semibold text-gray-800">{c.name}</h4>
                        <p className="text-xs text-gray-500 mt-1">{c.description}</p>
                        <p className="text-xs text-blue-500 mt-1">预期结果：{c.expectedResult}</p>
                  </div>
                      <button 
                        onClick={() => loadCase(c.config, c.testText)}
                        className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
                      >
                        加载
                      </button>
                    </div>
                  </div>
                ))}
            </div>
          </div>
          
            {/* 使用提示 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-6">
                <span className="text-2xl">💡</span>
                <h2 className="text-xl font-semibold text-gray-900">
                  使用提示
            </h2>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  实用技巧
                </span>
              </div>

              <div className="space-y-4">
                {/* 基础操作提示 */}
                <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 text-sm">⌨️</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
                      <div className="space-y-2">
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                            <span className="text-sm font-medium text-gray-700">关键词输入</span>
                            <span className="text-xs text-gray-500 block">区分大小写，支持中文、英文和数字</span>
                    </div>
                  </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
              <div>
                            <span className="text-sm font-medium text-gray-700">分析方法</span>
                            <span className="text-xs text-gray-500 block">单句分析逐句检测，多句分析合并检测</span>
                </div>
              </div>
              </div>
              </div>
            </div>
          </div>

                {/* 高级功能提示 */}
                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 text-sm">🔧</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-2">高级功能</h4>
                      <div className="space-y-2">
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                          <div>
                            <span className="text-sm font-medium text-gray-700">单句话内生效</span>
                            <span className="text-xs text-gray-500 block">按标点符号分割小句，仅在特定配置下可用</span>
                          </div>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                          <div>
                            <span className="text-sm font-medium text-gray-700">限定命中次数</span>
                            <span className="text-xs text-gray-500 block">统计关键词总出现次数（可跨关键词累加）</span>
                          </div>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                <div>
                            <span className="text-sm font-medium text-gray-700">检测范围</span>
                            <span className="text-xs text-gray-500 block">指定范围可精确控制检测的句子区间</span>
                          </div>
                </div>
              </div>
            </div>
                  </div>
                </div>

                {/* 最佳实践 */}
                <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">🎯</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
                      <div className="space-y-2">
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                          <div>
                            <span className="text-sm font-medium text-gray-700">性能建议</span>
                            <span className="text-xs text-gray-500 block">复杂场景可考虑使用正则表达式替代</span>
                          </div>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                            <span className="text-sm font-medium text-gray-700">测试建议</span>
                            <span className="text-xs text-gray-500 block">先用预设案例熟悉功能，再自定义配置</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 快速开始 */}
                <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 text-sm">🚀</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
                      <div className="flex items-center space-x-2 text-xs text-gray-600">
                        <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">步骤1</span>
                        <span>选择案例</span>
                        <span>→</span>
                        <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">步骤2</span>
                        <span>点击加载</span>
                        <span>→</span>
                        <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">步骤3</span>
                        <span>执行测试</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>从简单开始</strong>：先使用基础的"包含任意关键词"模式熟悉流程</p>
                <p>• <strong>理解分析方法</strong>：体验单句分析与多句分析的不同效果</p>
                <p>• <strong>掌握扩展功能</strong>：学会使用单句话内生效和限定命中次数</p>
                <p>• <strong>实际应用</strong>：结合业务场景设计合适的关键词和检测逻辑</p>
                <p>• <strong>性能优化</strong>：关键词数量较多时考虑使用正则表达式算子</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KeywordCheckPage; 