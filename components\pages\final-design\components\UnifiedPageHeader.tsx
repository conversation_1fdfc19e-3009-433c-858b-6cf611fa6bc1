import React, { useState } from 'react';
import { LucideIcon, FileText, X } from 'lucide-react';
import { Switch } from 'antd';

interface ActionButton {
  label: string;
  onClick: () => void;
  icon?: LucideIcon;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  type?: 'button' | 'switch';
  checked?: boolean;
}

interface UnifiedPageHeaderProps {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  iconColor?: string;
  iconBgColor?: string;
  actions?: ActionButton[];
  breadcrumbs?: { label: string; href?: string }[];
  badge?: {
    text: string;
    color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  };
  children?: React.ReactNode;
  showDesignGuide?: boolean;
  designGuideContent?: React.ReactNode;
}

const getButtonClasses = (variant: ActionButton['variant'] = 'primary', size: ActionButton['size'] = 'md') => {
  const baseClasses = 'inline-flex items-center font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 border border-transparent',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 border border-transparent',
    outline: 'bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500 border border-gray-300'
  };
  
  return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]}`;
};

const getBadgeClasses = (color: string) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-800',
    green: 'bg-green-100 text-green-800',
    yellow: 'bg-yellow-100 text-yellow-800',
    red: 'bg-red-100 text-red-800',
    purple: 'bg-purple-100 text-purple-800',
    gray: 'bg-gray-100 text-gray-800'
  };
  
  return `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color as keyof typeof colorClasses] || colorClasses.gray}`;
};

/**
 * 统一页面头部组件
 * 提供一致的页面标题、副标题、操作按钮和面包屑导航
 */
export const UnifiedPageHeader: React.FC<UnifiedPageHeaderProps> = ({
  title,
  subtitle,
  icon: Icon,
  iconColor = 'text-blue-600',
  iconBgColor = 'bg-blue-100',
  actions = [],
  breadcrumbs,
  badge,
  children,
  showDesignGuide = false,
  designGuideContent
}) => {
  const [isDesignGuideOpen, setIsDesignGuideOpen] = useState(false);
  return (
    <div className="bg-white border-b border-gray-200">
      <div className="px-6 py-6">
        {/* 面包屑导航 */}
        {breadcrumbs && breadcrumbs.length > 0 && (
          <nav className="mb-4" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <svg className="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                  {crumb.href ? (
                    <a href={crumb.href} className="text-gray-500 hover:text-gray-700 transition-colors">
                      {crumb.label}
                    </a>
                  ) : (
                    <span className={index === breadcrumbs.length - 1 ? 'text-gray-900 font-medium' : 'text-gray-500'}>
                      {crumb.label}
                    </span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}
        
        {/* 主要头部内容 */}
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            {/* 图标 */}
            {Icon && (
              <div className={`w-12 h-12 ${iconBgColor} rounded-xl flex items-center justify-center flex-shrink-0`}>
                <Icon className={`w-6 h-6 ${iconColor}`} />
              </div>
            )}
            
            {/* 标题和副标题 */}
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-3">
                <h1 className="text-2xl font-bold text-gray-900 tracking-tight">
                  {title}
                </h1>
                {badge && (
                  <span className={getBadgeClasses(badge.color)}>
                    {badge.text}
                  </span>
                )}
              </div>
              {subtitle && (
                <p className="mt-2 text-sm text-gray-600 leading-relaxed max-w-3xl">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            {/* 详细设计说明按钮 */}
            {showDesignGuide && (
              <button
                onClick={() => setIsDesignGuideOpen(true)}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                <FileText className="w-4 h-4 mr-2" />
                详细设计说明
              </button>
            )}
            
            {/* 其他操作按钮 */}
            {actions.length > 0 && (
              <>
                {actions.map((action, index) => {
                  const ButtonIcon = action.icon;
                  if (action.type === 'switch') {
                    return (
                      <div key={index} className="flex items-center gap-2">
                        <Switch checked={action.checked} onChange={action.onClick} />
                        <span className="text-sm font-medium text-gray-700">{action.label}</span>
                      </div>
                    );
                  }
                  return (
                    <button
                      key={index}
                      onClick={action.onClick}
                      className={getButtonClasses(action.variant, action.size)}
                    >
                      {ButtonIcon && <ButtonIcon className="w-4 h-4 mr-2" />}
                      {action.label}
                    </button>
                  );
                })}
              </>
            )}
          </div>
        </div>
        
        {/* 自定义内容 */}
        {children && (
          <div className="mt-6">
            {children}
          </div>
        )}
      </div>
      
      {/* 详细设计说明抽屉 */}
      {isDesignGuideOpen && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 z-40 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={() => setIsDesignGuideOpen(false)}
          ></div>
          
          {/* 抽屉内容 */}
          <div className="fixed inset-y-0 right-0 z-50 w-full max-w-2xl bg-white shadow-xl transform transition-transform duration-300 ease-in-out flex flex-col">
            {/* 抽屉头部 */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-900">
                {title} - 详细设计说明
              </h3>
              <button
                onClick={() => setIsDesignGuideOpen(false)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            {/* 抽屉内容 */}
            <div className="flex-1 overflow-y-auto px-6 py-6">
              {designGuideContent ? (
                designGuideContent
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <FileText className="w-8 h-8 text-gray-400" />
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">页面建设中</h4>
                  <p className="text-gray-600">
                    该页面的详细设计说明正在完善中，敬请期待。
                  </p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UnifiedPageHeader;