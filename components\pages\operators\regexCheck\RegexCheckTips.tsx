import React from 'react';

/**
 * 正则表达式检查使用提示组件
 * 提供使用技巧和最佳实践指导
 */
const RegexCheckTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">正则语法</span>
                    <span className="text-xs text-gray-500 block">使用JavaScript正则表达式语法，支持元字符和量词</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">匹配标志</span>
                    <span className="text-xs text-gray-500 block">默认启用不区分大小写(i)、全局匹配(g)、多行模式(m)</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">规则组合</span>
                    <span className="text-xs text-gray-500 block">先应用命中规则，再过滤排除规则</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 高级功能提示 */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">高级功能</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">单句话内生效</span>
                    <span className="text-xs text-gray-500 block">按标点符号分割句子，在每个小段中独立匹配</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">排除规则</span>
                    <span className="text-xs text-gray-500 block">过滤不希望匹配的内容，实现精确控制</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">检测范围</span>
                    <span className="text-xs text-gray-500 block">指定句子范围，聚焦关键对话区域</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">模式设计</span>
                    <span className="text-xs text-gray-500 block">从简单模式开始，逐步增加复杂度</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">测试验证</span>
                    <span className="text-xs text-gray-500 block">使用多样化的测试数据验证匹配效果</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">性能考虑</span>
                    <span className="text-xs text-gray-500 block">避免过于复杂的正则表达式，影响执行效率</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-sm">❓</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">常见问题</h4>
              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">正则表达式不匹配</div>
                  <ul className="text-xs text-gray-600 space-y-1 ml-3">
                    <li>• 检查特殊字符是否正确转义</li>
                    <li>• 确认字符类和量词的使用</li>
                    <li>• 考虑大小写和空格的影响</li>
                  </ul>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">匹配结果过多</div>
                  <ul className="text-xs text-gray-600 space-y-1 ml-3">
                    <li>• 使用更精确的模式匹配</li>
                    <li>• 添加排除规则过滤无关内容</li>
                    <li>• 限制检测范围或角色</li>
                  </ul>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">性能问题</div>
                  <ul className="text-xs text-gray-600 space-y-1 ml-3">
                    <li>• 避免过度使用回溯的模式</li>
                    <li>• 简化复杂的嵌套表达式</li>
                    <li>• 考虑使用关键词检查替代</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 正则表达式速查表 */}
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <span className="text-gray-600 text-sm">📖</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-3">正则表达式速查表</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                <div className="space-y-2">
                  <div className="bg-white p-2 rounded border">
                    <div className="font-medium text-gray-700 mb-1">常用元字符</div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <code className="text-blue-600">.</code>
                        <span className="text-gray-600">任意字符（除换行符）</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">*</code>
                        <span className="text-gray-600">前面字符的0次或多次</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">+</code>
                        <span className="text-gray-600">前面字符的1次或多次</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">?</code>
                        <span className="text-gray-600">前面字符的0次或1次</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white p-2 rounded border">
                    <div className="font-medium text-gray-700 mb-1">字符类</div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <code className="text-blue-600">[0-9]</code>
                        <span className="text-gray-600">数字字符</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">[a-zA-Z]</code>
                        <span className="text-gray-600">字母字符</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">\d</code>
                        <span className="text-gray-600">数字（等同[0-9]）</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">\w</code>
                        <span className="text-gray-600">单词字符（字母数字下划线）</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="bg-white p-2 rounded border">
                    <div className="font-medium text-gray-700 mb-1">量词</div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <code className="text-blue-600">{'{n}'}</code>
                        <span className="text-gray-600">恰好n次</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">{'{n,}'}</code>
                        <span className="text-gray-600">至少n次</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">{'{n,m}'}</code>
                        <span className="text-gray-600">n到m次</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white p-2 rounded border">
                    <div className="font-medium text-gray-700 mb-1">位置匹配</div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <code className="text-blue-600">^</code>
                        <span className="text-gray-600">行开始</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">$</code>
                        <span className="text-gray-600">行结束</span>
                      </div>
                      <div className="flex justify-between">
                        <code className="text-blue-600">\b</code>
                        <span className="text-gray-600">单词边界</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速开始 */}
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
              <span className="text-indigo-600 text-sm">🚀</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤1</span>
                <span>选择案例</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤2</span>
                <span>加载配置</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤3</span>
                <span>运行测试</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤4</span>
                <span>调整优化</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegexCheckTips; 