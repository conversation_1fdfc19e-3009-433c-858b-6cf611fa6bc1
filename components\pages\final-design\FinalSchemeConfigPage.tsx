import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, Edit, Trash2, FileText, Power, PowerOff, Settings, Upload } from 'lucide-react';
import { CreateSchemeForm } from './FinalCreateSchemeForm';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import UnifiedPageHeader from './components/UnifiedPageHeader';

// Simplified Rule interface for displaying within a scheme
interface RuleInScheme {
    id: string;
    name: string;
    score: number;
}

// Scheme data structure
interface Scheme {
    id: string;
    name: string;
    status: 'enabled' | 'disabled';
    totalScore: number;
    passThreshold: number;
    appealDeadlineDays: number;
    rules: RuleInScheme[];
    creator: string;
    lastModified: string;
    description: string;
}

// Search filters interface
interface SearchFilters {
    schemeName: string;
    status: 'all' | 'enabled' | 'disabled';
}

// Mock data for all available rules in the library
// In a real app, this would be fetched from an API
const allAvailableRules = [
  { id: 'RULE-001', name: '客户不满与合规词检测', category: '合规风险', importance: '严重违规', description: '识别客户不满关键词，并排除特定合规术语。' },
  { id: 'RULE-002', name: '标准开场白检测', category: '服务规范', importance: '轻度违规', description: '检测坐席开场白是否符合"您好，很高兴为您服务"的标准句式。' },
  { id: 'RULE-003', name: '合规声明-录音告知', category: '合规风险', importance: '中度违规', description: '验证坐席是否在通话开始时进行了录音告知。' },
  { id: 'RULE-004', name: '客户情绪-激动模型', category: '客户体验', importance: '中度违规', description: '使用AI模型结合能量检测判断客户是否存在激动情绪。' },
  { id: 'RULE-005', name: '身份证号码格式校验', category: '信息安全', importance: '严重违规', description: '通过正则表达式验证身份证号码格式是否正确。' },
  { id: 'RULE-006', name: '客服辱骂检测模型', category: '服务红线', importance: '严重违规', description: '监控客服在对话中是否存在不文明用语。' },
  { id: 'RULE-007', name: '上下文重复询问', category: '服务效率', importance: '轻度违规', description: '检测坐席是否在短时间内重复询问客户相同的问题。' },
  { id: 'RULE-008', name: '静音与抢话综合检测', category: '服务质量', importance: '中度违规', description: '检测单次静音时长是否超过30秒，以及是否存在抢话。' },
  { id: 'RULE-009', name: '信息实体-手机号与邮箱', category: '信息提取', importance: '轻度违规', description: '提取对话中客户提及的手机号码和邮箱地址。' },
  { id: 'RULE-010', name: '大模型质检-金融产品推荐', category: '销售合规', importance: '严重违规', description: '使用大模型判断坐席的金融产品推荐是否合规、适当，并校验是否提及风险。' },
];

const initialSchemesData: Scheme[] = [
  { 
    id: 'SCHEME-001', 
    name: '服务规范通用方案', 
    status: 'enabled', 
    totalScore: 100,
    passThreshold: 85,
    appealDeadlineDays: 7,
    rules: [
      { id: 'RULE-002', name: '标准开场白检测', score: -5 },
      { id: 'RULE-007', name: '上下文重复询问', score: -10 },
      { id: 'RULE-008', name: '静音与抢话综合检测', score: -15 },
    ],
    creator: '王主管', 
    lastModified: '2024-05-28 11:00', 
    description: '适用于所有常规服务场景，考察基本的服务礼仪和效率。' 
  },
  { 
    id: 'SCHEME-002', 
    name: '金融产品销售合规方案', 
    status: 'enabled', 
    totalScore: 100,
    passThreshold: 95,
    appealDeadlineDays: 3,
    rules: [
      { id: 'RULE-001', name: '客户不满与合规词检测', score: -20 },
      { id: 'RULE-003', name: '合规声明-录音告知', score: -25 },
      { id: 'RULE-005', name: '身份证号码格式校验', score: -20 },
      { id: 'RULE-010', name: '大模型质检-金融产品推荐', score: -35 },
    ],
    creator: '李经理', 
    lastModified: '2024-05-27 15:30', 
    description: '针对高风险的金融产品销售场景，强调查验合规红线。' 
  },
  { 
    id: 'SCHEME-003', 
    name: '客户体验优化方案', 
    status: 'disabled', 
    totalScore: 100,
    passThreshold: 80,
    appealDeadlineDays: 5,
    rules: [
        { id: 'RULE-004', name: '客户情绪-激动模型', score: -30 },
        { id: 'RULE-006', name: '客服辱骂检测模型', score: -50 },
        { id: 'RULE-009', name: '信息实体-手机号与邮箱', score: -5 },
    ],
    creator: '张三', 
    lastModified: '2024-05-26 09:45', 
    description: '重点关注客户的情绪反馈和服务过程中的负面体验。' 
  },
];

const PAGE_SIZE = 10;

const Switch = ({ checked, onChange }: { checked: boolean, onChange: () => void }) => (
    <button
        type="button"
        onClick={onChange}
        className={`${
            checked ? 'bg-green-500' : 'bg-gray-300'
        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        role="switch"
        aria-checked={checked}
    >
        <span
            aria-hidden="true"
            className={`${
                checked ? 'translate-x-5' : 'translate-x-0'
            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
        />
    </button>
);

/**
 * 质检方案配置页面
 * 提供质检方案的创建、查看、筛选和管理功能。方案是质检规则的有序集合。
 */
export const FinalSchemeConfigPage: React.FC = () => {
    const [schemes, setSchemes] = useState<Scheme[]>(initialSchemesData);
    const [searchFilters, setSearchFilters] = useState<SearchFilters>({ schemeName: '', status: 'all' });
    const [currentPage, setCurrentPage] = useState(1);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingScheme, setEditingScheme] = useState<Scheme | null>(null);

    const filteredSchemes = useMemo(() => {
        return schemes.filter(scheme => {
            const matchesSearch = scheme.name.toLowerCase().includes(searchFilters.schemeName.toLowerCase());
            const matchesStatus = searchFilters.status === 'all' || scheme.status === searchFilters.status;
            return matchesSearch && matchesStatus;
        });
    }, [schemes, searchFilters.schemeName, searchFilters.status]);

    const totalPages = Math.ceil(filteredSchemes.length / PAGE_SIZE);
    const paginatedSchemes = filteredSchemes.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

    const handlePageChange = (page: number) => {
        if (page > 0 && page <= totalPages) {
            setCurrentPage(page);
        }
    };
    
    const handleStatusToggle = (schemeId: string) => {
        setSchemes(currentSchemes =>
            currentSchemes.map(scheme =>
                scheme.id === schemeId
                    ? { ...scheme, status: scheme.status === 'enabled' ? 'disabled' : 'enabled' }
                    : scheme
            )
        );
    };

    const handleDelete = (schemeId: string) => {
        if (window.confirm('确定要删除这个质检方案吗？')) {
            setSchemes(currentSchemes => currentSchemes.filter(scheme => scheme.id !== schemeId));
        }
    };

    const handleOpenCreateForm = () => {
        setEditingScheme(null);
        setIsFormOpen(true);
    };

    const handleOpenEditForm = (scheme: Scheme) => {
        setEditingScheme(scheme);
        setIsFormOpen(true);
    };

    const handleCloseForm = () => {
        setIsFormOpen(false);
        setEditingScheme(null);
    };

    const handleSubmitScheme = (schemeData: { name: string; description: string; totalScore: number; passThreshold: number; appealDeadlineDays: number; rules: RuleInScheme[] }) => {
        setSchemes(prevSchemes => {
            if (editingScheme) {
                // Update existing scheme
                return prevSchemes.map(s => s.id === editingScheme.id ? { ...s, ...schemeData, lastModified: new Date().toLocaleString() } : s);
            } else {
                // Create new scheme
                const newScheme: Scheme = {
                    ...schemeData,
                    id: `SCHEME-${String(prevSchemes.length + 1).padStart(3, '0')}`,
                    status: 'enabled',
                    creator: '当前用户',
                    lastModified: new Date().toLocaleString(),
                };
                return [newScheme, ...prevSchemes];
            }
        });
        handleCloseForm();
    };

    const filterFields: FilterField[] = [
        {
            key: 'schemeName',
            label: '方案名称',
            type: 'text',
            placeholder: '按方案名称搜索...',
        },
        {
            key: 'status',
            label: '状态',
            type: 'select',
            placeholder: '所有状态',
            options: [
                { value: 'all', label: '所有状态' },
                { value: 'enabled', label: '已启用' },
                { value: 'disabled', label: '已禁用' },
            ],
        },
    ];

    const handleFiltersChange = (newFilters: Record<string, any>) => {
        setSearchFilters(newFilters as SearchFilters);
        setCurrentPage(1);
    };

    const handleSearch = () => {
        setCurrentPage(1);
    };

    const handleReset = () => {
        setSearchFilters({ schemeName: '', status: 'all' });
        setCurrentPage(1);
    };

    const handleImportScheme = () => {
        // Implementation of handleImportScheme function
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            {/* 统一页面头部 */}
            <UnifiedPageHeader
                title="质检方案管理"
                subtitle="组合多个独立的质检规则，形成可用于具体质检任务的完整评分方案。"
                icon={Settings}
                iconColor="text-blue-600"
                iconBgColor="bg-blue-100"
                badge={{ text: `共 ${schemes.length} 个方案`, color: "blue" }}
                actions={[
                    {
                        label: "导入方案",
                        icon: Upload,
                        variant: "outline",
                        onClick: handleImportScheme
                    },
                    {
                        label: "新建方案",
                        icon: Plus,
                        variant: "primary",
                        onClick: handleOpenCreateForm
                    }
                ]}
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* Filters and Search */}
                    <UnifiedSearchFilter
                        fields={filterFields}
                        filters={searchFilters}
                        onFiltersChange={handleFiltersChange}
                        onSearch={handleSearch}
                        onReset={handleReset}
                        showFilterCount={false} // Optionally hide filter count if only one or two simple filters
                    />

                    {/* Schemes Table */}
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-4 py-3">序号</th>
                                    <th scope="col" className="px-6 py-3">方案名称</th>
                                    <th scope="col" className="px-6 py-3">规则数</th>
                                    <th scope="col" className="px-6 py-3">通过分/总分</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">最后修改时间</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedSchemes.map((scheme, index) => (
                                    <tr key={scheme.id} className="bg-white border-b hover:bg-gray-50">
                                         <td className="px-4 py-4 text-gray-700">
                                            {(currentPage - 1) * PAGE_SIZE + index + 1}
                                        </td>
                                        <td className="px-6 py-4 align-top">
                                            <div className="font-semibold text-gray-900">{scheme.name}</div>
                                            <p className="mt-1 text-xs text-gray-500">{scheme.description}</p>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900 font-medium">{scheme.rules.length}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className='flex items-baseline'>
                                                <span className="font-semibold text-lg text-gray-800">{scheme.totalScore}</span>
                                                <span className="text-sm text-gray-500 ml-1">分</span>
                                            </div>
                                            <div className='text-xs text-green-600 font-bold'>≥ {scheme.passThreshold}分合格</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm text-gray-900">{scheme.creator}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm text-gray-900">{scheme.lastModified}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onClick={() => handleOpenEditForm(scheme)} className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors" title="编辑">
                                                <Edit className="w-4 h-4" />
                                            </button>
                                            <button 
                                                onClick={() => handleStatusToggle(scheme.id)}
                                                className={`p-1.5 rounded-lg transition-colors mx-1 ${
                                                    scheme.status === 'enabled' 
                                                        ? 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50' 
                                                        : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                                                }`}
                                                title={scheme.status === 'enabled' ? '禁用' : '启用'}
                                            >
                                                {scheme.status === 'enabled' ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
                                            </button>
                                            <button 
                                                onClick={() => handleDelete(scheme.id)}
                                                className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors" 
                                                title="删除"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredSchemes.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的方案。</p>
                        </div>
                    )}
                    
                    {/* Pagination */}
                    <UnifiedPagination
                        current={currentPage}
                        pageSize={PAGE_SIZE}
                        total={filteredSchemes.length}
                        onChange={handlePageChange}
                    />
                </motion.div>
            </main>
            {isFormOpen && (
                <CreateSchemeForm 
                    onClose={handleCloseForm}
                    onSubmit={handleSubmitScheme}
                    allRules={allAvailableRules}
                    initialData={editingScheme}
                />
            )}
        </div>
    );
};

export default FinalSchemeConfigPage;