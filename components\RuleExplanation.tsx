import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, InformationCircleIcon } from './icons';

/**
 * 规则解释组件
 * 用于详细说明复杂质检规则的业务逻辑和条件关系
 */
export const RuleExplanation: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <InformationCircleIcon className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-blue-800">规则原理详解</h3>
        </div>
        <button className="flex items-center space-x-1 text-blue-600 hover:text-blue-800">
          <span className="text-sm">{isExpanded ? '收起' : '展开'}</span>
          {isExpanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      </div>

      {isExpanded && (
        <div className="mt-4 space-y-4">
          {/* 业务场景说明 */}
          <div className="bg-white p-3 rounded-lg border border-blue-100">
            <h4 className="font-semibold text-gray-800 mb-2">🎯 业务场景</h4>
            <p className="text-gray-700 text-xs leading-relaxed">
              本规则用于检测客服在信用卡咨询场景下是否严格遵循公司业务流程。当客户咨询信用卡相关业务时，
              客服应当按照标准话术进行询问和确认，确保信息收集的完整性和合规性。
            </p>
          </div>

          {/* 逻辑分支说明 */}
          <div className="bg-white p-3 rounded-lg border border-blue-100">
            <h4 className="font-semibold text-gray-800 mb-3">🔄 逻辑分支解析</h4>
            <div className="text-xs text-gray-700 space-y-2">
              <div className="bg-red-50 border-l-4 border-red-400 p-3 rounded">
                <p className="font-medium text-red-800 mb-1">违规情况一：(a && b && !c)</p>
                <p className="text-red-700">
                  <strong>触发条件：</strong>客户明确表示"办过"信用卡，但客服未按流程询问"发卡行"或"额度"信息
                </p>
                <p className="text-red-600 text-xs mt-1">
                  ⚠️ 当客户已有信用卡时，客服应当了解具体的发卡行和额度情况
                </p>
              </div>
              
              <div className="bg-red-50 border-l-4 border-red-400 p-3 rounded">
                <p className="font-medium text-red-800 mb-1">违规情况二：(a && d && !(e && f) && g && h)</p>
                <p className="text-red-700">
                  <strong>触发条件：</strong>客户提及信用卡，客服询问了工作信息和资产情况，要求真实填写，
                  但未同时询问"年收入"和"税前"收入
                </p>
                <p className="text-red-600 text-xs mt-1">
                  ⚠️ 在收集完整资产信息时，年收入和税前收入必须同时询问以确保评估准确性
                </p>
              </div>
            </div>
          </div>

          {/* 条件详解 */}
          <div className="bg-white p-3 rounded-lg border border-blue-100">
            <h4 className="font-semibold text-gray-800 mb-3">📋 条件说明</h4>
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-blue-500 text-white text-xs rounded-full text-center leading-6 font-bold">A</span>
                  <div>
                    <p className="font-medium text-gray-800">信用卡话题检测</p>
                    <p className="text-gray-600 text-xs">检测对话中是否提及信用卡相关内容</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-green-500 text-white text-xs rounded-full text-center leading-6 font-bold">B</span>
                  <div>
                    <p className="font-medium text-gray-800">客户办卡确认</p>
                    <p className="text-gray-600 text-xs">客户是否明确表示已经办过信用卡</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-yellow-500 text-white text-xs rounded-full text-center leading-6 font-bold">C</span>
                  <div>
                    <p className="font-medium text-gray-800">发卡行额度询问</p>
                    <p className="text-gray-600 text-xs">客服是否询问了发卡行和额度信息</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-purple-500 text-white text-xs rounded-full text-center leading-6 font-bold">D</span>
                  <div>
                    <p className="font-medium text-gray-800">工作信息收集</p>
                    <p className="text-gray-600 text-xs">客服是否询问了工作单位相关信息</p>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-indigo-500 text-white text-xs rounded-full text-center leading-6 font-bold">E</span>
                  <div>
                    <p className="font-medium text-gray-800">年收入询问</p>
                    <p className="text-gray-600 text-xs">客服是否询问了年收入信息</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-pink-500 text-white text-xs rounded-full text-center leading-6 font-bold">F</span>
                  <div>
                    <p className="font-medium text-gray-800">税前收入确认</p>
                    <p className="text-gray-600 text-xs">客服是否同时询问了税前收入</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-red-500 text-white text-xs rounded-full text-center leading-6 font-bold">G</span>
                  <div>
                    <p className="font-medium text-gray-800">资产情况了解</p>
                    <p className="text-gray-600 text-xs">客服是否询问了名下房产或车辆</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-block w-6 h-6 bg-gray-500 text-white text-xs rounded-full text-center leading-6 font-bold">H</span>
                  <div>
                    <p className="font-medium text-gray-800">真实性要求</p>
                    <p className="text-gray-600 text-xs">客服是否要求客户提供真实准确信息</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 流程图说明 */}
          <div className="bg-white p-3 rounded-lg border border-blue-100">
            <h4 className="font-semibold text-gray-800 mb-3">🔄 标准流程</h4>
            <div className="text-xs text-gray-700">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                <span>检测到信用卡相关话题 (条件A)</span>
              </div>
              <div className="ml-10 border-l-2 border-gray-200 pl-4 space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">✓</div>
                  <span>如果客户说"办过" → 必须询问发卡行和额度</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs">✓</div>
                  <span>询问工作信息 → 必须完整收集收入信息 + 资产信息 + 真实性要求</span>
                </div>
              </div>
            </div>
          </div>

          {/* 实际话术示例 */}
          <div className="bg-white p-3 rounded-lg border border-blue-100">
            <h4 className="font-semibold text-gray-800 mb-3">💬 实际话术示例</h4>
            
            {/* 违规示例一 */}
            <div className="mb-3">
              <h5 className="font-medium text-red-700 mb-2 text-sm">❌ 违规示例一：(a && b && !c)</h5>
              <div className="bg-gray-50 p-2 rounded border-l-4 border-red-400 text-xs">
                <div className="space-y-2">
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>您好，我想了解一下您是否有信用卡？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>我<mark className="bg-blue-200">办过</mark>工商银行的<mark className="bg-blue-200">信用卡</mark>。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>好的，那我们为您推荐我们银行的信用卡产品...</span>
                  </div>
                </div>
                <div className="mt-3 p-2 bg-red-50 rounded text-red-700 text-xs">
                  <p><strong>违规原因：</strong>客户明确说"办过信用卡"，但客服未询问发卡行具体信息和额度，直接推荐产品</p>
                  <p><strong>条件匹配：</strong>a✓ (提及信用卡) && b✓ (客户说办过) && !c✓ (未询问发卡行额度)</p>
                </div>
              </div>
            </div>

            {/* 违规示例二 */}
            <div className="mb-3">
              <h5 className="font-medium text-red-700 mb-2 text-sm">❌ 违规示例二：(a && d && !(e && f) && g && h)</h5>
              <div className="bg-gray-50 p-2 rounded border-l-4 border-red-400 text-xs">
                <div className="space-y-2">
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>您想办理<mark className="bg-blue-200">信用卡</mark>是吗？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>是的，想了解一下。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>请问您的<mark className="bg-purple-200">工作单位</mark>是什么？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>我在XX科技公司工作。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>您<mark className="bg-indigo-200">年收入</mark>大概多少？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>15万左右。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>请问您<mark className="bg-red-200">名下</mark>有<mark className="bg-red-200">房产</mark>吗？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>有一套住房。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>请您<mark className="bg-gray-200">如实</mark>填写申请信息，确保<mark className="bg-gray-200">准确</mark>无误。</span>
                  </div>
                </div>
                <div className="mt-3 p-2 bg-red-50 rounded text-red-700 text-xs">
                  <p><strong>违规原因：</strong>客服询问了年收入，但未同时询问税前收入情况，收入信息收集不完整</p>
                  <p><strong>条件匹配：</strong>a✓ (提及信用卡) && d✓ (询问工作) && !(e✓ && f✗)✓ (年收入有，税前无) && g✓ (询问房产) && h✓ (要求真实)</p>
                </div>
              </div>
            </div>

            {/* 正确示例一 */}
            <div className="mb-3">
              <h5 className="font-medium text-green-700 mb-2 text-sm">✅ 正确示例一：针对违规情况一</h5>
              <div className="bg-gray-50 p-2 rounded border-l-4 border-green-400 text-xs">
                <div className="space-y-2">
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>您好，我想了解一下您是否有<mark className="bg-blue-200">信用卡</mark>？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>我<mark className="bg-blue-200">办过</mark>一张。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>请问是哪家银行的？<mark className="bg-yellow-200">额度</mark>是多少？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>建设银行的，额度2万。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>好的，我们为您推荐更适合的产品...</span>
                  </div>
                </div>
                <div className="mt-3 p-2 bg-green-50 rounded text-green-700 text-xs">
                  <p><strong>合规原因：</strong>客户说办过信用卡后，客服及时询问了发卡行和额度信息，信息收集完整</p>
                  <p><strong>条件匹配：</strong>a✓ && b✓ && c✓ → 规则不触发</p>
                </div>
              </div>
            </div>

            {/* 正确示例二 */}
            <div>
              <h5 className="font-medium text-green-700 mb-2 text-sm">✅ 正确示例二：针对违规情况二</h5>
              <div className="bg-gray-50 p-2 rounded border-l-4 border-green-400 text-xs">
                <div className="space-y-2">
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>您想办理<mark className="bg-blue-200">信用卡</mark>是吗？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>是的，想了解一下。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>请问您的<mark className="bg-purple-200">工作单位</mark>是什么？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>我在XX科技公司工作。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>您<mark className="bg-indigo-200">年收入</mark>大概多少？是<mark className="bg-indigo-200">税前</mark>还是税后收入？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>税前大概18万，税后15万左右。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>好的，请问您<mark className="bg-red-200">名下</mark>有<mark className="bg-red-200">房产</mark>或车辆吗？</span>
                  </div>
                  <div className="flex">
                    <span className="text-green-600 font-medium w-12">客户：</span>
                    <span>有一套住房和一辆车。</span>
                  </div>
                  <div className="flex">
                    <span className="text-blue-600 font-medium w-12">客服：</span>
                    <span>请您<mark className="bg-gray-200">如实</mark>填写申请信息，确保<mark className="bg-gray-200">准确</mark>无误。现在为您推荐合适的信用卡产品...</span>
                  </div>
                </div>
                <div className="mt-3 p-2 bg-green-50 rounded text-green-700 text-xs">
                  <p><strong>合规原因：</strong>客服正确收集了完整的收入信息（包含年收入和税前情况），所有必要信息都已获取</p>
                  <p><strong>条件匹配：</strong>a✓ && d✓ && (e✓ && f✓)✓ && g✓ && h✓ → !(e && f) = false，整个表达式为false，规则不触发</p>
                </div>
              </div>
            </div>
          </div>

          {/* 注意事项 */}
          <div className="bg-amber-50 border border-amber-200 p-3 rounded-lg">
            <h4 className="font-semibold text-amber-800 mb-2">⚠️ 注意事项</h4>
            <ul className="text-xs text-amber-700 space-y-1 list-disc list-inside">
              <li>条件E和F必须同时满足才能正确收集收入信息</li>
              <li>使用"!"表示条件不成立，如!c表示客服未询问发卡行和额度</li>
              <li>逻辑表达式中的"&&"表示"并且"，"||"表示"或者"</li>
              <li>两种违规情况任一成立都会触发规则</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}; 