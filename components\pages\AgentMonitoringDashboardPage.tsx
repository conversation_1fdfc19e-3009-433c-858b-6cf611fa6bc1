import React, { useState, useEffect } from 'react';
import { PageHeader } from '../PageHeader';
import { motion } from 'framer-motion';
import { User, Phone, Smile, Frown, MicOff, AlertCircle, Search, Filter, Headphones, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Tooltip } from '../common/Tooltip';

// --- Types and Mock Data ---

type AgentStatus = 'online' | 'on-call' | 'wrap-up' | 'offline';
type Sentiment = 'positive' | 'neutral' | 'negative';

interface AlertInfo {
    type: 'sentiment' | 'silence' | 'keyword';
    message: string;
    severity: 'high' | 'medium' | 'low';
}

interface Agent {
    id: string;
    name: string;
    team: string;
    status: AgentStatus;
    callDuration: number; // in seconds
    sentiment: Sentiment;
    isSilent: boolean;
    alerts: AlertInfo[];
    sessionId: string;
}

const initialAgents: Agent[] = [
    { id: 'A001', name: '王伟', team: '贷后一组', status: 'on-call', callDuration: 125, sentiment: 'negative', isSilent: false, alerts: [{ type: 'sentiment', message: '客户情绪激动', severity: 'high' }], sessionId: 'S_A001_01' },
    { id: 'A002', name: '李静', team: '贷后一组', status: 'on-call', callDuration: 45, sentiment: 'neutral', isSilent: false, alerts: [], sessionId: 'S_A002_01' },
    { id: 'A003', name: '张强', team: '贷后二组', status: 'wrap-up', callDuration: 320, sentiment: 'positive', isSilent: false, alerts: [], sessionId: 'S_A003_01' },
    { id: 'A004', name: '刘芳', team: '售前咨询', status: 'online', callDuration: 0, sentiment: 'neutral', isSilent: false, alerts: [], sessionId: '' },
    { id: 'A005', name: '陈浩', team: '贷后一组', status: 'on-call', callDuration: 210, sentiment: 'neutral', isSilent: true, alerts: [{ type: 'silence', message: '会话已静默20秒', severity: 'medium' }, {type: 'keyword', message: '客户提及竞品', severity: 'low'}], sessionId: 'S_A005_01' },
    { id: 'A006', name: '赵敏', team: '贷后二组', status: 'offline', callDuration: 0, sentiment: 'neutral', isSilent: false, alerts: [], sessionId: '' },
    { id: 'A007', name: '吴磊', team: '售前咨询', status: 'on-call', callDuration: 88, sentiment: 'positive', isSilent: false, alerts: [], sessionId: 'S_A007_01' },
    { id: 'A008', name: '孙秀英', team: '贷后一组', status: 'online', callDuration: 0, sentiment: 'neutral', isSilent: false, alerts: [], sessionId: '' },
];

const statusConfig: Record<AgentStatus, {
    text: string;
    color: string;
    dotColor: string;
    pillBg: string;
    pillKnob?: string;
}> = {
    'online': { text: '空闲', color: 'text-green-600', dotColor: 'bg-green-500', pillBg: 'bg-green-500', pillKnob: 'bg-green-100' },
    'on-call': { text: '通话中', color: 'text-blue-600', dotColor: 'bg-blue-500', pillBg: 'bg-blue-600', pillKnob: 'bg-blue-200' },
    'wrap-up': { text: '话后处理', color: 'text-yellow-600', dotColor: 'bg-yellow-500', pillBg: 'bg-yellow-500', pillKnob: 'bg-yellow-100' },
    'offline': { text: '离线', color: 'text-gray-500', dotColor: 'bg-gray-400', pillBg: 'bg-gray-300' },
};

const sentimentIcons: Record<Sentiment, React.ElementType> = {
    positive: Smile,
    neutral: User,
    negative: Frown,
};

const Legend = () => (
    <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-xs text-gray-600 mb-6 p-4 bg-white rounded-lg border border-gray-200/80 shadow-sm">
        <span className="font-semibold mr-2">图例:</span>
        {Object.entries(statusConfig).map(([key, status]) => (
            <div key={key} className="flex items-center">
                <span className={`w-3 h-3 rounded-full mr-1.5 ${status.dotColor}`}></span>
                <span>{status.text}</span>
            </div>
        ))}
        <div className="flex items-center">
            <Frown className="w-4 h-4 mr-1.5 text-red-500" />
            <span>负面情绪</span>
        </div>
        <div className="flex items-center">
            <MicOff className="w-4 h-4 mr-1.5 text-yellow-500" />
            <span>会话静默</span>
        </div>
        <div className="flex items-center">
            <AlertCircle className="w-4 h-4 mr-1.5 text-red-600" />
            <span>激活预警</span>
        </div>
    </div>
);

// --- Main Component ---

export const AgentMonitoringDashboardPage: React.FC = () => {
    const [agents, setAgents] = useState<Agent[]>(initialAgents);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterTeam, setFilterTeam] = useState('all');

    // Simulate real-time updates
    useEffect(() => {
        const interval = setInterval(() => {
            setAgents(prevAgents => 
                prevAgents.map(agent => {
                    if (agent.status === 'on-call') {
                        const newDuration = agent.callDuration + 1;
                        // Randomly change sentiment for demo purposes
                        const newSentiment = Math.random() < 0.05 ? (agent.sentiment === 'negative' ? 'neutral' : 'negative') : agent.sentiment;
                        return { ...agent, callDuration: newDuration, sentiment: newSentiment };
                    }
                    return agent;
                })
            );
        }, 1000);
        return () => clearInterval(interval);
    }, []);
    
    const teams = ['all', ...Array.from(new Set(initialAgents.map(a => a.team)))];
    
    const filteredAgents = agents
        .filter(a => a.name.toLowerCase().includes(searchTerm.toLowerCase()))
        .filter(a => filterTeam === 'all' || a.team === filterTeam);

    const formatDuration = (seconds: number) => {
        const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
        const secs = (seconds % 60).toString().padStart(2, '0');
        return `${mins}:${secs}`;
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="坐席实时监控"
                description="主动洞察所有坐席的实时服务状态与风险指标。"
                badge="质检工作台"
            />
            <main className="p-6 md:p-10">
                {/* Filters */}
                <div className="mb-6 flex justify-between items-center">
                    <div className="flex items-center gap-4">
                         <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="搜索坐席名称..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div className="flex items-center gap-2">
                             <Filter className="w-4 h-4 text-gray-500" />
                             <select 
                                value={filterTeam} 
                                onChange={e => setFilterTeam(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                            >
                                {teams.map(team => <option key={team} value={team}>{team === 'all' ? '所有团队' : team}</option>)}
                             </select>
                        </div>
                    </div>
                </div>

                {/* Legend */}
                <Legend />

                {/* Agent Grid */}
                <motion.div layout className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredAgents.map(agent => (
                        <motion.div layout key={agent.id} className="bg-white rounded-xl shadow-sm border border-gray-100 flex flex-col hover:shadow-md transition-shadow">
                            <div className="p-4 border-b">
                                <div className="flex justify-between items-start">
                                    <div>
                                        <h3 className="font-bold text-gray-800 text-lg">{agent.name}</h3>
                                        <p className="text-sm text-gray-500">{agent.team}</p>
                                    </div>
                                    <div className={`relative w-12 h-6 rounded-full flex items-center p-1 transition-colors ${statusConfig[agent.status].pillBg} ${agent.status === 'on-call' ? 'justify-end' : 'justify-start'}`}>
                                        {statusConfig[agent.status].pillKnob && (
                                            <motion.span
                                                layoutId={`knob-${agent.id}`}
                                                className={`block w-4 h-4 rounded-full ${statusConfig[agent.status].pillKnob} shadow`}
                                                animate={agent.status === 'on-call' ? { scale: [1, 1.05, 1] } : {}}
                                                transition={agent.status === 'on-call' ? { duration: 1.5, repeat: Infinity, ease: "easeInOut" } : { type: 'spring', stiffness: 700, damping: 30 }}
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="p-4 flex-grow space-y-3">
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">通话时长</span>
                                    <span className="font-mono font-semibold text-gray-800">{agent.status === 'on-call' ? formatDuration(agent.callDuration) : '--:--'}</span>
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">客户情绪</span>
                                    {React.createElement(sentimentIcons[agent.sentiment], { className: `w-5 h-5 ${agent.sentiment === 'negative' ? 'text-red-500' : 'text-gray-500'}`})}
                                </div>
                                 <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">会话静默</span>
                                    {agent.isSilent ? <MicOff className="w-5 h-5 text-yellow-500" /> : <span className="text-gray-400">-</span>}
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">激活预警</span>
                                    {agent.alerts.length > 0 ? 
                                        <Tooltip 
                                            text={
                                                <ul className="text-left p-1">
                                                    {agent.alerts.map((alert, index) => (
                                                        <li key={index} className="text-xs font-normal text-white flex items-center">
                                                            <AlertCircle size={12} className="mr-1.5 flex-shrink-0" />
                                                            {alert.message}
                                                        </li>
                                                    ))}
                                                </ul>
                                            }
                                            placement="top"
                                        >
                                            <div className="flex items-center text-red-600 font-bold cursor-pointer">
                                                <AlertCircle className="w-5 h-5 mr-1" /> {agent.alerts.length}
                                            </div>
                                        </Tooltip> : <span className="text-gray-400">-</span>
                                    }
                                </div>
                            </div>
                            <div className="p-3 bg-gray-50/70 border-t flex justify-end items-center gap-2">
                                <Tooltip text="监听 (需CTI集成)" placement="top">
                                    <button className="text-gray-400 p-1.5 rounded-lg cursor-not-allowed hover:bg-gray-200"><Headphones className="w-4 h-4" /></button>
                                </Tooltip>
                                <Tooltip text="强插 (需CTI集成)" placement="top">
                                    <button className="text-gray-400 p-1.5 rounded-lg cursor-not-allowed hover:bg-gray-200"><Zap className="w-4 h-4" /></button>
                                </Tooltip>
                                <Link to={`/qa-management/sessions/${agent.sessionId}`} 
                                    className={`text-sm font-semibold px-3 py-1.5 rounded-lg ${agent.status === 'on-call' ? 'text-blue-600 hover:bg-blue-100' : 'text-gray-400 cursor-not-allowed bg-gray-100'}`}>
                                    查看会话
                                </Link>
                            </div>
                        </motion.div>
                    ))}
                </motion.div>
                {filteredAgents.length === 0 && (
                     <div className="text-center py-20 text-gray-500">
                         <User className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                         <p className="text-lg">未找到符合条件的坐席</p>
                     </div>
                 )}
            </main>
        </div>
    );
};
