import React, { useState } from 'react';
import TextSimilarityConcepts from './textSimilarity/TextSimilarityConcepts';
import TextSimilarityConfigComponent, { TextSimilarityConfig } from './textSimilarity/TextSimilarityConfig';
import TextSimilarityTester from './textSimilarity/TextSimilarityTester';
import TextSimilarityTips from './textSimilarity/TextSimilarityTips';
import TextSimilarityCases from './textSimilarity/TextSimilarityCases';

/**
 * 文本相似度检查详细演示页面
 * 提供文本相似度检查算子的完整学习体验
 */
const TextSimilarityPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState<TextSimilarityConfig>({
    detectionRole: 'agent',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 5,
    similarityThreshold: 80,
    templates: ['您好，请问有什么可以帮助您的', '感谢您的来电'],
    singleSentence: false
  });

  // 测试文本
  const [testText, setTestText] = useState(
    '客服：您好，请问有什么可以帮助您的吗？\n客户：我想咨询一下你们的产品信息。\n客服：好的，我来为您详细介绍一下我们的产品特点。'
  );

  // 测试结果
  const [testResult, setTestResult] = useState<{
    matched: boolean;
    bestMatch?: {
      template: string;
      similarity: number;
    };
    matchedSentences: string[];
    details: string;
  } | null>(null);

  // 加载案例
  const loadCase = (caseConfig: TextSimilarityConfig, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                📊 文本相似度检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测实际文本是否在预置话术的相似度范围内，支持灵活的阈值配置和多模板匹配
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                🚀 基础入门
              </span>
              <span className="text-sm text-gray-500">
                算子类型：文字检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <TextSimilarityConcepts />

            {/* 配置演示 */}
            <TextSimilarityConfigComponent config={config} setConfig={setConfig} />

            {/* 实时测试 */}
            <TextSimilarityTester
              config={config}
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              setTestResult={setTestResult}
            />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <TextSimilarityCases onLoadCase={loadCase} />
          
            {/* 使用提示 */}
            <TextSimilarityTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>从基础开始</strong>：先了解相似度计算原理，体验不同阈值的效果</p>
                <p>• <strong>模板设计</strong>：学习如何设计高质量的话术模板，避免过于具体或宽泛</p>
                <p>• <strong>参数调优</strong>：通过实际测试找到最适合业务场景的相似度阈值</p>
                <p>• <strong>场景应用</strong>：结合具体业务需求设计检测规则和标准话术</p>
                <p>• <strong>性能优化</strong>：在保证准确性的前提下，合理控制模板数量和复杂度</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextSimilarityPage; 