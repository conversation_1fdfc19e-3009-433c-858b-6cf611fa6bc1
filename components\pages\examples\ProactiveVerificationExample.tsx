import React from 'react';
import { 
    Lightbulb, AlertCircle, CheckCircle, ListChecks, ArrowRight, Bot, User, 
    FileText, CheckSquare, Users, ScanText, Combine, Info, Gauge, Milestone, SlidersHorizontal
} from 'lucide-react';

const CaseTitle = ({ children }: { children: React.ReactNode }) => (
    <h2 className="text-2xl font-bold text-gray-800 border-l-4 border-blue-600 pl-4 mb-6">
        {children}
    </h2>
);

const ConfigItem = ({ icon, label, value, explanation }: { icon: React.ReactNode, label: string, value: React.ReactNode, explanation: string }) => (
    <div className="flex items-start py-4 border-b border-gray-200 last:border-b-0">
        <div className="w-1/4 flex items-center text-sm font-medium text-gray-700 pr-4">
            {icon}
            <span className="ml-2">{label}</span>
        </div>
        <div className="w-1/3 text-sm font-semibold text-blue-800 bg-blue-50 px-3 py-1 rounded-md">
            {value}
        </div>
        <div className="w-5/12 text-xs text-gray-600 pl-6 italic flex items-start">
             <Info size={14} className="mr-2 mt-0.5 flex-shrink-0 text-gray-400" />
            <span>{explanation}</span>
        </div>
    </div>
);

const KeywordsDisplay = ({ keywords }: { keywords: string[] }) => (
    <div className="flex flex-wrap gap-2">
        {keywords.map(kw => (
            <span key={kw} className="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">{kw}</span>
        ))}
    </div>
);


const DialogueBox = ({ speaker, text, highlight = false, speakerType }: { speaker: string, text: string, highlight?: boolean, speakerType: 'bot' | 'user' }) => (
    <div className={`flex items-start space-x-3 ${highlight ? 'p-3 bg-yellow-50 rounded-lg ring-2 ring-yellow-300' : ''}`}>
        {speakerType === 'bot' ? 
            <Bot className="w-8 h-8 text-blue-600 flex-shrink-0" /> :
            <User className="w-8 h-8 text-green-600 flex-shrink-0" />
        }
        <div>
            <p className="font-semibold text-gray-700">{speaker}</p>
            <p className="text-gray-600">{text}</p>
        </div>
    </div>
);


export const ProactiveVerificationExample: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="bg-white border-b border-gray-200 mb-6 -mx-6 -mt-6">
        <div className="max-w-full mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">综合示例：客服未主动核身且语速过快</h1>
          <p className="text-gray-600 mt-1">
            结合"关键词检查"和"语速检查"算子，检测服务流程规范与服务质量。
          </p>
        </div>
      </div>

      <div className="max-w-full mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-8">
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <CaseTitle>业务场景与规则拆解</CaseTitle>
            <p className="mb-6 text-gray-700">
                在处理涉及用户敏感信息的业务（如查询订单、修改账户）时，客服必须先通过验证客户身份信息（如手机号、订单号）来确保安全。同时，过快的语速会严重影响服务体验。此规则旨在捕获那些"既不核身、语速又快"的不规范服务行为。
            </p>
            <div className="space-y-4">
                <div className="flex items-start p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <ListChecks className="w-6 h-6 text-blue-600 mr-4 flex-shrink-0" />
                    <div>
                        <h3 className="font-semibold text-blue-800">[条件 A] 客户咨询敏感业务</h3>
                        <p className="text-sm text-blue-700">使用【关键词检查】算子，检测客户是否提及"订单"、"物流"、"账户"等词语。</p>
                    </div>
                </div>
                <div className="flex items-start p-4 bg-green-50 border border-green-200 rounded-lg">
                    <ListChecks className="w-6 h-6 text-green-600 mr-4 flex-shrink-0" />
                    <div>
                        <h3 className="font-semibold text-green-800">[条件 B] 客服主动进行核身</h3>
                        <p className="text-sm text-green-700">使用【关键词检查】算子，检测客服是否提及"订单号"、"手机号"、"核对下信息"等词语。</p>
                    </div>
                </div>
                <div className="flex items-start p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <ListChecks className="w-6 h-6 text-orange-600 mr-4 flex-shrink-0" />
                    <div>
                        <h3 className="font-semibold text-orange-800">[条件 C] 客服语速超标</h3>
                        <p className="text-sm text-orange-700">使用【语速检查】算子，检测客服在整通对话中的平均语速是否超过300字/分钟。</p>
                    </div>
                </div>
                <div className="flex items-center justify-center p-4">
                    <p className="text-lg font-bold text-gray-700 font-mono">
                        最终逻辑: <span className="text-red-600">A && !B && C</span>
                    </p>
                </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <CaseTitle>规则配置详情</CaseTitle>
            <div className="space-y-8">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">[条件 A] 客户咨询敏感业务 (关键词检查)</h3>
                    <div className="divide-y divide-gray-200">
                         <ConfigItem icon={<Users size={16} />} label="检测角色" value={<span className="flex items-center"><User size={16} className="mr-2 text-green-600"/> 客户</span>} explanation="业务请求由客户发起，因此检测角色为'客户'。" />
                         <ConfigItem icon={<Milestone size={16} />} label="前置条件" value="无" explanation="这是逻辑链的起点，是触发事件，无需前置条件。" />
                         <ConfigItem icon={<ScanText size={16} />} label="检测范围" value="全文" explanation="客户可能在对话任意环节提出请求，需覆盖全程。" />
                         <ConfigItem icon={<FileText size={16} />} label="关键词" value={<KeywordsDisplay keywords={["订单", "物流", "我的账户", "消费记录", "退款"]} />} explanation="这些是典型的涉及敏感信息的业务词。" />
                         <ConfigItem icon={<Combine size={16} />} label="分析方式" value="单句分析" explanation="客户的请求通常在单句话内就能完整表达，无需合并句子分析。" />
                         <ConfigItem icon={<CheckSquare size={16} />} label="检测类型" value="包含任意一个关键词" explanation="只要提到任何一个敏感业务词，就认为条件成立。" />
                         <ConfigItem icon={<SlidersHorizontal size={16} />} label="扩展功能" value="无" explanation="基础的请求检测，无需使用'单句话内生效'等高级功能。" />
                    </div>
                </div>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">[条件 B] 客服主动进行核身 (关键词检查)</h3>
                     <div className="divide-y divide-gray-200">
                        <ConfigItem icon={<Users size={16} />} label="检测角色" value={<span className="flex items-center"><Bot size={16} className="mr-2 text-blue-600"/> 客服</span>} explanation="核身是客服的规定动作，检测角色为'客服'。" />
                        <ConfigItem icon={<Milestone size={16} />} label="前置条件" value="无" explanation="核身动作是否发生是一个独立事件，其逻辑关系在最终规则中定义，无需前置。" />
                        <ConfigItem icon={<ScanText size={16} />} label="检测范围" value="全文" explanation="确保在对话全程中捕获客服的核身行为。" />
                        <ConfigItem icon={<FileText size={16} />} label="关键词" value={<KeywordsDisplay keywords={["订单号", "手机号", "核对", "验证", "您本人", "身份证"]} />} explanation="这些是客服在核身时会使用的标准用语。" />
                        <ConfigItem icon={<Combine size={16} />} label="分析方式" value="单句分析" explanation="核身用语通常也是在单句话内完整表达。" />
                        <ConfigItem icon={<CheckSquare size={16} />} label="检测类型" value="包含任意一个关键词" explanation="只要客服说了任何一个核身用语，就认为他尝试过核身。" />
                        <ConfigItem icon={<SlidersHorizontal size={16} />} label="扩展功能" value="无" explanation="基础的核身行为检测，无需使用高级功能。" />
                    </div>
                </div>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">[条件 C] 客服语速超标 (语速检查)</h3>
                     <div className="divide-y divide-gray-200">
                        <ConfigItem icon={<Users size={16} />} label="检测角色" value={<span className="flex items-center"><Bot size={16} className="mr-2 text-blue-600"/> 客服</span>} explanation="语速是对客服服务质量的考量，检测角色为'客服'。" />
                        <ConfigItem icon={<Milestone size={16} />} label="前置条件" value="无" explanation="语速是否超标是一个独立的服务质量维度，无需前置。" />
                        <ConfigItem icon={<Gauge size={16} />} label="检查逻辑" value="语速 &gt; 300字/分钟，忽略 &lt; 5字的句子" explanation="超过300字/分属于明显语速偏快；忽略短句可防止'嗯、啊'等对话词干扰准确性。" />
                        <ConfigItem icon={<Combine size={16} />} label="检查方式" value="检测整个对话的平均语速" explanation="我们关心的是客服在整通对话中的持续表现，而非单一句子的瞬时速度，因此采用平均值检测。" />
                    </div>
                </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <CaseTitle>实战演练</CaseTitle>
            
            <div className="mb-8">
                <div className="flex items-center mb-4">
                    <AlertCircle className="w-6 h-6 text-red-500 mr-3" />
                    <h3 className="text-xl font-semibold text-gray-800">规则命中示例 (未核身且语速快)</h3>
                </div>
                <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                    <DialogueBox speaker="客户" text="喂，帮我查下我上周那个订单到哪了？" highlight={true} speakerType="user" />
                    <DialogueBox speaker="客服" text="好的女士您稍等我立刻为您查询处理请您稍等片刻马上就好。" highlight={true} speakerType="bot" />
                </div>
                <p className="mt-3 text-sm text-gray-600">
                    <span className="font-bold text-red-600">分析</span>：客户提及"订单"(**A成立**)，客服未提及任何核身关键词(**B不成立，!B为真**)，且客服语速极快，超过了300字/分钟的阈值(**C成立**)。最终 `A && !B && C` 为真，规则**命中**。
                </p>
            </div>

            <div>
                 <div className="flex items-center mb-4">
                    <CheckCircle className="w-6 h-6 text-green-500 mr-3" />
                    <h3 className="text-xl font-semibold text-gray-800">规则未命中示例 (已核身或语速正常)</h3>
                </div>
                <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                    <DialogueBox speaker="客户" text="喂，帮我查下我上周那个订单到哪了？" highlight={true} speakerType="user" />
                    <DialogueBox speaker="客服" text="好的女士，为了您的账户安全，我需要先跟您核对下您的手机号可以吗？" highlight={false} speakerType="bot" />
                </div>
                <p className="mt-3 text-sm text-gray-600">
                     <span className="font-bold text-green-600">分析</span>：客户提及"订单"(**A成立**)，但客服说了"核对下您的手机号"(**B成立，!B为假**)，此时即使客服语速快，逻辑链 `A && !B && C` 也已中断，规则**未命中**。同理，如果客服未核身但语速正常（C不成立），规则也未命中。
                </p>
            </div>
          </div>

        </div>
        <div className="lg:col-span-1">
          <div className="sticky top-6 bg-white p-6 rounded-lg shadow-sm border">
             <div className="flex items-center mb-4">
                <Lightbulb className="w-6 h-6 text-yellow-500 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">本例学习要点</h2>
            </div>
            <ul className="space-y-4 text-gray-700">
                <li className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                    <span>
                        <span className="font-semibold">多算子组合</span>:
                        本例演示了如何将 `关键词检查` (文字类) 和 `语速检查` (语音类) 两个不同维度的算子组合，实现更复杂的质检逻辑。
                    </span>
                </li>
                 <li className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                    <span>
                        <span className="font-semibold">复杂逻辑链</span>:
                        通过 `A && !B && C` 这种包含"非"逻辑的与关系链，可以精确定义一个多步骤、有先后顺序的违规场景。
                    </span>
                </li>
                 <li className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                    <span>
                        <span className="font-semibold">流程合规性质检</span>:
                        这类组合规则不再局限于单一的服务质量问题，而是能有效地对客服是否遵守特定服务流程（如安全核身）进行自动化监督。
                    </span>
                </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProactiveVerificationExample; 