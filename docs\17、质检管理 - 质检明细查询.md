
---

### 页面十五：质检明细查询 (QualityDetailQueryPage.tsx)

#### 1. 核心定位与目标
该页面是系统内**最强大、最灵活的原始数据查询入口**。与“质检任务详情页”不同，后者是围绕一个“任务批次”来查看数据，而本页面则允许用户**跨任务、跨时间、跨团队**，根据任意组合的条件来查询系统中任何一条通话的质检明细记录。其核心目标是满足各种临时的、复杂的、探索性的数据查询和审计需求。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题 (根据角色动态变化)**:
    *   **质检主管视角**: "质检明细查询"
    *   **班组长视角**: "团队质检成绩" (或类似，强调团队范围)
    *   **坐席视角**: "我的质检成绩"
*   **副标题**: "查看和管理质检成绩明细信息"
*   **图标**: `Search` (搜索图标)，直观地表明其查询功能。
*   **核心操作**: `导出数据` (`Download`图标)，允许用户将当前查询结果导出为文件（如JSON或CSV），用于离线分析或归档。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
这是该页面的**核心和灵魂**，提供了系统中最全面的筛选维度。

*   **筛选字段 (根据角色动态显示)**:
    *   **基础信息**: `记录编号`、`所属任务`、`客户号码`。
    *   **人员信息**: `坐席` (姓名/工号，班组长和主管可见)、`所属班组` (主管可见)。
    *   **时间信息**: `通话开始时间` (支持日期时间范围选择)。
    *   **分数信息**: `最终得分` (支持数值范围查询)。
    *   **结果信息**:
        *   `质检结果`: “合格”、“不合格”。
        *   `申诉状态`: “可申诉”、“申诉中”、“已处理”、“已过期”。
        *   `申诉结果`: “成功”、“失败”。
*   **交互**:
    *   用户可以通过组合这些筛选条件，构建出非常复杂的查询逻辑。例如：
        *   查询“A组”在“上个月”所有“最终得分低于70分”且“申诉失败”的记录。
        *   查询坐席“张三”所有由“营销质检任务_1”产生的记录。
        *   查询所有与客户号码“138********”相关的通话记录。

**c. 查询结果表格 (`Table`)**
*   **布局**: 以详细的表格形式展示所有满足筛选条件的质检记录。
*   **表格列 (根据角色动态显示)**:
    *   `序号`
    *   `记录编号`
    *   `所属任务`
    *   `坐席` (班组长和主管可见)
    *   `所属班组` (主管可见)
    *   `客户号码`
    *   `通话开始时间`
    *   `通话时长`
    *   `最终得分`: **核心指标**，同样通过Tooltip展示分数演进过程（AI -> 复核 -> 申诉）。
    *   `质检结果`: “合格”/“不合格”徽章。
    *   `申诉信息`: 一个复合信息列，用徽章和文字清晰展示申诉状态、结果和关键时间。
    *   `操作`:
        *   **查看详情 (`Eye`图标)**: 所有角色都可以点击跳转到 **[多模式会话详情页]**，深入了解该次通话的全部细节。
        *   **发起申诉 (`AlertTriangle`图标)**: 仅在坐席视角下，对符合条件的记录显示此按钮，提供申诉入口。

**d. 分页组件 (`UnifiedPagination`)**
*   由于查询结果可能非常庞大，分页功能是必不可少的。

#### 3. 核心交互与操作
*   **探索性查询**: 与其他目标明确的页面不同，此页面的主要价值在于其**探索性**。管理者可以用它来验证某个假设、调查某个特定事件或进行深度的数据挖掘。
*   **数据审计与追溯**: 它是系统最全面的审计工具。任何一条记录，无论来自哪个任务、哪个计划，都可以在这里被找到并追溯其完整的生命周期。
*   **权限隔离**: 页面设计巧妙地通过**动态显示/隐藏筛选条件和表格列**来实现不同角色的数据权限隔离。坐席只能查自己的数据，班组长能查本组的，而主管拥有全局查询权限，这保证了数据的安全性和合规性。
*   **数据导出**: 导出功能使得用户可以将系统内的数据与外部工具（如Excel, Tableau, Python脚本）结合，进行更复杂的自定义分析和可视化。

---