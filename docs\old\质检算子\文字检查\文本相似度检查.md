
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 文字检查 > 文本相似度检查**

产品详情 | ❤️ 我的收藏

# **文本相似度检查**
更新时间: 2025-01-23 16:57:12

本文介绍文本相似度检查如何进行配置。

*   **功能介绍**：检测实际文本是否在预置话术的相似度范围内。
*   **配置方法**：
    *   **检测角色**：角色分为：客服、客户、所用角色。
    *   **检测范围**：全文、指定范围
        *   全文：检测全文。
        *   指定范围：可指定第 a~b 句。
    *   **相似度**：输入相似度阈值，不超过 100 的正整数。建议设置为 80%的相似度
    *   **相似话术**：请输入示例语句，请句尽量标准不要口语化，例如“我想投诉你们部门”，可以录入多种表述。
    *   **扩展功能**：单句活内生效
        *   匹配时是否限制在单句话中（单句话是指中间没有逗号、句号等），举例说明：
            测试文本：“你好，这里是 xxx，向您推荐一款产品”；
            当勾选了“单句活内生效”时，会将测试文本拆分为“你好”、“这里是 xxx”、“向您推荐一款产品”3 小段话分别进行分析，当其中 1 段或多段与当前条件匹配时，才算命中；
            当未勾选“单句活内生效”时，会将测试文本当作一整段话进行质检。
*   **测试**：在填写完文本相似度检查后，可以在测试中查看配置的效果。

**(图片为UI配置界面与测试结果截图)**

**a ^ 文本相似度检查**
*   **检测角色**: 客服
*   **检测范围**: 全文
*   **相似度达到**: 80 %
*   **相似话术**: 输入按enter键添加话术。
    *   你好，请问您有什么问题 [面] [复制]
*   **扩展功能**: [ ] 单句活内生效
*   **测试**: [ 请问你有什么问题? 9/200 ] [ 测试 ]

**b ^ 关键词**
*   **检测角色**:
    *   [✔] **已命中**
        命中检查规则，与「你好，请问您有什么问题」相似度达到了 99

*   **使用示例**：假设要检测客服是否规范使用欢迎语，使用文本相似度检查算子，输入符合规范的语句，如“你好，请问有什么可以帮到您的”“您好，请问要咨询什么”，允许客服实际使用的话语和预置语句有一定范围的误差。