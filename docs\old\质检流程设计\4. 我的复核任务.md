# 4. 我的复核任务

“我的复核任务”是质检员、主管等角色处理所有待办人工审核任务的核心工作台。它取代了传统质检系统中分散的、来源不一的多个任务入口（如“我的抽检任务”、“我的申诉任务”等），构建了一个统一、高效、智能的人机协同中心。

## 一、核心设计理念：聚焦于“复核”

本模块的核心设计理念是：**人工质检 = 对AI筛选结果的复核**。

在这个理念下，人工不再是发现问题的主要力量，而是作为专家，对AI系统预处理、筛选出的“疑似问题”或“高价值会话”进行最终的确认、定性和深度分析。这彻底改变了传统质检员“大海捞针”式的工作模式，将宝贵的人力资源聚焦在最需要人类智慧的环节。

因此，系统摒弃了“抽样任务”的概念，所有任务都以“复核任务”的形式存在，强调其本质是“对AI分析结果的二次审核”。

## 二、统一任务池：任务来源与优先级

所有待处理的会话都会汇入一个统一的复核任务池，用户在“我的复核任务”界面中集中处理。任务主要有两个来源，系统会根据来源自动为其赋予不同的优先级：

| 任务来源 | 触发机制 | 核心价值 | 默认优先级 | 界面标签建议 |
| :--- | :--- | :--- | :--- | :--- |
| **实时分析预警** | 实时分析引擎匹配到高危规则，并配置了“生成复核任务”的动作。 | 风险回溯与处置 | **高** | `[实时预警]` |
| **复核策略筛选** | “复核策略”根据调度周期，从AI全量质检的数据库中筛选出会话。 | 智能发现与精准抽样 | **中 / 低** | `[AI筛选]` |

### 1. 来源一：实时分析预警 (High-Priority Tasks)
- **是什么**：当通话过程中触发了严重的预警规则（如客户明确表示要投诉、出现违禁词等），系统在发出警报的同时，会自动创建一个复核任务。
- **为什么**：确保所有最严重的风险事件都留痕，并被100%事后复盘，用于追溯问题、总结经验和对相关人员进行辅导。
- **特点**：任务产生最快，优先级最高，需要尽快处理。

### 2. 来源二：复核策略筛选 (Strategy-Driven Tasks)
- **是什么**：这是常规复核任务的主要来源。QA管理员可以配置多种“复核策略”，替代传统的“随机抽样计划”。
- **示例策略**：
    - `筛选所有AI评分低于60分的通话，分配给对应团队主管复核`
    - `筛选所有被AI标记为“无效致歉”的通话，每天生成10个任务给资深质检员`
    - `筛选提及竞品A，且客户情绪为负面的通话，全量生成复核任务`
- **为什么**：变“盲抽”为“精抽”，基于AI对100%会话的分析结果，精准地找到最值得人工去听、去分析的通话录音，极大地提升了质检效率和效果。
- **特点**：任务是周期性、批量生成的，优先级根据策略本身的重要性而定。

## 三、界面设计 (UI/UX)

“我的复核任务”列表页面的设计应突出**清晰、高效、可追溯**的特点。

```
+--------------------------------------------------------------------------------------------------+
| 我的复核任务 (99+)                                                                                |
|                                                                                                  |
|  筛选器: [任务来源 v]  [优先级 v]  [任务状态 v]  [分配时间 v]  [坐席 v]  | [搜索：会话ID/坐席工号] |
+--------------------------------------------------------------------------------------------------+
|                                                                                                  |
| [ ] 会话ID: 1122334455   [高] [实时预警]                                                          |
|     - 触发规则: 客户投诉                                                                         |
|     - 坐席: 张三 (007)      分配时间: 2024-07-01 10:30:05      通话时长: 05:33                     |
|     -------------------------------------------------------------------------------------------  |
|                                                                                                  |
| [ ] 会话ID: 1122334466   [中] [AI筛选]                                                           |
|     - 来源策略: AI评分低于60分 (得分: 58)                                                        |
|     - 坐席: 李四 (008)      分配时间: 2024-07-01 09:00:10      通话时长: 12:45                     |
|     -------------------------------------------------------------------------------------------  |
|                                                                                                  |
| [ ] 会话ID: 1122334477   [中] [AI筛选]                                                           |
|     - 来源策略: 无效道歉模型命中                                                                 |
|     - 坐席: 王五 (009)      分配时间: 2024-07-01 09:00:10      通话时长: 08:12                     |
|     -------------------------------------------------------------------------------------------  |
|                                                                                                  |
| ...                                                                                              |
+--------------------------------------------------------------------------------------------------+
|                                                                    <<  1  2  3  ...  10  >>        |
+--------------------------------------------------------------------------------------------------+

```

### 关键设计元素：

1.  **统一列表 (Unified List)**：所有来源的任务都在一个列表中展示，避免用户在不同页面间切换。
2.  **清晰的标识 (Clear Labels)**：
    -   **优先级标签**：如 `[高]`, `[中]`, `[低]`，让用户一眼识别任务的重要性。
    -   **来源标签**：如 `[实时预警]`, `[AI筛选]`，让用户清楚任务的背景。
3.  **关键信息摘要 (Key Info Snippet)**：
    -   除了会话ID、坐席等基础信息外，最关键的是要显示 **任务的触发原因**。
    -   例如，“触发规则: 客户投诉” 或 “来源策略: AI评分低于60分 (得分: 58)”。这为质检员提供了必要的上下文，使其在点开任务前就有了初步的预期，极大地提升了处理效率。
4.  **强大的筛选与搜索 (Powerful Filtering & Search)**：用户可以根据任务来源、优先级、状态等多个维度进行筛选，快速定位到自己关心的任务。

点击任一任务，即可进入标准的 **质检工作台 (Review Workstation)** 页面，进行录音播放、文本查看、评分、填写评语等操作。