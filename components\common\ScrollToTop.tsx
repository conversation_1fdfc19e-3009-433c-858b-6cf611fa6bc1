import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * 路由切换时自动滚动到页面顶部
 * 通过操作ID为'main-content'的元素来实现
 */
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      // 使用setTimeout确保在DOM更新后再执行滚动
      const timer = setTimeout(() => {
        mainContent.scrollTop = 0;
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [pathname]);

  return null;
}

export default ScrollToTop; 