import React, { useState, useMemo, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { PageHeader } from '../PageHeader';
import { Plus, Search, Edit, Copy, Trash2, ChevronLeft, ChevronRight, FileText, Code, BrainCircuit, Microscope, SlidersHorizontal, ChevronDown, Sparkles } from 'lucide-react';
import { CreateRuleForm } from '../CreateRuleForm';
import { CreateRuleProForm } from '../CreateRuleProForm';

interface Condition {
    id: string;
    type: string;
}

interface Rule {
    id: string;
    name: string;
    status: 'enabled' | 'disabled';
    detectionLogic: 'AND' | 'OR';
    conditions: Condition[];
    creator: string;
    lastModified: string;
    description: string;
    importance: string;
    category: string;
    ruleType: string;
    effectiveTime: string;
    manualReview: string;
}

const initialRulesData: Rule[] = [
  { id: 'RULE-001', name: '客户不满与合规词检测', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r1c1', type: '关键词检查'}, {id: 'r1c2', type: '正则表达式检查'}], creator: '王主管', lastModified: '2024-05-20 10:30', description: '识别客户不满关键词，并排除特定合规术语。', importance: '严重违规', category: '合规风险', ruleType: '服务规范', effectiveTime: '全年期生效', manualReview: '需要' },
  { id: 'RULE-002', name: '标准开场白检测', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r2c1', type: '文本相似度检查'}], creator: '李静', lastModified: '2024-05-19 14:00', description: '检测坐席开场白是否符合"您好，很高兴为您服务"的标准句式。', importance: '轻度违规', category: '服务规范', ruleType: '服务规范', effectiveTime: '全年期生效', manualReview: '不需要' },
  { id: 'RULE-003', name: '合规声明-录音告知', status: 'disabled', detectionLogic: 'AND', conditions: [{id: 'r3c1', type: '正则表达式检查'}], creator: '王主管', lastModified: '2024-05-18 09:00', description: '验证坐席是否在通话开始时进行了录音告知。', importance: '中度违规', category: '合规风险', ruleType: '合规风险', effectiveTime: '全年期生效', manualReview: '需要' },
  { id: 'RULE-004', name: '客户情绪-激动模型', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r4c1', type: '客户模型检测'}, {id: 'r4c2', type: '能量检测'}], creator: '系统', lastModified: '2024-05-17 11:45', description: '使用AI模型结合能量检测判断客户是否存在激动情绪。', importance: '中度违规', category: '客户体验', ruleType: '客户体验', effectiveTime: '全年期生效', manualReview: '需要' },
  { id: 'RULE-005', name: '身份证号码格式校验', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r5c1', type: '正则表达式检查'}], creator: '张三', lastModified: '2024-05-16 16:20', description: '通过正则表达式验证身份证号码格式是否正确。', importance: '严重违规', category: '信息安全', ruleType: '信息安全', effectiveTime: '全年期生效', manualReview: '需要' },
  { id: 'RULE-006', name: '客服辱骂检测模型', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r6c1', type: '客服模型检测'}], creator: '系统', lastModified: '2024-05-15 13:00', description: '监控客服在对话中是否存在不文明用语。', importance: '严重违规', category: '服务红线', ruleType: '服务红线', effectiveTime: '全年期生效', manualReview: '需要' },
  { id: 'RULE-007', name: '上下文重复询问', status: 'disabled', detectionLogic: 'AND', conditions: [{id: 'r7c1', type: '上下文重复检查'}], creator: '李静', lastModified: '2024-05-14 18:00', description: '检测坐席是否在短时间内重复询问客户相同的问题。', importance: '轻度违规', category: '服务效率', ruleType: '服务效率', effectiveTime: '全年期生效', manualReview: '不需要' },
  { id: 'RULE-008', name: '静音与抢话综合检测', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r8c1', type: '通话静音检查'}, {id: 'r8c2', type: '抢话设置'}], creator: '王主管', lastModified: '2024-05-13 10:10', description: '检测单次静音时长是否超过30秒，以及是否存在抢话。', importance: '中度违规', category: '服务质量', ruleType: '服务质量', effectiveTime: '全年期生效', manualReview: '不需要' },
  { id: 'RULE-009', name: '信息实体-手机号与邮箱', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r9c1', type: '信息实体检查'}, {id: 'r9c2', type: '正则表达式检查'}], creator: '张三', lastModified: '2024-05-12 11:55', description: '提取对话中客户提及的手机号码和邮箱地址。', importance: '轻度违规', category: '信息提取', ruleType: '信息提取', effectiveTime: '全年期生效', manualReview: '不需要' },
  { id: 'RULE-010', name: '大模型质检-金融产品推荐', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r10c1', type: '大模型检查'}, {id: 'r10c2', type: '关键词检查'}], creator: '王主管', lastModified: '2024-05-21 09:15', description: '使用大模型判断坐席的金融产品推荐是否合规、适当，并校验是否提及风险。', importance: '严重违规', category: '销售合规', ruleType: '销售合规', effectiveTime: '全年期生效', manualReview: '需要' },
];

const ALL_RULE_TYPES = [
    '关键词检查',
    '文本相似度检查',
    '正则表达式检查',
    '上下文重复检查',
    '信息实体检查',
    '通话静音检查',
    '语速检查',
    '抢话设置',
    '角色判断',
    '非正常挂机',
    '非正常接听',
    '录音时长检测',
    '能量检测',
    '对话语句数检测',
    '客服模型检测',
    '客户模型检测',
    '大模型检查',
    '随录参数检查'
];

const ruleTypeIcons = {
    '关键词检查': <FileText className="w-4 h-4" />,
    '文本相似度检查': <FileText className="w-4 h-4" />,
    '正则表达式检查': <Code className="w-4 h-4" />,
    '上下文重复检查': <FileText className="w-4 h-4" />,
    '信息实体检查': <FileText className="w-4 h-4" />,
    '客户模型检测': <BrainCircuit className="w-4 h-4" />,
    '客服模型检测': <BrainCircuit className="w-4 h-4" />,
    '大模型检查': <BrainCircuit className="w-4 h-4" />,
    '通话静音检查': <Microscope className="w-4 h-4" />,
    '语速检查': <Microscope className="w-4 h-4" />,
    '抢话设置': <Microscope className="w-4 h-4" />,
    '能量检测': <Microscope className="w-4 h-4" />,
    '角色判断': <Microscope className="w-4 h-4" />,
    '非正常挂机': <Microscope className="w-4 h-4" />,
    '非正常接听': <Microscope className="w-4 h-4" />,
    '录音时长检测': <Microscope className="w-4 h-4" />,
    '对话语句数检测': <Microscope className="w-4 h-4" />,
    '随录参数检查': <SlidersHorizontal className="w-4 h-4" />,
};

const ruleTypeColors = {
    '关键词检查': 'text-blue-600 bg-blue-100',
    '文本相似度检查': 'text-blue-600 bg-blue-100',
    '正则表达式检查': 'text-green-600 bg-green-100',
    '上下文重复检查': 'text-blue-600 bg-blue-100',
    '信息实体检查': 'text-blue-600 bg-blue-100',
    '客户模型检测': 'text-purple-600 bg-purple-100',
    '客服模型检测': 'text-purple-600 bg-purple-100',
    '大模型检查': 'text-purple-600 bg-purple-100',
    '通话静音检查': 'text-teal-600 bg-teal-100',
    '语速检查': 'text-teal-600 bg-teal-100',
    '抢话设置': 'text-teal-600 bg-teal-100',
    '能量检测': 'text-teal-600 bg-teal-100',
    '角色判断': 'text-teal-600 bg-teal-100',
    '非正常挂机': 'text-teal-600 bg-teal-100',
    '非正常接听': 'text-teal-600 bg-teal-100',
    '录音时长检测': 'text-teal-600 bg-teal-100',
    '对话语句数检测': 'text-teal-600 bg-teal-100',
    '随录参数检查': 'text-orange-600 bg-orange-100',
}

const importanceColors: { [key: string]: string } = {
    '轻度违规': 'bg-blue-100 text-blue-800',
    '中度违规': 'bg-yellow-100 text-yellow-800',
    '严重违规': 'bg-red-100 text-red-800',
};

const PAGE_SIZE = 8;

const ALL_IMPORTANCE_LEVELS = ['轻度违规', '中度违规', '严重违规'];

const Switch = ({ checked, onChange }: { checked: boolean, onChange: () => void }) => (
    <button
        type="button"
        onClick={onChange}
        className={`${
            checked ? 'bg-green-500' : 'bg-gray-300'
        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        role="switch"
        aria-checked={checked}
    >
        <span
            aria-hidden="true"
            className={`${
                checked ? 'translate-x-5' : 'translate-x-0'
            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
        />
    </button>
);

/**
 * 质检规则库页面
 * 提供质检规则的创建、查看、筛选、搜索和管理功能
 */
export const RuleLibraryPage: React.FC = () => {
    const [rules, setRules] = useState<Rule[]>(initialRulesData);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [importanceFilters, setImportanceFilters] = useState<string[]>([]);
    const [typeFilters, setTypeFilters] = useState<string[]>([]);
    const [isImportanceDropdownOpen, setIsImportanceDropdownOpen] = useState(false);
    const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [isCreateModalOpen, setCreateModalOpen] = useState(false);
    const [isCreateProModalOpen, setIsCreateProModalOpen] = useState(false);
    const importanceDropdownRef = useRef<HTMLDivElement>(null);
    const typeDropdownRef = useRef<HTMLDivElement>(null);
    
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (importanceDropdownRef.current && !importanceDropdownRef.current.contains(event.target as Node)) {
                setIsImportanceDropdownOpen(false);
            }
            if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target as Node)) {
                setIsTypeDropdownOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const handleImportanceFilterChange = (level: string) => {
        setImportanceFilters(prev =>
            prev.includes(level)
                ? prev.filter(l => l !== level)
                : [...prev, level]
        );
    };

    const handleTypeFilterChange = (type: string) => {
        setTypeFilters(prev =>
            prev.includes(type)
                ? prev.filter(t => t !== type)
                : [...prev, type]
        );
    };
    
    const filteredRules = useMemo(() => {
        return rules.filter(rule => {
            const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || rule.status === statusFilter;
            const matchesImportance = importanceFilters.length === 0 || importanceFilters.includes(rule.importance);
            const matchesType = typeFilters.length === 0 || rule.conditions.some(c => typeFilters.includes(c.type));
            return matchesSearch && matchesStatus && matchesImportance && matchesType;
        });
    }, [rules, searchTerm, statusFilter, importanceFilters, typeFilters]);

    const totalPages = Math.ceil(filteredRules.length / PAGE_SIZE);
    const paginatedRules = filteredRules.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

    const handlePageChange = (page: number) => {
        if (page > 0 && page <= totalPages) {
            setCurrentPage(page);
        }
    };
    
    const handleStatusToggle = (ruleId: string) => {
        setRules(currentRules =>
            currentRules.map(rule =>
                rule.id === ruleId
                    ? { ...rule, status: rule.status === 'enabled' ? 'disabled' : 'enabled' }
                    : rule
            )
        );
    };

    const handleAddRule = (newRuleData: {
        name: string;
        description: string;
        importance: string;
        category: string;
        detectionLogic: 'ALL' | 'ANY' | 'NONE' | 'CUSTOM';
        conditions: Condition[];
        ruleType: string;
        effectiveTime: string;
        manualReview: string;
    }) => {
        const newRule: Rule = {
            id: `RULE-${String(rules.length + 1).padStart(3, '0')}`,
            status: 'enabled',
            creator: '当前用户',
            lastModified: new Date().toLocaleString(),
            ...newRuleData,
            detectionLogic: newRuleData.detectionLogic === 'ALL' ? 'AND' : 'OR',
        };
        setRules(prevRules => [newRule, ...prevRules]);
        setCreateModalOpen(false);
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="质检规则库"
                description="在这里创建和管理所有独立的质检规则，它们是构成质检方案的基础。"
                badge="质检管理"
                actions={
                    <div className="flex items-center gap-3">
                    <button 
                        onClick={() => setCreateModalOpen(true)}
                        className="flex items-center bg-blue-600 text-white px-4 py-2 text-sm font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                        <Plus className="w-4 h-4 mr-2" />
                        创建规则
                    </button>
                        <button 
                            onClick={() => setIsCreateProModalOpen(true)}
                            className="flex items-center bg-purple-600 text-white px-4 py-2 text-sm font-semibold rounded-lg hover:bg-purple-700 transition-colors shadow-sm">
                            <Sparkles className="w-4 h-4 mr-2" />
                            创建规则 Pro
                        </button>
                    </div>
                }
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* Filters and Search */}
                    <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
                        <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="按规则名称搜索..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                        <div className="flex items-center gap-4">
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            >
                                <option value="all">所有状态</option>
                                <option value="enabled">已启用</option>
                                <option value="disabled">已禁用</option>
                            </select>
                            <div ref={importanceDropdownRef} className="relative">
                                <button
                                    onClick={() => setIsImportanceDropdownOpen(!isImportanceDropdownOpen)}
                                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors flex items-center justify-between w-48 bg-white"
                                >
                                    <span className="truncate">
                                        {importanceFilters.length === 0
                                            ? '所有重要程度'
                                            : importanceFilters.length === 1
                                            ? importanceFilters[0]
                                            : `已选 ${importanceFilters.length} 项`}
                                    </span>
                                    <ChevronDown className="w-4 h-4 ml-2 text-gray-500" />
                                </button>
                                {isImportanceDropdownOpen && (
                                    <div className="absolute z-10 mt-1 w-56 bg-white rounded-md shadow-lg border border-gray-200 max-h-80 overflow-y-auto">
                                        <div className="p-2 border-b border-gray-200">
                                            <label className="flex items-center w-full px-2 py-1.5 text-sm rounded-md hover:bg-gray-50 cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={importanceFilters.length === 0}
                                                    onChange={() => setImportanceFilters([])}
                                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                />
                                                <span className="ml-2 text-gray-700 font-medium">所有重要程度</span>
                                            </label>
                                        </div>
                                        <div className="p-2">
                                            {ALL_IMPORTANCE_LEVELS.map(level => (
                                                <label key={level} className="flex items-center w-full px-2 py-1.5 text-sm rounded-md hover:bg-gray-50 cursor-pointer">
                                                    <input
                                                        type="checkbox"
                                                        checked={importanceFilters.includes(level)}
                                                        onChange={() => handleImportanceFilterChange(level)}
                                                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                    />
                                                    <span className="ml-2 text-gray-700">{level}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div ref={typeDropdownRef} className="relative">
                                <button
                                    onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors flex items-center justify-between w-48 bg-white"
                                >
                                    <span className="truncate">
                                        {typeFilters.length === 0
                                            ? '所有类型'
                                            : typeFilters.length === 1
                                            ? typeFilters[0]
                                            : `已选 ${typeFilters.length} 项`}
                                    </span>
                                    <ChevronDown className="w-4 h-4 ml-2 text-gray-500" />
                                </button>
                                {isTypeDropdownOpen && (
                                    <div className="absolute z-10 mt-1 w-56 bg-white rounded-md shadow-lg border border-gray-200 max-h-80 overflow-y-auto">
                                        <div className="p-2 border-b border-gray-200">
                                            <label className="flex items-center w-full px-2 py-1.5 text-sm rounded-md hover:bg-gray-50 cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={typeFilters.length === 0}
                                                    onChange={() => setTypeFilters([])}
                                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                />
                                                <span className="ml-2 text-gray-700 font-medium">所有类型</span>
                                            </label>
                                        </div>
                                        <div className="p-2">
                                        {ALL_RULE_TYPES.map(type => (
                                            <label key={type} className="flex items-center w-full px-2 py-1.5 text-sm rounded-md hover:bg-gray-50 cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={typeFilters.includes(type)}
                                                    onChange={() => handleTypeFilterChange(type)}
                                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                />
                                                <span className="ml-2 text-gray-700">{type}</span>
                                            </label>
                                        ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Rules Table */}
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3">规则名称</th>
                                    <th scope="col" className="px-6 py-3">状态</th>
                                    <th scope="col" className="px-6 py-3">规则类型</th>
                                    <th scope="col" className="px-6 py-3">生效时间</th>
                                    <th scope="col" className="px-6 py-3">人工复核</th>
                                    <th scope="col" className="px-6 py-3">类型</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">最后修改</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedRules.map(rule => (
                                    <tr key={rule.id} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-6 py-4 align-top">
                                            <div className="flex items-center">
                                                <span className="font-semibold text-gray-900">{rule.name}</span>
                                                <span className={`ml-3 text-xs font-medium px-2 py-0.5 rounded-full ${importanceColors[rule.importance]}`}>
                                                    {rule.importance}
                                                </span>
                                            </div>
                                            <div className="text-xs text-gray-500 font-normal truncate max-w-sm mt-1">{rule.description}</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <Switch checked={rule.status === 'enabled'} onChange={() => handleStatusToggle(rule.id)} />
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.ruleType}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.effectiveTime}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.manualReview}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center flex-wrap gap-2 max-w-xs">
                                                {[...new Set(rule.conditions.map(c => c.type))].map(t => (
                                                    <span key={t} className={`flex items-center gap-1.5 px-2 py-1 text-xs font-medium rounded-full ${ruleTypeColors[t as keyof typeof ruleTypeColors]}`}>
                                                        {ruleTypeIcons[t as keyof typeof ruleTypeIcons]}
                                                        {t}
                                                    </span>
                                                ))}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.creator}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.lastModified}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                                onClick={() => handleStatusToggle(rule.id)}
                                                className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                                                title="编辑"
                                            >
                                                <Edit className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => handleStatusToggle(rule.id)}
                                                className="text-blue-600 hover:text-blue-900 p-1.5 hover:bg-blue-50 rounded-lg transition-colors mx-1"
                                                title="复制"
                                            >
                                                <Copy className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => handleStatusToggle(rule.id)}
                                                className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                                                title="删除"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredRules.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的规则。</p>
                        </div>
                    )}
                    
                    {/* Pagination */}
                    {totalPages > 1 && (
                         <div className="flex items-center justify-between pt-4">
                            <span className="text-sm text-gray-700">
                                共 <span className="font-semibold">{filteredRules.length}</span> 条规则，第 <span className="font-semibold">{currentPage}</span> / {totalPages} 页
                            </span>
                            <div className="inline-flex items-center -space-x-px">
                                <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} className="px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronLeft className="w-4 h-4" />
                                </button>
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                                    <button
                                        key={page}
                                        onClick={() => handlePageChange(page)}
                                        className={`px-3 py-2 leading-tight border border-gray-300 ${currentPage === page ? 'text-blue-600 bg-blue-50' : 'text-gray-500 bg-white'} hover:bg-gray-100 hover:text-gray-700`}
                                    >
                                        {page}
                                    </button>
                                ))}
                                <button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} className="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronRight className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    )}
                </motion.div>
            </main>
            
            {isCreateModalOpen && (
                <CreateRuleForm 
                    onClose={() => setCreateModalOpen(false)}
                    onSubmit={handleAddRule as any}
                    allRuleTypes={ALL_RULE_TYPES}
                />
            )}
            {isCreateProModalOpen && (
                <CreateRuleProForm 
                    onClose={() => setIsCreateProModalOpen(false)}
                    onSubmit={handleAddRule as any}
                />
            )}
        </div>
    );
};

export default RuleLibraryPage;
