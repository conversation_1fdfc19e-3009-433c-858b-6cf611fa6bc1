## 智能质检规则总括

-   **使用场景：**在进行人工质检时，通常会提前设置一些基本的服务规范规则，用于判断被质检对象是否按业务规范进行对话，例如客服接通电话后必须说问候语。类似的，智能质检规则则是在智能对话分析中判定被质检对象需要遵循的规范规则。
    
-   **规则构成：**智能质检**规则**，由一个或多个**条件**通过一定的**逻辑关系**编排组成，条件由**算子**和**检查范围**组成，具体我们在一个业务场景中说明规则、条件、算子间的关系及其简单使用。
    
-   **使用示例：**当客户询问有关在犹豫期内外退保费的问题时，客服需要清楚地回答犹豫期内退还所交全部保费，犹豫期外只退还现金价值。现要判断客服是否按业务要求进行作答，具体配置可参考下图：
    

![智能质检规则总括-算子条件与规则](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/3677039761/p606653.png)

-   图中针对客服设置算子A：文本相似度：“犹豫期内交保费”；算子B：语速检查：“300字/秒”等算子构成条件A，用于检测客服是否清楚地回答犹豫期内退还所交全部保费，犹豫期外只退还现金价值。
    
-   图中针对客户设置算子A：正则：“犹豫期.\*退保”；算子B：语速检测：“300字/秒”等算子构成条件B，用于检测客户是否询问了犹豫期退保问题。
    
-   图中选择逻辑表达式&&将条件A和条件B相关联形成规则，即条件A&&条件B，同时满足条件A和条件B则视为规则命中，具体来说就是检测到在客户询问有关在犹豫期内外退保费的问题时客服需要清楚地回答犹豫期内退还所交全部保费，犹豫期外只退还现金价值。
    

为了充分的了解并灵活运用智能对话分析服务强大的分析规则，我们再分别介绍四个基本的名词：算子、检查范围、条件、逻辑关系。

## 算子

算子可以理解为分析方式，例如智能对话分析中对对话的音频或文本文件进行的“关键词检查”、“通话静音检查”等分析方式。目前有以下四种类型的算子：

<table id="Zi0HC" tablewidth="708" tablecolswidth="177 177 177 177" autofit="false"><colgroup colwidth="1*"></colgroup><colgroup colwidth="1*"></colgroup><colgroup colwidth="1*"></colgroup><colgroup colwidth="1*"></colgroup><tbody><tr id="34738078b25u8"><td id="34738071b26jb" rowspan="1" colspan="1"><p id="34738070b2wqo"><b>文字检查类</b></p></td><td id="34738073b28ip" rowspan="1" colspan="1"><p id="34738072b2b0g"><b>语音检查类</b></p></td><td id="34738075b27t3" rowspan="1" colspan="1"><p id="34738074b2fod"><b>模型检查类</b></p></td><td id="34738077b2ry9" rowspan="1" colspan="1"><p id="34738076b2c7l"><b>其他检查类</b></p></td></tr><tr id="34738081b2pey"><td id="3473807ab232p" rowspan="1" colspan="1"><p id="34738079b24dn">关键词检查</p></td><td id="3473807cb2pr3" rowspan="1" colspan="1"><p id="3473807bb2a48">通话静音检查</p></td><td id="3473807eb29ri" rowspan="1" colspan="1"><p id="3473807db2fhf">客户检测模型</p></td><td id="34738080b2vi2" rowspan="1" colspan="1"><p id="3473807fb2z2x">随录参数检查</p></td></tr><tr id="3473a783b26e0"><td id="34738083b2ofl" rowspan="1" colspan="1"><p id="34738082b2u55">文本相似度检查</p></td><td id="34738085b2tyg" rowspan="1" colspan="1"><p id="34738084b2mnn">语速检查</p></td><td id="3473a780b2a7k" rowspan="1" colspan="1"><p id="34738086b2pye">客服检测模型</p></td><td id="3473a782b2q8t" rowspan="1" colspan="1"></td></tr><tr id="3473a78cb2zjs"><td id="3473a785b24w3" rowspan="1" colspan="1"><p id="3473a784b2xw3">正则表达式检查</p></td><td id="3473a787b2bse" rowspan="1" colspan="1"><p id="3473a786b2n62">抢话检查</p></td><td id="3473a789b2smb" rowspan="1" colspan="1"></td><td id="3473a78bb2ndy" rowspan="1" colspan="1"></td></tr><tr id="3473a795b2yrr"><td id="3473a78eb2z0u" rowspan="1" colspan="1"><p id="3473a78db29ll">上下文重复检查</p></td><td id="3473a790b2cvy" rowspan="1" colspan="1"><p id="3473a78fb2yyi">角色判断</p></td><td id="3473a792b270s" rowspan="1" colspan="1"></td><td id="3473a794b25jl" rowspan="1" colspan="1"></td></tr><tr id="3473ce93b2zcp"><td id="3473a797b27tr" rowspan="1" colspan="1"><p id="3473a796b29j6">信息实体检查</p></td><td id="3473a799b2uju" rowspan="1" colspan="1"><p id="3473a798b2r9c">非正常挂机</p></td><td id="3473ce90b2h7h" rowspan="1" colspan="1"></td><td id="3473ce92b2kmr" rowspan="1" colspan="1"></td></tr><tr id="3473ce9cb28ky"><td id="3473ce95b2ev9" rowspan="1" colspan="1"></td><td id="3473ce97b27ag" rowspan="1" colspan="1"><p id="3473ce96b2ddp">录音时长检测</p></td><td id="3473ce99b2o7a" rowspan="1" colspan="1"></td><td id="3473ce9bb2fpb" rowspan="1" colspan="1"></td></tr><tr id="3473cea5b28os"><td id="3473ce9eb2npc" rowspan="1" colspan="1"></td><td id="3473cea0b2ll2" rowspan="1" colspan="1"><p id="3473ce9fb2ejp">能量检测</p></td><td id="3473cea2b2fd4" rowspan="1" colspan="1"></td><td id="3473cea4b2p4c" rowspan="1" colspan="1"></td></tr><tr id="3473f5a3b21qm"><td id="3473cea7b2yis" rowspan="1" colspan="1"></td><td id="3473cea9b2kdr" rowspan="1" colspan="1"><p id="3473cea8b2hx5">对话语句数检查</p></td><td id="3473f5a0b2n4f" rowspan="1" colspan="1"></td><td id="3473f5a2b2k9b" rowspan="1" colspan="1"></td></tr></tbody></table>

## 检查范围

检查范围是指算子的使用范围，明确的检查范围会确定对应算子将在整段对话中选取哪一部分进行分析。检查范围由适用角色、前置条件、检测范围组成。

-   适用角色：当前条件用来检测哪个角色，可选值为所有角色/客服/客户 ，默认为客服；
    
-   前置条件：当其他的某个条件第X次或每次命中时，才会执行当前条件的检测，默认为无前置条件；当设置了前置条件时，分为 “每次、任意一次、第N次” 三种情况，我们具体来说明三种情况的区别：若一个规则有条件A和条件B两个条件，条件B的前置条件是条件A，条件之间的逻辑关系是a&&b，那么：
    
    -   每次：条件A每次命中时，条件B也都命中，规则才算命中。
        
    -   任意一次：若条件A命中了多次时，只要有其中一次条件B也命中了，则规则命中。
        
    -   第N次：条件A第X次命中时，条件B也命中，则规则命中。
        
-   检测范围，指定检测对话中的哪些句子，详情看下图：
    

![智能质检规则总括-检测范围](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/3677039761/p606654.png)

## 条件

条件由算子和其对应的检查范围组成，是规则的基本组成部分。

## 逻辑关系

-   逻辑关系是描述条件与条件之间的联系，一个或多个条件通过一定的逻辑关系编排组成规则，条件间的逻辑关系可以通过逻辑运算符进行表示。
    
-   逻辑关系的逻辑运算符（&&、||、! ）是计算机程序语言中的一种运算符，运算的最终结果只有两个值：真和假，在这里可以理解为多个条件使用逻辑运算符来判断一个规则如何才算命中，即“真”为命中，“假”为未命中。
    
-   例如一个规则有两个条件a和b，使用逻辑运算符表明条件a和b间的关系具体用例如下表所示：
    

<table id="hQ4jA" tablewidth="708" tablecolswidth="236 236 236" autofit="false"><colgroup colwidth="1*"></colgroup><colgroup colwidth="1*"></colgroup><colgroup colwidth="1*"></colgroup><tbody><tr id="347443c3b2ndc"><td id="34741cbeb2sre" rowspan="1" colspan="1"><p id="34741cbdb2ayi">运算符</p></td><td id="347443c0b27gy" rowspan="1" colspan="1"><p id="34741cbfb2xle">描述</p></td><td id="347443c2b2gs7" rowspan="1" colspan="1"><p id="347443c1b2ih3">例子</p></td></tr><tr id="347443cab29zh"><td id="347443c5b25ac" rowspan="1" colspan="1"><p id="347443c4b24mt">&amp;&amp;</p></td><td id="347443c7b2k7x" rowspan="1" colspan="1"><p id="347443c6b2973">称为逻辑与运算符，当且仅当运算符两边的条件都命中时，规则才算是命中。</p></td><td id="347443c9b2kj1" rowspan="1" colspan="1"><p id="347443c8b2ji5">逻辑关系为 a&amp;&amp;b 时，规则未命中</p></td></tr><tr id="347443d1b29pr"><td id="347443ccb2g46" rowspan="1" colspan="1"><p id="347443cbb2h9h">||</p></td><td id="347443ceb2fcu" rowspan="1" colspan="1"><p id="347443cdb2m9c">称为逻辑或运算符，当运算符两边的条件有一个命中时，规则即为命中。</p></td><td id="347443d0b27ue" rowspan="1" colspan="1"><p id="347443cfb259o">逻辑关系为 a||b 时，规则命中。</p></td></tr><tr id="347443d8b2q1a"><td id="347443d3b2bb5" rowspan="1" colspan="1"><p id="347443d2b2s66">!</p></td><td id="347443d5b2e4s" rowspan="1" colspan="1"><p id="347443d4b2irg">称为逻辑非运算符，用来反转条件的命中状态，例如一个条件为命中状态，则逻辑非运算符将得使之变为未命中。</p></td><td id="347443d7b2itu" rowspan="1" colspan="1"><p id="347443d6b2yik">逻辑关系为 a&amp;&amp;!b 时，规则命中，与第一种情况作对比，可以更加深入的理解。</p></td></tr></tbody></table>

**说明**

运算符中逻辑非（!）的优先级最高，与或运算（&&, ||）最低。可以用括号来改变运算次序，如!（a && b）就会先算a && b。