import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PageHeader } from '../PageHeader';
import { Plus, Search, ChevronLeft, ChevronRight, PlayCircle, PauseCircle, CheckCircle, XCircle, Clock, Loader, Edit, Trash2, Lightbulb } from 'lucide-react';
import { CreateTaskForm } from '../CreateTaskForm';
import { ConfirmationModal } from '../common/ConfirmationModal';
import { TaskManagementDesignGuide } from '../guides/TaskManagementDesignGuide';
import { Link } from 'react-router-dom';

// Data structure for a QA Task
interface QATask {
    id: string;
    name: string;
    type: 'one-time' | 'recurring';
    status: 'completed' | 'running' | 'pending' | 'failed' | 'scheduled' | 'paused';
    summary: string;
    qaScheme: string;
    progress: number; // 0-100
    totalItems: number;
    processedItems: number;
    creator: string;
    creationDate: string;
    executionInfo: string;
}

const initialTasksData: QATask[] = [
    {
        id: 'TASK-001',
        name: '2024年5月金融产品销售合规性质检',
        type: 'one-time',
        status: 'completed',
        summary: '时间: 2024-05-01 to 2024-05-31 | 抽样: 全量',
        qaScheme: '金融产品销售合规方案',
        progress: 100,
        totalItems: 1500,
        processedItems: 1500,
        creator: '王主管',
        creationDate: '2024-06-01',
        executionInfo: '完成于 2024-06-03',
    },
    {
        id: 'TASK-002',
        name: '每日贷后支持通话抽检',
        type: 'recurring',
        status: 'running',
        summary: '范围: 上一个自然日 | 团队: 贷后支持一/二组 | 抽样: 10%',
        qaScheme: '服务规范通用方案',
        progress: 75,
        totalItems: 850,
        processedItems: 637,
        creator: '系统自动',
        creationDate: '2024-05-20',
        executionInfo: '下次运行: 今天 02:00',
    },
    {
        id: 'TASK-003',
        name: '新员工(6月入职)在线服务质量检查',
        type: 'one-time',
        status: 'scheduled',
        summary: '时间: 2024-06-01 to 2024-06-15 | 渠道: 在线客服',
        qaScheme: '新员工考核方案',
        progress: 0,
        totalItems: 200,
        processedItems: 0,
        creator: '李经理',
        creationDate: '2024-06-14',
        executionInfo: '调度于 2024-06-16 09:00',
    },
    {
        id: 'TASK-004',
        name: 'Q2 VIP客户服务通话全量质检',
        type: 'one-time',
        status: 'failed',
        summary: '时间: 2024-04-01 to 2024-06-30 | 团队: VIP客户服务部',
        qaScheme: '客户体验优化方案',
        progress: 25,
        totalItems: 10000,
        processedItems: 2500,
        creator: '开发团队',
        creationDate: '2024-06-07',
        executionInfo: '失败于 2024-06-08',
    },
    {
        id: 'TASK-005',
        name: '每周长通话(>10min)检查',
        type: 'recurring',
        status: 'pending',
        summary: '范围: 上一个自然周 | 时长 > 600s | 抽样: 20%',
        qaScheme: '服务规范通用方案',
        progress: 0,
        totalItems: 0,
        processedItems: 0,
        creator: '系统自动',
        creationDate: '2024-06-01',
        executionInfo: '等待执行周期',
    },
    {
        id: 'TASK-006',
        name: '存量客户意向摸排（专项）',
        type: 'one-time',
        status: 'paused',
        summary: '时间: 2024-06-10 to 2024-06-30 | 抽样: 50%',
        qaScheme: '客户体验优化方案',
        progress: 40,
        totalItems: 8000,
        processedItems: 3200,
        creator: '李经理',
        creationDate: '2024-06-11',
        executionInfo: '暂停于 2024-06-12',
    },
];

interface StatusConfig {
    [key: string]: {
        text: string;
        icon: React.ElementType;
        color: string;
        bgColor: string;
        animate?: boolean;
    };
}

const statusConfig: StatusConfig = {
    completed: { text: '已完成', icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-100' },
    running: { text: '运行中', icon: Loader, color: 'text-blue-500', bgColor: 'bg-blue-100', animate: true },
    scheduled: { text: '已调度', icon: Clock, color: 'text-purple-500', bgColor: 'bg-purple-100' },
    paused: { text: '已暂停', icon: PauseCircle, color: 'text-yellow-500', bgColor: 'bg-yellow-100' },
    pending: { text: '待开始', icon: Clock, color: 'text-gray-500', bgColor: 'bg-gray-100' },
    failed: { text: '失败', icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-100' },
};

const ProgressBar = ({ progress }: { progress: number }) => (
    <div className="w-full bg-gray-200 rounded-full h-2.5">
        <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${progress}%` }}></div>
    </div>
);

const PAGE_SIZE = 10;

/**
 * 质检任务管理页面
 */
export const TaskManagementPage: React.FC = () => {
    const [tasks, setTasks] = useState<QATask[]>(initialTasksData);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingTask, setEditingTask] = useState<QATask | undefined>(undefined);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [taskToDelete, setTaskToDelete] = useState<string | null>(null);
    const [isDesignGuideOpen, setIsDesignGuideOpen] = useState(false);

    const filteredTasks = useMemo(() => {
        return tasks.filter(task => {
            const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
            return matchesSearch && matchesStatus;
        });
    }, [tasks, searchTerm, statusFilter]);

    const totalPages = Math.ceil(filteredTasks.length / PAGE_SIZE);
    const paginatedTasks = filteredTasks.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

    const handlePageChange = (page: number) => {
        if (page > 0 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    const handleCreateClick = () => {
        setEditingTask(undefined);
        setIsFormOpen(true);
    };

    const handleEditClick = (task: QATask) => {
        setEditingTask(task);
        setIsFormOpen(true);
    };

    const handlePauseTask = (taskId: string) => {
        setTasks(tasks => tasks.map(task => 
            task.id === taskId ? { ...task, status: 'paused' } : task
        ));
    };

    const handleResumeTask = (taskId: string) => {
        setTasks(tasks => tasks.map(task => 
            task.id === taskId ? { ...task, status: 'running' } : task
        ));
    };

    const handleFormSubmit = (formData: any) => {
        if(editingTask) {
            // Edit logic
            const updatedTasks = tasks.map(task =>
                task.id === editingTask.id ? { ...task, ...formData } : task
            );
            setTasks(updatedTasks);
        } else {
             // Create logic
            const newTask: QATask = {
                id: `TASK-${String(tasks.length + 1).padStart(3, '0')}`,
                name: formData.name,
                type: formData.taskType,
                status: 'pending',
                summary: '',
                qaScheme: formData.qaScheme,
                progress: 0,
                totalItems: Math.floor(Math.random() * 5000) + 200, // mock total items
                processedItems: 0,
                creator: '当前用户', // mock creator
                creationDate: new Date().toISOString().split('T')[0],
                executionInfo: '',
            };
            setTasks([newTask, ...tasks]);
        }
        setIsFormOpen(false);
    };

    const handleDeleteClick = (id: string) => {
        setTaskToDelete(id);
        setIsDeleteModalOpen(true);
    };

    const handleConfirmDelete = () => {
        if (taskToDelete) {
            setTasks(prevTasks => prevTasks.filter(task => task.id !== taskToDelete));
            setTaskToDelete(null);
        }
        setIsDeleteModalOpen(false);
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="质检任务管理"
                description="创建、监控和管理离线的批量质检任务。"
                badge="质检管理"
                actions={
                    <div className="flex items-center gap-3">
                        <button
                            onClick={() => setIsDesignGuideOpen(true)}
                            className="flex items-center bg-white text-gray-600 px-4 py-2 text-sm font-semibold rounded-lg hover:bg-gray-100 transition-colors shadow-sm border"
                        >
                            <Lightbulb className="w-4 h-4 mr-2" />
                            设计思路
                        </button>
                        <button 
                            onClick={handleCreateClick}
                            className="flex items-center bg-blue-600 text-white px-4 py-2 text-sm font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                            <Plus className="w-4 h-4 mr-2" />
                            创建任务
                        </button>
                    </div>
                }
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* Filters and Search */}
                    <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
                        <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="按任务名称搜索..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                        <div className="flex items-center gap-4">
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            >
                                <option value="all">所有状态</option>
                                {Object.entries(statusConfig).map(([key, { text }]) => (
                                    <option key={key} value={key}>{text}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Tasks Table */}
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3 min-w-[280px]">任务名称</th>
                                    <th scope="col" className="px-6 py-3">类型</th>
                                    <th scope="col" className="px-6 py-3">状态</th>
                                    <th scope="col" className="px-6 py-3 min-w-[200px]">进度</th>
                                    <th scope="col" className="px-6 py-3">所用方案</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">创建时间</th>
                                    <th scope="col" className="px-6 py-3">执行信息</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedTasks.map(task => {
                                    const StatusIcon = statusConfig[task.status].icon;
                                    const statusColor = statusConfig[task.status].color;
                                    const statusBgColor = statusConfig[task.status].bgColor;
                                    const isAnimated = statusConfig[task.status].animate;

                                    return (
                                        <tr key={task.id} className="bg-white border-b hover:bg-gray-50">
                                            <td className="px-6 py-4 font-semibold text-gray-800">
                                                <Link to={`/qa-management/tasks/${task.id}`} className="hover:text-blue-600 hover:underline transition-colors">
                                                    {task.name}
                                                </Link>
                                                <div className="text-xs text-gray-500 font-normal mt-1">{task.summary}</div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
                                                    task.type === 'one-time' ? 'bg-indigo-100 text-indigo-800' : 'bg-pink-100 text-pink-800'
                                                }`}>
                                                    {task.type === 'one-time' ? '一次性' : '周期性'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium whitespace-nowrap ${statusBgColor} ${statusColor}`}>
                                                    <StatusIcon className={`w-3.5 h-3.5 ${isAnimated ? 'animate-spin' : ''}`} />
                                                    {statusConfig[task.status].text}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="flex items-center gap-3">
                                                    <div className="w-full">
                                                        <ProgressBar progress={task.progress} />
                                                    </div>
                                                    <div className="text-xs font-semibold whitespace-nowrap">{task.progress}%</div>
                                                </div>
                                                <div className="text-xs text-gray-500 mt-1">{task.processedItems} / {task.totalItems}</div>
                                            </td>
                                            <td className="px-6 py-4">{task.qaScheme}</td>
                                            <td className="px-6 py-4 whitespace-nowrap">{task.creator}</td>
                                            <td className="px-6 py-4 whitespace-nowrap">{task.creationDate}</td>
                                            <td className="px-6 py-4">{task.executionInfo}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button onClick={() => handleEditClick(task)} className="text-gray-500 hover:text-blue-600 p-1.5 hover:bg-blue-50 rounded-lg" title="编辑"><Edit className="w-4 h-4" /></button>
                                                
                                                {task.status === 'running' && (
                                                    <button onClick={() => handlePauseTask(task.id)} className="text-gray-500 hover:text-yellow-600 p-1.5 hover:bg-yellow-50 rounded-lg ml-2" title="暂停任务"><PauseCircle className="w-4 h-4" /></button>
                                                )}

                                                {(task.status === 'paused' || task.status === 'pending') && (
                                                    <button onClick={() => handleResumeTask(task.id)} className="text-gray-500 hover:text-blue-600 p-1.5 hover:bg-blue-50 rounded-lg ml-2" title="执行/恢复任务"><PlayCircle className="w-4 h-4" /></button>
                                                )}

                                                <button onClick={() => handleDeleteClick(task.id)} className="text-gray-500 hover:text-red-600 p-1.5 hover:bg-red-50 rounded-lg ml-2" title="删除"><Trash2 className="w-4 h-4" /></button>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                    {filteredTasks.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的任务。</p>
                        </div>
                    )}
                    
                    {/* Pagination */}
                    {totalPages > 1 && (
                         <div className="flex items-center justify-between pt-4">
                            <span className="text-sm text-gray-700">
                                共 <span className="font-semibold">{filteredTasks.length}</span> 条任务，第 <span className="font-semibold">{currentPage}</span> / {totalPages} 页
                            </span>
                            <div className="inline-flex items-center -space-x-px">
                                <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} className="px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronLeft className="w-4 h-4" />
                                </button>
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                                    <button
                                        key={page}
                                        onClick={() => handlePageChange(page)}
                                        className={`px-3 py-2 leading-tight border border-gray-300 ${currentPage === page ? 'text-blue-600 bg-blue-50' : 'text-gray-500 bg-white'} hover:bg-gray-100 hover:text-gray-700`}
                                    >
                                        {page}
                                    </button>
                                ))}
                                <button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} className="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronRight className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    )}
                </motion.div>
            </main>
             <AnimatePresence>
                {isFormOpen && (
                    <CreateTaskForm
                        key={editingTask?.id || 'create'}
                        onClose={() => setIsFormOpen(false)}
                        onSubmit={handleFormSubmit}
                        initialData={editingTask ? {
                            id: editingTask.id,
                            name: editingTask.name,
                            taskType: editingTask.type,
                            qaScheme: editingTask.qaScheme,
                        } : undefined}
                    />
                )}
            </AnimatePresence>
            <ConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                title="确认删除任务"
                message="此操作不可撤销，您确定要删除这个任务吗？"
            />
            <TaskManagementDesignGuide
                isOpen={isDesignGuideOpen}
                onClose={() => setIsDesignGuideOpen(false)}
            />
        </div>
    );
};

export default TaskManagementPage; 