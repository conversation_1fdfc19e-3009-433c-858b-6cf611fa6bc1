### **4. 团队绩效报表 - 详细设计文档**

---

#### **4.1 报表概述**

*   **报表ID:** RPT_04
*   **报表名称:** 团队绩效报表
*   **面向用户:** 班组长、区域主管
*   **核心目的:**
    1.  量化评估团队在特定时间周期内的整体服务质量表现。
    2.  通过横向与纵向对比，精准识别团队的服务短板与优势所在。
    3.  从数据中发现优秀坐席与最佳实践，为团队赋能和培训提供依据。
*   **核心价值:** 为管理者提供一个数据驱动的团队管理驾驶舱，将模糊的"感觉"变为精确的指标，帮助他们进行高效的目标设定、绩效评估和成员辅导，最终提升整个团队的服务水平和客户满意度。

---

#### **4.2 界面布局设计 (UI Layout)**

报表继续沿用四区域布局，保持系统内各报表模块的一致性和易用性。

*   **A区 (顶部): 筛选器区域 (Filters Area)**
    *   用于定义报表统计的范围。
*   **B区 (中上): 核心指标卡区域 (KPI Cards Area)**
    *   展示团队最核心的绩效结果，并与中心平均水平进行对比。
*   **C区 (中部): 分析图表区域 (Analysis Charts Area)**
    *   多维度、可视化地展示团队的趋势、排名、成员分布和短板。
*   **D区 (底部): 明细列表区域 (Detailed Lists Area)**
    *   提供团队内成员的详细排名和可供学习的优秀案例。

```
+----------------------------------------------------------------------------------+
| A: [时间范围] [班组选择] [查询] [重置]                                             |
+----------------------------------------------------------------------------------+
| B: [ 团队平均分 ] [ 团队合格率 ] [ 团队申诉率 ]                                  |
|    (vs 平均水平) (vs 平均水平) (vs 平均水平)                                     |
+----------------------------------------------------------------------------------+
| C: [ 团队平均分月度变化图 (折线图) ]         [ 各团队绩效排名 (条形图) ]           |
|                                                                                  |
|    [ 团队成员分数分布 (箱线图) ]             [ 团队高频失分项对比 (分组条形图) ]   |
+----------------------------------------------------------------------------------+
| D: [ 团队内坐席排名 (表格) ]                  [ 高分/亮点通话案例库 (表格) ]      |
+----------------------------------------------------------------------------------+
```

---

#### **4.3 组件详细说明 (Component Details)**

##### **A. 筛选器区域 (Filters Area)**

*   **组件1: 时间范围选择器**
    *   **UI控件:** `DateRangePicker`。
    *   **功能描述:** 支持预设选项 ("上月", "本季度", "近半年") 和自定义时间范围。
    *   **默认值:** "上月"。

*   **组件2: 班组选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **数据源:** 当前管理者权限范围内的所有班组。
    *   **默认值:** "全部班组"。

*   **组件3: 操作按钮区**
    *   **UI控件:** `Button` 组，包含【查询】、【重置】。

##### **B. 核心指标卡区域 (KPI Cards Area)**

1.  **`团队平均分`**
    *   **计算逻辑:** 筛选条件下，所选班组的平均质检分数。
    *   **辅助信息:** 与质检中心全体平均分的对比（如：高于/低于平均分 X.X 分）。
2.  **`团队合格率`**
    *   **计算逻辑:** `(所选班组质检合格数 / 总数) * 100%`。
    *   **辅助信息:** 与质检中心整体合格率的对比。
3.  **`团队申诉率`**
    *   **计算逻辑:** `(所选班组发起的申诉数 / 总质检数) * 100%`。
    *   **辅助信息:** 与质检中心整体申诉率的对比（注意，此指标越低越好）。

##### **C. 分析图表区域 (Analysis Charts Area)**

*   **图表1: `团队平均分月度/季度变化图`**
    *   **图表类型:** 折线图 (Line Chart)。
    *   **X轴:** 时间（按月或季度）。
    *   **Y轴:** 平均分。
    *   **数据线:**
        *   为每个被选中的班组绘制一条实线。
        *   绘制一条虚线代表"质检中心平均分"，作为基准。
    *   **解读:** 直观展示团队成绩的动态进步过程。

*   **图表2: `各团队绩效排名`**
    *   **图表类型:** 条形图 (Bar Chart)。
    *   **Y轴:** 班组名称。
    *   **X轴:** 平均分（或合格率，可提供切换按钮）。
    *   **交互:** 高亮显示当前筛选的班组，点击某个班组的条形可联动筛选下方D区的内容。
    *   **解读:** 清晰了解团队在所有班组中的横向位置。

*   **图表3: `团队成员分数分布图`**
    *   **图表类型:** 箱线图 (Box Plot)。
    *   **X轴:** 班组名称。
    *   **Y轴:** 质检分数。
    *   **图表元素:** 为每个班组展示其成员分数的最大值、上四分位数(Q3)、中位数、下四分位数(Q1)和最小值。
    *   **解读:** 快速识别团队成员的表现一致性，是否存在"拔尖"或"拖后腿"的成员。

*   **图表4: `团队高频失分项`**
    *   **图表类型:** 分组条形图 (Grouped Bar Chart)。
    *   **Y轴:** 质检规则项（如"开场白不规范"）。
    *   **X轴:** 发生次数或频率。
    *   **分组指标:**
        *   `所选团队`: 该团队在此项上的失分次数。
        *   `中心平均`: 全体人员在此项上的平均失分次数。
    *   **解读:** 精准定位本团队相对于整体的、需要优先改进的薄弱环节。

##### **D. 明细列表区域 (Detailed Lists Area)**

*   **列表1: `团队内坐席排名`**
    *   **展现形式:** 表格 (Table)。
    *   **排序:** 默认按"平均分"降序。
    *   **列定义:**
        1.  `排名`
        2.  `坐席姓名`
        3.  `平均分`
        4.  `合格率`
        5.  `质检量`
        6.  `申诉率`
        7.  `操作`: [查看个人报表] (链接至坐席个人成长报表)。

*   **列表2: `高分/亮点通话案例库`**
    *   **展现形式:** 表格 (Table)。
    *   **数据源:** 筛选出总分高于95分，或被复核员打上"优秀案例"标签的通话。
    *   **列定义:**
        1.  `通话ID`
        2.  `坐席姓名`
        3.  `最终得分`
        4.  `质检/复核员`
        5.  `亮点标签`: 如 `服务热情`、`问题解决能力强` 等。
        6.  `质检日期`
        7.  `操作`: [播放录音], [查看详情]。

---

#### **4.4 数据刷新机制 (Data Refresh)**

*   **刷新策略:** T+1。
*   **说明:** 团队绩效数据通常用于周期性复盘，每日更新一次即可满足需求。

---

#### **4.5 附注 (Notes)**

*   **对比逻辑:** "与中心平均水平对比"的指标，应在UI上清晰展示正向或负向差异，例如使用不同颜色的箭头和数值。
*   **下钻分析 (Drill-Down):** 报表内各模块应尽可能联动。例如，在"各团队绩效排名"图表中点击A班组，下方的"团队内坐席排名"和"高分案例库"应自动筛选并仅展示A班组的数据。
*   **权限控制:** 班组长默认只能选择自己所在的班组；区域主管则可以选择其管辖范围内的所有班组。 