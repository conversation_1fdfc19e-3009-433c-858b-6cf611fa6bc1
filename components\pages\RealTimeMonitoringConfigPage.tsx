import React, { useState } from 'react';
import { PageHeader } from '../PageHeader';
import { Plus, Edit2, Trash2, Search, RadioTower } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Tooltip } from '../common/Tooltip';
import { ConfirmationModal } from '../common/ConfirmationModal';
import { AnimatePresence } from 'framer-motion';
import { CreateMonitoringConfigForm, MonitoringConfigFormData } from '../CreateMonitoringConfigForm';

// --- Types and Mock Data ---

interface MonitoringConfig {
    id: string;
    name: string;
    isActive: boolean;
    qaScheme: string;
    scope: {
        type: 'teams' | 'agents' | 'all';
        ids: string[];
        description: string;
    };
    triggerAction: {
        type: 'create_high_priority_alert' | 'create_normal_alert';
        notify: boolean;
        notifiedUsers: { id: string, name: string }[];
    };
    lastModified: string;
    modifier: string;
}

const initialConfigs: MonitoringConfig[] = [
    {
        id: 'RTC-001',
        name: '客户负面情绪实时监控',
        isActive: true,
        qaScheme: '客户体验优化方案',
        scope: { type: 'all', ids: [], description: '全部团队' },
        triggerAction: { type: 'create_high_priority_alert', notify: false, notifiedUsers: [] },
        lastModified: '2024-06-24',
        modifier: '王主管',
    },
    {
        id: 'RTC-002',
        name: '金融销售合规实时监控',
        isActive: true,
        qaScheme: '金融产品销售合规方案',
        scope: { type: 'teams', ids: ['T01', 'T02'], description: '金融产品销售一部、二部' },
        triggerAction: { type: 'create_high_priority_alert', notify: true, notifiedUsers: [{ id: 'U01', name: '李经理' }, { id: 'U02', name: '风控组' }] },
        lastModified: '2024-06-20',
        modifier: '李经理',
    },
    {
        id: 'RTC-003',
        name: '新人服务质量监控',
        isActive: false,
        qaScheme: '新员工考核方案',
        scope: { type: 'agents', ids: [], description: '6月后入职坐席(动态)' },
        triggerAction: { type: 'create_normal_alert', notify: false, notifiedUsers: [] },
        lastModified: '2024-06-15',
        modifier: '系统',
    },
];

// --- Sub-components ---

const ToggleSwitch = ({ checked, onChange }: { checked: boolean; onChange: (checked: boolean) => void }) => {
    return (
        <label className="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" checked={checked} onChange={(e) => onChange(e.target.checked)} className="sr-only peer" />
            <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
    );
};


// --- Main Component ---

export const RealTimeMonitoringConfigPage: React.FC = () => {
    const [configs, setConfigs] = useState(initialConfigs);
    const [searchTerm, setSearchTerm] = useState('');
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingConfig, setEditingConfig] = useState<MonitoringConfigFormData | undefined>(undefined);

    const handleToggle = (id: string, isActive: boolean) => {
        setConfigs(configs.map(c => c.id === id ? { ...c, isActive } : c));
    };
    
    const filteredConfigs = configs.filter(c => c.name.toLowerCase().includes(searchTerm.toLowerCase()));

    const handleCreateNew = () => {
        setEditingConfig(undefined);
        setIsFormOpen(true);
    };

    const handleEdit = (config: MonitoringConfig) => {
        // Map the MonitoringConfig to MonitoringConfigFormData
        const formData: MonitoringConfigFormData = {
            id: config.id,
            name: config.name,
            qaScheme: { value: config.qaScheme, label: config.qaScheme },
            scopeType: config.scope.type === 'all' ? 'all' : 'teams',
            // This is a simplified mapping. A real app would look up labels for the IDs.
            scopeTeams: config.scope.type === 'teams' 
                ? config.scope.description.split('、').map(teamName => ({ value: teamName, label: teamName }))
                : [],
            triggerActionType: { value: config.triggerAction.type, label: config.triggerAction.type === 'create_high_priority_alert' ? '创建高优预警' : '创建普通预警' },
            notify: config.triggerAction.notify,
            notifiedUsers: config.triggerAction.notifiedUsers.map(u => ({ value: u.id, label: u.name })),
        };
        setEditingConfig(formData);
        setIsFormOpen(true);
    };

    const handleFormSubmit = (data: MonitoringConfigFormData) => {
        console.log("Form submitted", data);
        if(editingConfig) {
            // update logic
        } else {
            // create logic
        }
        setIsFormOpen(false);
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="实时监控配置"
                description="配置并管理持续运行的实时质检方案，及时发现会话风险。"
                badge="质检管理"
                actions={
                    <button onClick={handleCreateNew} className="flex items-center bg-blue-600 text-white px-4 py-2 text-sm font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                        <Plus className="w-4 h-4 mr-2" />
                        新建监控方案
                    </button>
                }
            />
            <main className="p-6 md:p-10">
                 <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-800">监控方案列表</h2>
                        <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="搜索方案名称..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    </div>
                    
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                             <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3">状态</th>
                                    <th scope="col" className="px-6 py-3 min-w-[250px]">监控方案名称</th>
                                    <th scope="col" className="px-6 py-3">所用质检方案</th>
                                    <th scope="col" className="px-6 py-3">监控范围</th>
                                    <th scope="col" className="px-6 py-3 min-w-[250px]">触发动作</th>
                                    <th scope="col" className="px-6 py-3">最后修改</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredConfigs.map(config => (
                                    <tr key={config.id} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-6 py-4">
                                            <ToggleSwitch checked={config.isActive} onChange={(checked) => handleToggle(config.id, checked)} />
                                        </td>
                                        <td className="px-6 py-4 font-semibold text-gray-800">{config.name}</td>
                                        <td className="px-6 py-4">{config.qaScheme}</td>
                                        <td className="px-6 py-4">{config.scope.description}</td>
                                        <td className="px-6 py-4">
                                            <div>{config.triggerAction.type === 'create_high_priority_alert' ? '创建高优预警' : '创建普通预警'}</div>
                                            {config.triggerAction.notify && config.triggerAction.notifiedUsers.length > 0 && (
                                                <div className="text-xs text-gray-500 mt-1">
                                                   通知: {config.triggerAction.notifiedUsers.map(u => u.name).join(', ')}
                                                </div>
                                            )}
                                        </td>
                                        <td className="px-6 py-4">
                                            <div>{config.modifier}</div>
                                            <div className="text-xs text-gray-400">{config.lastModified}</div>
                                        </td>
                                        <td className="px-6 py-4 text-right">
                                            <button onClick={() => handleEdit(config)} className="text-gray-500 hover:text-blue-600 p-1.5 hover:bg-blue-50 rounded-lg"><Edit2 className="w-4 h-4" /></button>
                                            <button className="text-gray-500 hover:text-red-600 p-1.5 hover:bg-red-50 rounded-lg ml-2"><Trash2 className="w-4 h-4" /></button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                 </div>
            </main>
            <AnimatePresence>
                {isFormOpen && (
                    <CreateMonitoringConfigForm
                        key={editingConfig?.id || 'new'}
                        onClose={() => setIsFormOpen(false)}
                        onSubmit={handleFormSubmit}
                        initialData={editingConfig}
                    />
                )}
            </AnimatePresence>
        </div>
    );
};

export default RealTimeMonitoringConfigPage; 