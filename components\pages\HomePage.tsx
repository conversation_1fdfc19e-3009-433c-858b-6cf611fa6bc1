import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

// 核心功能模块数据
const coreModules = [
  {
    name: "质检工作台",
    description: "为不同角色提供专属的质检工作界面，支持个性化配置和高效操作流程。",
    icon: "👥",
    bgColor: "bg-blue-500",
    textColor: "text-white",
    borderColor: "border-blue-200",
    features: ["我的复核任务", "申诉处理", "质检成绩管理", "通知中心"],
    link: "/final-design/my-review-tasks"
  },
  {
    name: "质检管理",
    description: "全面的质检流程管理，包括规则配置、任务分配、复核申诉等核心功能。",
    icon: "⚙️",
    bgColor: "bg-green-500",
    textColor: "text-white",
    borderColor: "border-green-200",
    features: ["质检规则管理", "质检方案管理", "质检计划管理", "质检任务管理"],
    link: "/final-design/rule-library"
  },
  {
    name: "统计分析",
    description: "深度数据分析和可视化报表，提供多维度的质检洞察和业务指标。",
    icon: "📊",
    bgColor: "bg-purple-500",
    textColor: "text-white",
    borderColor: "border-purple-200",
    features: ["质检运营总览", "服务质量深度分析", "复核工作分析报告", "坐席申诉洞察报告"],
    link: "/final-design/quality-operation-overview"
  },
  {
    name: "系统管理",
    description: "系统配置和管理功能，包括用户权限、数据源配置、AI模型管理等。",
    icon: "🛡️",
    bgColor: "bg-orange-500",
    textColor: "text-white",
    borderColor: "border-orange-200",
    features: ["数据源管理", "大模型管理", "语音识别引擎管理", "通知渠道管理"],
    link: "/final-design/data-source-management"
  }
];

// 用户角色视角
const userRoles = [
  {
    icon: '👤',
    title: '坐席员视角',
    description: '查看个人质检成绩、排名趋势，了解失分点并发起申诉，持续提升服务质量。',
    link: '/final-design/agent-homepage'
  },
  {
    icon: '👥',
    title: '班组长视角', 
    description: '监控团队整体表现，识别共性问题，为团队成员提供针对性辅导和培训。',
    link: '/final-design/team-leader-homepage'
  },
  {
    icon: '🔍',
    title: '复核员视角',
    description: '高效处理复核任务，校准AI质检结果，确保质检标准的准确性和一致性。',
    link: '/final-design/reviewer-homepage'
  },
  {
    icon: '👨‍💼',
    title: '质检主管视角',
    description: '全局掌控质检体系运营，设计质检标准，分析数据洞察，驱动持续改进。',
    link: '/final-design/supervisor-homepage'
  }
];

// 系统核心价值数据
const coreValues = [
  {
    title: "AI驱动质检",
    description: "融合自然语言处理、情感分析、语音识别等AI技术，实现智能化质检分析，提升质检效率和准确性。",
    icon: "🤖",
    link: "/final-design/quality-operation-overview"
  },
  {
    title: "人机协同工作",
    description: "AI自动质检与人工复核相结合，既保证质检覆盖率，又确保质检结果的准确性和公正性。",
    icon: "🤝",
    link: "/final-design/my-review-tasks"
  },
  {
    title: "数据驱动优化",
    description: "基于质检数据的深度分析和可视化展示，为业务决策提供数据支撑，持续优化服务质量。",
    icon: "📊",
    link: "/final-design/quality-operation-overview"
  },
  {
    title: "实时预警机制",
    description: "建立多层级预警体系，及时发现质量问题和异常情况，确保服务质量的实时监控和快速响应。",
    icon: "⚠️",
    link: "/final-design/real-time-alert-center"
  }
];

/**
 * 智能客服质检系统首页
 * 展示系统核心价值、功能模块和用户角色视角
 */
const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-white text-gray-800">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
        {/* 动态背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute top-20 right-10 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-6 py-24 sm:py-32 relative z-10">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center"
          >
            <motion.h1 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="text-4xl md:text-6xl font-extrabold tracking-tight mb-6"
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 animate-gradient-x">
                智能客服质检系统
              </span>
            </motion.h1>
            
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-6 text-lg md:text-xl max-w-3xl mx-auto text-gray-600 leading-relaxed"
            >
              <span className="font-semibold text-gray-700">AI驱动</span>的全流程质检解决方案，实现从数据接入到分析洞察的完整闭环，
              <br className="hidden sm:block" />
              为客服中心提供<span className="font-semibold text-gray-700">智能化、数据化</span>的服务质量管理平台。
            </motion.p>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mt-12 flex flex-col sm:flex-row justify-center items-stretch gap-6 px-4"
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/quality-operation-overview"
                  className="group relative rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-base font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden flex items-center justify-center min-w-[200px]"
                >
                  <span className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  <span className="relative flex items-center gap-2">
                    查看运营总览
                    <motion.span
                      className="inline-block"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      →
                    </motion.span>
                  </span>
                </Link>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/supervisor-homepage"
                  className="group relative rounded-xl border-2 border-gray-300 bg-white/80 backdrop-blur-sm px-8 py-4 text-base font-semibold text-gray-700 shadow-lg hover:shadow-xl hover:border-blue-400 transition-all duration-300 flex items-center justify-center min-w-[200px]"
                >
                  <span className="relative flex items-center gap-2">
                    体验系统功能
                    <motion.span
                      className="inline-block"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
                    >
                      →
                    </motion.span>
                  </span>
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Core Modules Section */}
      <div className="py-20 sm:py-28 relative">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-b from-white via-gray-50/50 to-white"></div>
        
        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800">
                核心功能模块
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              涵盖质检全流程的四大核心模块，从工作台操作到系统管理，提供完整的质检解决方案。
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {coreModules.map((module, index) => (
              <motion.div
                key={module.name}
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{ 
                  y: -8, 
                  scale: 1.02,
                  transition: { duration: 0.3 }
                }}
                className={`group relative overflow-hidden rounded-2xl border-2 ${module.borderColor} p-8 transition-all duration-500 hover:shadow-2xl bg-white/80 backdrop-blur-sm`}
              >
                {/* 动态背景效果 */}
                <div className={`absolute -top-8 -right-8 w-32 h-32 ${module.bgColor} rounded-full opacity-20 transition-all duration-700 group-hover:scale-[12] group-hover:opacity-30`}></div>
                <div className={`absolute -bottom-4 -left-4 w-20 h-20 ${module.bgColor} rounded-full opacity-10 transition-all duration-700 group-hover:scale-[6] group-hover:opacity-20`}></div>
                
                {/* 光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <div className="relative z-10">
                  <motion.div 
                    className={`w-16 h-16 ${module.bgColor} ${module.textColor} rounded-2xl flex items-center justify-center mb-6 shadow-lg`}
                    whileHover={{ 
                      rotate: [0, -10, 10, 0],
                      scale: 1.1
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    <span className="text-3xl">{module.icon}</span>
                  </motion.div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors duration-300">
                    {module.name}
                  </h3>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {module.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-6">
                    {module.features.map((feature, featureIndex) => (
                      <motion.span 
                        key={feature} 
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ delay: featureIndex * 0.1 }}
                        className="text-xs font-medium bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-3 py-2 rounded-full hover:from-blue-100 hover:to-purple-100 hover:text-blue-700 transition-all duration-300 cursor-default"
                      >
                        {feature}
                      </motion.span>
                    ))}
                  </div>
                  
                  <Link 
                    to={module.link} 
                    className="group/link inline-flex items-center gap-2 font-semibold text-sm text-blue-600 hover:text-purple-600 transition-colors duration-300"
                  >
                    查看详情
                    <motion.span
                      className="inline-block"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      →
                    </motion.span>
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* User Roles Section */}
      <div className="py-20 sm:py-28 relative overflow-hidden">
        {/* 动态背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" style={{animationDelay: '4s'}}></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                多角色协同工作
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              系统为不同角色提供专属功能和视角，实现从坐席到主管的全员参与质检流程。
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {userRoles.map((role, index) => (
              <motion.div
                key={role.title}
                initial={{ opacity: 0, y: 50, rotateY: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ 
                  duration: 0.7, 
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{ 
                  y: -12, 
                  scale: 1.05,
                  rotateY: 5,
                  transition: { duration: 0.3 }
                }}
                className="group bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-white/20 relative overflow-hidden"
                style={{
                  transformStyle: 'preserve-3d',
                  perspective: '1000px'
                }}
              >
                {/* 卡片光效 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-2 -right-2 w-24 h-24 bg-blue-200 rounded-full opacity-10 transition-all duration-500 group-hover:scale-150 group-hover:opacity-20"></div>
                
                <Link to={role.link} className="block relative z-10">
                  <div className="text-center">
                    <motion.div 
                      className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg"
                      whileHover={{ 
                        rotate: [0, -15, 15, 0],
                        scale: 1.15
                      }}
                      transition={{ duration: 0.6 }}
                    >
                      <span className="text-2xl">{role.icon}</span>
                    </motion.div>
                    
                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors duration-300">
                      {role.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm leading-relaxed min-h-[72px] mb-4">
                      {role.description}
                    </p>
                    
                    <motion.p 
                      className="font-semibold text-sm text-blue-600 group-hover:text-purple-600 transition-colors duration-300"
                      whileHover={{ scale: 1.05 }}
                    >
                      进入工作台
                      <motion.span
                        className="inline-block ml-1"
                        animate={{ x: [0, 4, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                      >
                        →
                      </motion.span>
                    </motion.p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Core Values Section */}
      <div className="py-20 sm:py-28 relative overflow-hidden">
        {/* 科技感背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900"></div>
        <div className="absolute inset-0">
          {/* 动态网格背景 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[size:50px_50px] animate-pulse"></div>
          {/* 光点效果 */}
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
          <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-ping" style={{animationDelay: '2s'}}></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                系统核心价值
              </span>
            </h2>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto">
              基于AI技术和数据驱动理念，构建智能化质检体系，实现质检流程的全面升级。
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 60, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ 
                  duration: 0.8, 
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 60
                }}
                whileHover={{ 
                  y: -10, 
                  scale: 1.03,
                  transition: { duration: 0.4 }
                }}
                className="group relative overflow-hidden rounded-3xl border border-white/20 p-8 transition-all duration-500 hover:shadow-2xl bg-white/10 backdrop-blur-lg"
              >
                {/* 动态背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-500 rounded-full opacity-10 transition-all duration-700 group-hover:scale-150 group-hover:opacity-20 blur-xl"></div>
                <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-purple-500 rounded-full opacity-5 transition-all duration-700 group-hover:scale-125 group-hover:opacity-15 blur-lg"></div>
                
                {/* 边框光效 */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>
                
                <div className="relative z-10">
                  <div className="flex items-center mb-6">
                    <motion.div 
                      className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-2xl flex items-center justify-center mr-4 shadow-2xl"
                      whileHover={{ 
                        rotate: [0, -20, 20, 0],
                        scale: 1.2
                      }}
                      transition={{ duration: 0.8 }}
                    >
                      <span className="text-3xl">{value.icon}</span>
                    </motion.div>
                    <h3 className="text-2xl font-bold text-white group-hover:text-blue-300 transition-colors duration-300">
                      {value.title}
                    </h3>
                  </div>
                  
                  <p className="text-gray-300 mb-6 leading-relaxed text-lg">
                    {value.description}
                  </p>
                  
                  <Link 
                    to={value.link} 
                    className="group/link inline-flex items-center gap-3 font-semibold text-lg text-blue-400 hover:text-purple-400 transition-colors duration-300"
                  >
                    了解更多
                    <motion.span
                      className="inline-block"
                      animate={{ x: [0, 6, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      →
                    </motion.span>
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

    </div>
  );
};

export default HomePage;