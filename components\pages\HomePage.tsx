import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Users,
  Settings,
  BarChart3,
  Shield,
  CheckCircle,
  TrendingUp,
  Zap,
  Brain,
  ArrowRight,
  Sparkles,
  Target,
  Award
} from 'lucide-react';

// 核心功能模块数据
const coreModules = [
  {
    name: "质检工作台",
    description: "为不同角色提供专属的质检工作界面，支持个性化配置和高效操作流程。",
    icon: Users,
    bgColor: "bg-gradient-to-br from-blue-500 to-blue-600",
    hoverColor: "hover:from-blue-600 hover:to-blue-700",
    textColor: "text-white",
    borderColor: "border-blue-200",
    shadowColor: "shadow-blue-500/25",
    features: ["我的复核任务", "申诉处理", "质检成绩管理", "通知中心"],
    link: "/final-design/my-review-tasks",
    stats: "1,234+ 任务处理"
  },
  {
    name: "质检管理",
    description: "全面的质检流程管理，包括规则配置、任务分配、复核申诉等核心功能。",
    icon: Settings,
    bgColor: "bg-gradient-to-br from-emerald-500 to-emerald-600",
    hoverColor: "hover:from-emerald-600 hover:to-emerald-700",
    textColor: "text-white",
    borderColor: "border-emerald-200",
    shadowColor: "shadow-emerald-500/25",
    features: ["质检规则管理", "质检方案管理", "质检计划管理", "质检任务管理"],
    link: "/final-design/rule-library",
    stats: "500+ 规则配置"
  },
  {
    name: "统计分析",
    description: "深度数据分析和可视化报表，提供多维度的质检洞察和业务指标。",
    icon: BarChart3,
    bgColor: "bg-gradient-to-br from-purple-500 to-purple-600",
    hoverColor: "hover:from-purple-600 hover:to-purple-700",
    textColor: "text-white",
    borderColor: "border-purple-200",
    shadowColor: "shadow-purple-500/25",
    features: ["质检运营总览", "服务质量深度分析", "复核工作分析报告", "坐席申诉洞察报告"],
    link: "/final-design/quality-operation-overview",
    stats: "98.5% 准确率"
  },
  {
    name: "系统管理",
    description: "系统配置和管理功能，包括用户权限、数据源配置、AI模型管理等。",
    icon: Shield,
    bgColor: "bg-gradient-to-br from-orange-500 to-orange-600",
    hoverColor: "hover:from-orange-600 hover:to-orange-700",
    textColor: "text-white",
    borderColor: "border-orange-200",
    shadowColor: "shadow-orange-500/25",
    features: ["数据源管理", "大模型管理", "语音识别引擎管理", "通知渠道管理"],
    link: "/final-design/data-source-management",
    stats: "24/7 系统监控"
  }
];

// 用户角色视角
const userRoles = [
  {
    icon: Users,
    title: '坐席员视角',
    description: '查看个人质检成绩、排名趋势，了解失分点并发起申诉，持续提升服务质量。',
    link: '/final-design/agent-homepage',
    bgGradient: 'from-blue-50 to-indigo-50',
    iconColor: 'text-blue-600',
    borderColor: 'border-blue-200',
    hoverBorder: 'hover:border-blue-300'
  },
  {
    icon: Target,
    title: '班组长视角',
    description: '监控团队整体表现，识别共性问题，为团队成员提供针对性辅导和培训。',
    link: '/final-design/team-leader-homepage',
    bgGradient: 'from-emerald-50 to-green-50',
    iconColor: 'text-emerald-600',
    borderColor: 'border-emerald-200',
    hoverBorder: 'hover:border-emerald-300'
  },
  {
    icon: CheckCircle,
    title: '复核员视角',
    description: '高效处理复核任务，校准AI质检结果，确保质检标准的准确性和一致性。',
    link: '/final-design/reviewer-homepage',
    bgGradient: 'from-purple-50 to-violet-50',
    iconColor: 'text-purple-600',
    borderColor: 'border-purple-200',
    hoverBorder: 'hover:border-purple-300'
  },
  {
    icon: Award,
    title: '质检主管视角',
    description: '全局掌控质检体系运营，设计质检标准，分析数据洞察，驱动持续改进。',
    link: '/final-design/supervisor-homepage',
    bgGradient: 'from-orange-50 to-amber-50',
    iconColor: 'text-orange-600',
    borderColor: 'border-orange-200',
    hoverBorder: 'hover:border-orange-300'
  }
];

// 系统核心价值数据
const coreValues = [
  {
    title: "AI驱动质检",
    description: "融合自然语言处理、情感分析、语音识别等AI技术，实现智能化质检分析，提升质检效率和准确性。",
    icon: Brain,
    link: "/final-design/quality-operation-overview",
    gradient: "from-cyan-500 to-blue-600",
    stats: "95%+ 识别准确率"
  },
  {
    title: "人机协同工作",
    description: "AI自动质检与人工复核相结合，既保证质检覆盖率，又确保质检结果的准确性和公正性。",
    icon: Sparkles,
    link: "/final-design/my-review-tasks",
    gradient: "from-violet-500 to-purple-600",
    stats: "50% 效率提升"
  },
  {
    title: "数据驱动优化",
    description: "基于质检数据的深度分析和可视化展示，为业务决策提供数据支撑，持续优化服务质量。",
    icon: TrendingUp,
    link: "/final-design/quality-operation-overview",
    gradient: "from-emerald-500 to-teal-600",
    stats: "实时数据洞察"
  },
  {
    title: "实时预警机制",
    description: "建立多层级预警体系，及时发现质量问题和异常情况，确保服务质量的实时监控和快速响应。",
    icon: Zap,
    link: "/final-design/real-time-alert-center",
    gradient: "from-orange-500 to-red-600",
    stats: "秒级响应预警"
  }
];

/**
 * 智能客服质检系统首页
 * 展示系统核心价值、功能模块和用户角色视角
 */
const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
        {/* 增强的动态背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-96 h-96 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse"></div>
          <div className="absolute top-20 right-10 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse" style={{animationDelay: '4s'}}></div>

          {/* 添加网格背景 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:60px_60px]"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 py-24 sm:py-32 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center"
          >
            {/* 添加标签 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-blue-200/50 shadow-lg mb-8"
            >
              <Sparkles className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">AI驱动的智能质检平台</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="text-5xl md:text-7xl font-extrabold tracking-tight mb-8"
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                智能客服质检系统
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-8 text-xl md:text-2xl max-w-4xl mx-auto text-gray-600 leading-relaxed font-light"
            >
              基于<span className="font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md">人工智能</span>的全流程质检解决方案
              <br className="hidden sm:block" />
              为客服中心提供<span className="font-semibold text-purple-700 bg-purple-50 px-2 py-1 rounded-md">智能化、数据化</span>的服务质量管理平台
            </motion.p>
            
            {/* 统计数据展示 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                { label: "质检准确率", value: "98.5%", icon: CheckCircle },
                { label: "处理效率提升", value: "50%+", icon: TrendingUp },
                { label: "客户满意度", value: "96.8%", icon: Award }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full mb-3">
                    <stat.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</div>
                  <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="mt-16 flex flex-col sm:flex-row justify-center items-center gap-6"
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/quality-operation-overview"
                  className="group relative inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden"
                >
                  <span className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  <BarChart3 className="w-5 h-5 relative z-10" />
                  <span className="relative z-10">查看运营总览</span>
                  <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/supervisor-homepage"
                  className="group inline-flex items-center gap-3 px-8 py-4 bg-white/90 backdrop-blur-sm text-gray-700 font-semibold rounded-2xl border-2 border-gray-200 shadow-lg hover:shadow-xl hover:border-blue-300 hover:text-blue-700 transition-all duration-300"
                >
                  <Users className="w-5 h-5 group-hover:text-blue-600 transition-colors duration-300" />
                  <span>体验系统功能</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 group-hover:text-blue-600 transition-all duration-300" />
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Core Modules Section */}
      <div className="py-24 sm:py-32 relative">
        {/* 增强的背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-b from-white via-slate-50/80 to-white"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-1/4 w-64 h-64 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
          <div className="absolute bottom-20 right-1/4 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50 mb-6"
            >
              <Settings className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">核心功能模块</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800">
                全流程质检解决方案
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              涵盖质检全流程的四大核心模块，从工作台操作到系统管理，
              <br className="hidden sm:block" />
              为您提供完整、智能、高效的质检管理体验
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {coreModules.map((module, index) => (
              <motion.div
                key={module.name}
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -12,
                  scale: 1.03,
                  transition: { duration: 0.3 }
                }}
                className={`group relative overflow-hidden rounded-3xl border-2 ${module.borderColor} p-8 transition-all duration-500 hover:shadow-2xl ${module.shadowColor} bg-white/90 backdrop-blur-sm`}
              >
                {/* 增强的背景效果 */}
                <div className={`absolute -top-12 -right-12 w-40 h-40 ${module.bgColor} rounded-full opacity-10 transition-all duration-700 group-hover:scale-[8] group-hover:opacity-20 blur-2xl`}></div>
                <div className={`absolute -bottom-6 -left-6 w-24 h-24 ${module.bgColor} rounded-full opacity-5 transition-all duration-700 group-hover:scale-[4] group-hover:opacity-15 blur-xl`}></div>

                {/* 光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                <div className="relative z-10">
                  <div className="flex items-start justify-between mb-6">
                    <motion.div
                      className={`w-16 h-16 ${module.bgColor} ${module.hoverColor} ${module.textColor} rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300`}
                      whileHover={{
                        rotate: [0, -15, 15, 0],
                        scale: 1.15
                      }}
                      transition={{ duration: 0.6 }}
                    >
                      <module.icon className="w-8 h-8" />
                    </motion.div>

                    <div className="text-right">
                      <div className="text-xs font-medium text-gray-500 mb-1">统计数据</div>
                      <div className="text-sm font-bold text-gray-700">{module.stats}</div>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                    {module.name}
                  </h3>

                  <p className="text-gray-600 mb-6 leading-relaxed text-base">
                    {module.description}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-8">
                    {module.features.map((feature, featureIndex) => (
                      <motion.span
                        key={feature}
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ delay: featureIndex * 0.1 }}
                        className="text-xs font-medium bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 px-3 py-2 rounded-full hover:from-blue-100 hover:to-purple-100 hover:text-blue-700 transition-all duration-300 cursor-default border border-slate-200/50"
                      >
                        {feature}
                      </motion.span>
                    ))}
                  </div>

                  <Link
                    to={module.link}
                    className="group/link inline-flex items-center gap-3 font-semibold text-base text-blue-600 hover:text-purple-600 transition-colors duration-300 bg-blue-50 hover:bg-purple-50 px-4 py-2 rounded-xl"
                  >
                    <span>查看详情</span>
                    <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* User Roles Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        {/* 增强的动态背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-80 h-80 bg-gradient-to-r from-blue-200 to-cyan-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute -bottom-8 left-20 w-64 h-64 bg-gradient-to-r from-indigo-200 to-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse" style={{animationDelay: '4s'}}></div>

          {/* 添加装饰性网格 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.03)_1px,transparent_1px)] bg-[size:40px_40px]"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-50 to-pink-50 rounded-full border border-purple-200/50 mb-6"
            >
              <Users className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-700">多角色协同</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                全员参与质检流程
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              系统为不同角色提供专属功能和视角，
              <br className="hidden sm:block" />
              实现从坐席到主管的全员参与质检流程，提升整体服务质量
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {userRoles.map((role, index) => (
              <motion.div
                key={role.title}
                initial={{ opacity: 0, y: 50, rotateY: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.7,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{
                  y: -15,
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
                className={`group bg-gradient-to-br ${role.bgGradient} backdrop-blur-sm rounded-3xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border-2 ${role.borderColor} ${role.hoverBorder} relative overflow-hidden`}
              >
                {/* 增强的卡片光效 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                <div className={`absolute -top-4 -right-4 w-32 h-32 bg-gradient-to-br ${role.bgGradient} rounded-full opacity-20 transition-all duration-500 group-hover:scale-125 group-hover:opacity-30 blur-2xl`}></div>

                <Link to={role.link} className="block relative z-10">
                  <div className="text-center">
                    <motion.div
                      className={`w-16 h-16 bg-white ${role.iconColor} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg border-2 ${role.borderColor}`}
                      whileHover={{
                        rotate: [0, -15, 15, 0],
                        scale: 1.15
                      }}
                      transition={{ duration: 0.6 }}
                    >
                      <role.icon className="w-8 h-8" />
                    </motion.div>

                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors duration-300">
                      {role.title}
                    </h3>

                    <p className="text-gray-600 text-sm leading-relaxed min-h-[72px] mb-6">
                      {role.description}
                    </p>

                    <motion.div
                      className={`inline-flex items-center gap-2 font-semibold text-sm ${role.iconColor} bg-white/80 px-4 py-2 rounded-xl border ${role.borderColor} group-hover:bg-white transition-all duration-300`}
                      whileHover={{ scale: 1.05 }}
                    >
                      <span>进入工作台</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </motion.div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Core Values Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        {/* 增强的科技感背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900"></div>
        <div className="absolute inset-0">
          {/* 动态网格背景 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:60px_60px] animate-pulse"></div>

          {/* 增强的光点效果 */}
          <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-blue-400 rounded-full animate-ping opacity-60"></div>
          <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-purple-400 rounded-full animate-ping opacity-60" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-3/4 w-2.5 h-2.5 bg-cyan-400 rounded-full animate-ping opacity-60" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-ping opacity-60" style={{animationDelay: '3s'}}></div>

          {/* 添加光晕效果 */}
          <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6"
            >
              <Sparkles className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium text-blue-300">系统核心价值</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400">
                AI驱动的智能质检体系
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              基于人工智能技术和数据驱动理念，构建智能化质检体系，
              <br className="hidden sm:block" />
              实现质检流程的全面升级和服务质量的持续提升
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 60, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 60
                }}
                whileHover={{
                  y: -15,
                  scale: 1.03,
                  transition: { duration: 0.4 }
                }}
                className="group relative overflow-hidden rounded-3xl border border-white/20 p-8 transition-all duration-500 hover:shadow-2xl bg-white/10 backdrop-blur-lg"
              >
                {/* 增强的动态背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                <div className={`absolute -top-12 -right-12 w-48 h-48 bg-gradient-to-r ${value.gradient} rounded-full opacity-10 transition-all duration-700 group-hover:scale-125 group-hover:opacity-20 blur-3xl`}></div>
                <div className={`absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-r ${value.gradient} rounded-full opacity-5 transition-all duration-700 group-hover:scale-150 group-hover:opacity-15 blur-2xl`}></div>

                {/* 边框光效 */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${value.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-sm`}></div>

                <div className="relative z-10">
                  <div className="flex items-start justify-between mb-6">
                    <motion.div
                      className={`w-16 h-16 bg-gradient-to-br ${value.gradient} text-white rounded-2xl flex items-center justify-center shadow-2xl`}
                      whileHover={{
                        rotate: [0, -20, 20, 0],
                        scale: 1.2
                      }}
                      transition={{ duration: 0.8 }}
                    >
                      <value.icon className="w-8 h-8" />
                    </motion.div>

                    <div className="text-right">
                      <div className="text-xs font-medium text-gray-400 mb-1">性能指标</div>
                      <div className="text-sm font-bold text-gray-200">{value.stats}</div>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-white group-hover:text-blue-300 transition-colors duration-300 mb-4">
                    {value.title}
                  </h3>

                  <p className="text-gray-300 mb-8 leading-relaxed text-base">
                    {value.description}
                  </p>

                  <Link
                    to={value.link}
                    className="group/link inline-flex items-center gap-3 font-semibold text-base text-blue-400 hover:text-purple-400 transition-colors duration-300 bg-white/10 hover:bg-white/20 px-4 py-2 rounded-xl backdrop-blur-sm"
                  >
                    <span>了解更多</span>
                    <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action Section */}
      <div className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-72 h-72 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-64 h-64 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              准备好体验智能质检的强大功能了吗？
            </h2>
            <p className="text-xl text-gray-600 mb-12 leading-relaxed">
              立即开始使用我们的AI驱动质检系统，提升您的客服质量管理效率
            </p>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-6">
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/quality-operation-overview"
                  className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300"
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>立即开始</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/supervisor-homepage"
                  className="group inline-flex items-center gap-3 px-8 py-4 bg-white text-gray-700 font-semibold rounded-2xl border-2 border-gray-200 shadow-lg hover:shadow-xl hover:border-blue-300 hover:text-blue-700 transition-all duration-300"
                >
                  <Users className="w-5 h-5 group-hover:text-blue-600 transition-colors duration-300" />
                  <span>了解更多</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 group-hover:text-blue-600 transition-all duration-300" />
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

    </div>
  );
};

export default HomePage;