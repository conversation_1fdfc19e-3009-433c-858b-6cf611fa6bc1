import React, { useState } from 'react';
import AbnormalAnswerConcepts from './abnormalAnswer/AbnormalAnswerConcepts';
import AbnormalAnswerConfig from './abnormalAnswer/AbnormalAnswerConfig';
import AbnormalAnswerTester from './abnormalAnswer/AbnormalAnswerTester';
import AbnormalAnswerCases from './abnormalAnswer/AbnormalAnswerCases';
import AbnormalAnswerTips from './abnormalAnswer/AbnormalAnswerTips';
import { PageHeader } from '../../PageHeader';

/**
 * 解析时间戳字符串 [mm:ss]
 * @param line 对话行
 * @returns {number | null} 秒数或null
 */
const parseTimestamp = (line: string): number | null => {
  const match = line.match(/^\[(\d{2}):(\d{2})\]/);
  if (match) {
    const minutes = parseInt(match[1], 10);
    const seconds = parseInt(match[2], 10);
    return minutes * 60 + seconds;
  }
  return null;
};

/**
 * 非正常接听检查页面
 * @constructor
 */
const AbnormalAnswerPage: React.FC = () => {
  const [config, setConfig] = useState({
    operator: '>' as '>' | '<' | '>=' | '<=' | '=',
    duration: 5,
    role: 'agent' as 'any' | 'customer' | 'agent',
  });

  const [testText, setTestText] = useState(
    '[00:06] 客服：您好，久等了。\n[00:08] 客户：你好。'
  );

  const [testResult, setTestResult] = useState<{
    matched: boolean;
    firstSentenceTime: number | null;
    firstSentenceRole: string;
    details: string;
  } | null>(null);

  const runTest = () => {
    const lines = testText.split('\n').filter(s => s.trim());
    if (lines.length === 0) {
      setTestResult({
        matched: false,
        firstSentenceTime: null,
        firstSentenceRole: '',
        details: '未检测到任何对话内容。'
      });
      return;
    }

    let targetLine: string | undefined;
    let targetRole: string = '';

    if (config.role === 'any') {
      targetLine = lines[0];
      if (targetLine.includes('客服：')) targetRole = '客服';
      else if (targetLine.includes('客户：')) targetRole = '客户';
    } else if (config.role === 'agent') {
      targetLine = lines.find(line => line.includes('客服：'));
      targetRole = '客服';
    } else if (config.role === 'customer') {
      targetLine = lines.find(line => line.includes('客户：'));
      targetRole = '客户';
    }

    if (!targetLine) {
       setTestResult({
        matched: false,
        firstSentenceTime: null,
        firstSentenceRole: '',
        details: `规则未命中。未在对话中找到指定的角色 [${config.role === 'agent' ? '客服' : '客户'}]。`
      });
      return;
    }

    const firstSentenceTime = parseTimestamp(targetLine);
    
    if (firstSentenceTime === null) {
      setTestResult({
        matched: false,
        firstSentenceTime: null,
        firstSentenceRole: targetRole,
        details: `错误：在指定角色的第一句话中未找到有效的时间戳 [mm:ss]。句子: "${targetLine}"`
      });
      return;
    }

    let timeMatched = false;
    switch (config.operator) {
      case '>':
        timeMatched = firstSentenceTime > config.duration;
        break;
      case '<':
        timeMatched = firstSentenceTime < config.duration;
        break;
      case '>=':
        timeMatched = firstSentenceTime >= config.duration;
        break;
      case '<=':
        timeMatched = firstSentenceTime <= config.duration;
        break;
      case '=':
        timeMatched = firstSentenceTime === config.duration;
        break;
    }

    setTestResult({
      matched: timeMatched,
      firstSentenceTime,
      firstSentenceRole: targetRole,
      details: `规则${timeMatched ? '命中' : '未命中'}。检测到角色 [${targetRole}] 的首句响应时间为 ${firstSentenceTime} 秒，${timeMatched ? '满足' : '不满足'}条件 ( ${config.operator} ${config.duration} 秒 )。`
    });
  };

  const loadCase = (caseConfig: any, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="🎙️ 非正常接听"
        description="检测接通电话至第一句话之间的时长，并可设置第一句话的检测角色。"
        badge="语音检查"
        tag="基础入门"
      />

      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          <div className="w-[60%] space-y-6">
            <AbnormalAnswerConcepts />
            <AbnormalAnswerConfig config={config} setConfig={setConfig} />
            <AbnormalAnswerTester
              config={config}
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              setTestResult={setTestResult}
              runTest={runTest}
            />
          </div>

          <div className="w-[40%] space-y-6">
            <AbnormalAnswerCases loadCase={loadCase} />
            <AbnormalAnswerTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2 text-sm">
                <p>• <strong>从案例开始</strong>：加载预设的"客服响应过慢"和"正常快速响应"案例，直观感受不同条件下的检测结果。</p>
                <p>• <strong>理解时间戳格式</strong>：确保您的测试文本中包含 `[mm:ss]` 格式的时间戳，这是计算响应时长的基础。</p>
                <p>• <strong>掌握角色配置</strong>：重点关注"第一句话的角色"配置。如果需要严格考核客服首响，请务必将其设置为"客服"。</p>
                <p>• <strong>结合业务场景</strong>：根据呼叫中心的服务标准（SLA）来设定合理的检测时长，例如，金牌服务的响应时间要求可能比普通服务更短。</p>
                <p>• <strong>思考组合规则</strong>：可以将此算子与"关键词"算子组合，创建一个更复杂的规则，如"客服必须在3秒内说出您好"。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AbnormalAnswerPage; 