
---

### 页面二十四：多模式会话详情页 (FinalMultiModeSessionDetailPage.tsx) (完整版)

#### 1. 核心定位与目标
该页面是**单次通话会话**的全景视图和操作平台。其核心目标是为不同角色的用户提供一个**沉浸式**的界面，用于**查看、复核或处理申诉**某一次具体的通话录音。它是连接宏观数据与微观场景的枢纽，是所有质检判断和操作发生的最终场所。页面的“多模式”特性体现在它会根据URL中的 `viewMode` 参数，动态地调整布局和可用的操作，以适应不同角色的工作需求。

#### 2. 主要功能模块与内容 (三栏式布局)

该页面采用经典的三栏布局：**左侧为通话信息与操作区，中间为通话文本与音频播放区，右侧为评分与分析区**。

**a. 左侧栏：上下文信息与操作面板**

*   **任务信息 (`TaskInfoPanel`)**:
    *   显示本次质检所属的任务信息，如任务名称、质检方案、合格线等，为质检提供了“背景”和“标准”。
*   **通话信息 (`CallInfoPanel`)**:
    *   展示该次通话的基础信息，包括：被检坐席、所属班组、客户号码、通话开始时间、通话时长。
*   **分数演进面板 (`ScorePanel`)**:
    *   **核心模块**，可视化地展示了分数的生命周期：`AI初检` → `人工复核` → `申诉裁定`。
    *   用不同颜色和激活状态的节点清晰地标明了分数流转的路径和最终采纳的来源。
    *   同时显示最终得分和“合格/不合格”的结论。
*   **复核触发原因 (`ReviewTriggerPanel`)** (仅在复核模式下可见):
    *   如果该任务是自动进入复核流程的，这里会明确列出触发原因，如“低分触发”、“关键词触发”，为复核员提供工作重点。
*   **处理时间轴 (`ProcessTimeline`)** (在只读模式下可见):
    *   以时间线的形式，展示从AI初检、人工复核到申诉处理的每一个关键节点和操作人，提供了完整的审计追溯路径。
*   **操作面板 (`renderActionPanel` - 动态渲染)**:
    *   **这是“多模式”的核心体现**，根据`viewMode`参数，这里会渲染出不同的操作组件：
        *   **复核模式 (`viewMode=review`)**: 显示 `ReviewPanel`，复核员可以在此输入复核意见、调整分数（通过修改规则命中状态和扣分），并提交复核结果。
        *   **申诉处理模式 (`viewMode=appeal_processing`)**: 显示 `AppealPanel`，申诉处理人在此查看坐席申诉理由，然后做出“同意申诉”（并输入新分数）或“驳回申诉”的决定，并填写处理意见。
        *   **坐席申诉模式 (`viewMode=performance_check` 且申诉资格为“可申诉”)**: 显示 `InitiateAppealPanel`，坐席可以在此填写申诉理由并发起申诉。
        *   **只读查看模式 (`viewMode=basic_view` 或其他)**: 显示 `BasicViewPanel`，提示当前为只读模式，无可用操作。

**b. 中间栏：音频与文本交互区**

*   **音频播放器 (`AudioPlayer`)**:
    *   提供标准的音频播放控件：播放/暂停、快进/快退、进度条拖拽、时间显示。
    *   **联动功能**: 播放器的当前时间与右侧的通话文本高亮显示实时联动。
*   **通话文本 (`TranscriptPanel`)**:
    *   **核心内容**: 将整个通话过程按角色（坐席、客户）和时间顺序，以对话形式展示出来。
    *   **高亮联动**: 正在播放的语句会**高亮显示**，并且会自动滚动到视图中央，实现“音文同步”。
    *   **事件标注**: AI分析出的关键事件会直接标注在对应的文本下方，例如：
        *   `负面情绪` (`Frown`图标)
        *   `关键词命中` (`Target`图标)
        *   `长时静默` (`MicOff`图标)
    *   **交互**: 点击任意一句话，音频播放器会**自动跳转**到对应的时间点开始播放，极大提升了定位和复听的效率。

**c. 右侧栏：评分详情与分析区**

*   **评分详情 (`ScoringDetailsPanel`)**:
    *   **功能**: 以列表形式，逐条展示该次质检所使用的**质检方案**中的**所有规则**及其打分结果。
    *   **单条规则显示**:
        *   规则名称和描述。
        *   **命中状态**: 用醒目的对勾 (✔) 或叉号 (✘) 表示该规则是否被AI命中。
        *   **得分**: 命中则显示扣分值（如-5），未命中则显示+0。
        *   **可交互性**:
            *   点击命中的规则，中间的通话文本和音频播放器会**自动跳转**到规则被触发的时间点，实现“结果-场景”的快速追溯。
            *   在**复核模式**下，复核员可以直接**修改**规则的命中状态（✔/✘）和扣分值，最终得分会实时重新计算。
*   **复核/申诉详情 (`ReviewInfoPanel`, `AppealInfoPanel`)**:
    *   在只读模式下，如果该通话经历过复核或申诉，这里会展示对应的详情卡片，包括操作人、时间、以及详细的文字意见/理由。

#### 3. 核心交互与操作
*   **三位一体联动**: 页面的最大亮点是**音频、文本、评分规则**三者之间的无缝联动。
    *   `音频播放` -> `文本高亮`
    *   `点击文本` -> `音频跳转`
    *   `点击评分规则` -> `音频和文本跳转到问题发生处`
    这种设计为使用者提供了极致的效率和沉浸式体验，是高效完成复核、申诉处理等工作的关键。
*   **模式化交互**: 通过 `viewMode` 参数，页面能为不同目的的用户（查看者、复核员、申诉处理人、申诉者）提供精准的界面和操作权限，避免了功能混淆和误操作。
*   **从宏观到微观的闭环**: 该页面是所有分析和列表页面的最终落脚点。用户从任何一个报表或列表中发现一条有问题的记录，最终都会来到这里，进行最深入的、场景化的审查和操作。

---