import React, { useState } from 'react';

interface SpeechSpeedConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  speedThreshold: number;
  minCharCount: number;
  checkMode: 'single' | 'average';
}

interface SpeechSpeedTesterProps {
  config: SpeechSpeedConfig;
  testText: string;
  setTestText: (text: string) => void;
}

interface TestResult {
  matched: boolean;
  details: string;
  speedInfo: {
    maxSpeed: number;
    avgSpeed: number;
    fastSentences: Array<{
      sentence: string;
      speed: number;
      duration: number;
      charCount: number;
    }>;
    totalSentences: number;
    testedSentences: number;
  };
}

/**
 * 语速检查实时测试组件
 * 提供测试文本输入和结果显示
 */
const SpeechSpeedTester: React.FC<SpeechSpeedTesterProps> = ({ config, testText, setTestText }) => {
  const [testResult, setTestResult] = useState<TestResult | null>(null);

  // 解析时间戳格式：[00:05-00:08] 或 [5-8]
  const parseTimestamp = (timestamp: string): { start: number; end: number } | null => {
    const match1 = timestamp.match(/\[(\d{2}):(\d{2})-(\d{2}):(\d{2})\]/);
    if (match1) {
      const startSeconds = parseInt(match1[1]) * 60 + parseInt(match1[2]);
      const endSeconds = parseInt(match1[3]) * 60 + parseInt(match1[4]);
      return { start: startSeconds, end: endSeconds };
    }

    const match2 = timestamp.match(/\[(\d+)-(\d+)\]/);
    if (match2) {
      return { start: parseInt(match2[1]), end: parseInt(match2[2]) };
    }

    return null;
  };

  // 计算语速
  const calculateSpeed = (text: string, duration: number): number => {
    const charCount = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;
    return Math.round((charCount / duration) * 60);
  };

  // 执行测试
  const runTest = () => {
    const lines = testText.split('\n').filter(line => line.trim());
    let targetLines: Array<{ text: string; role: string; speed: number; duration: number; charCount: number }> = [];

    // 解析每一行，提取时间戳、角色和内容
    for (const line of lines) {
      const timestampMatch = line.match(/^(\[[\d:-]+\])\s*(.+)/);
      if (!timestampMatch) continue;

      const timestamp = timestampMatch[1];
      const content = timestampMatch[2];
      
      const timeInfo = parseTimestamp(timestamp);
      if (!timeInfo || timeInfo.end <= timeInfo.start) continue;

      const duration = timeInfo.end - timeInfo.start;
      
      // 提取角色和文本
      const roleMatch = content.match(/^(客服|客户)：(.+)/);
      if (!roleMatch) continue;

      const role = roleMatch[1] === '客服' ? 'agent' : 'customer';
      const text = roleMatch[2];
      const charCount = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;

      // 跳过字数不足的句子
      if (charCount < config.minCharCount) continue;

      // 根据检测角色筛选
      if (config.detectionRole !== 'all' && config.detectionRole !== role) continue;

      const speed = calculateSpeed(text, duration);
      
      targetLines.push({
        text: content,
        role,
        speed,
        duration,
        charCount
      });
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetLines = targetLines.slice(config.rangeStart - 1, config.rangeEnd);
    }

    if (targetLines.length === 0) {
      setTestResult({
        matched: false,
        details: '没有找到符合条件的句子进行检测',
        speedInfo: {
          maxSpeed: 0,
          avgSpeed: 0,
          fastSentences: [],
          totalSentences: 0,
          testedSentences: 0
        }
      });
      return;
    }

    const speeds = targetLines.map(line => line.speed);
    const maxSpeed = Math.max(...speeds);
    const avgSpeed = Math.round(speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length);

    let matched = false;
    let details = '';

    if (config.checkMode === 'single') {
      // 单句检测：任一句超过阈值即触发
      matched = maxSpeed > config.speedThreshold;
      const fastSentences = targetLines.filter(line => line.speed > config.speedThreshold);
      
      if (matched) {
        details = `规则命中 - 单句检测模式，发现${fastSentences.length}句语速超过阈值`;
      } else {
        details = `规则未命中 - 单句检测模式，最高语速${maxSpeed}字/分钟未超过${config.speedThreshold}字/分钟`;
      }
    } else {
      // 平均检测：整体平均语速超过阈值
      matched = avgSpeed > config.speedThreshold;
      
      if (matched) {
        details = `规则命中 - 平均检测模式，平均语速${avgSpeed}字/分钟超过阈值`;
      } else {
        details = `规则未命中 - 平均检测模式，平均语速${avgSpeed}字/分钟未超过${config.speedThreshold}字/分钟`;
      }
    }

    const fastSentences = targetLines
      .filter(line => line.speed > config.speedThreshold)
      .map(line => ({
        sentence: line.text,
        speed: line.speed,
        duration: line.duration,
        charCount: line.charCount
      }));

    setTestResult({
      matched,
      details,
      speedInfo: {
        maxSpeed,
        avgSpeed,
        fastSentences,
        totalSentences: lines.length,
        testedSentences: targetLines.length
      }
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              setTestText('[00:05-00:08] 客服：您好，欢迎来电咨询\n[00:09-00:12] 客户：我想了解产品价格\n[00:13-00:16] 客服：好的，我来为您查询\n[00:17-00:20] 客户：谢谢');
              setTestResult(null);
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* 测试文本输入区 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
              📝 测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {testText.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              id="test-text"
              rows={10}
              className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="请输入需要测试的对话文本，格式：[时间戳] 角色：内容&#10;&#10;示例：&#10;[00:05-00:08] 客服：您好，欢迎来电咨询&#10;[00:09-00:12] 客户：我想了解产品&#10;[00:13-00:16] 客服：好的，我来为您查询"
            />
            {testText.trim() && (
              <button
                onClick={() => {
                  setTestText('');
                  setTestResult(null);
                }}
                className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="清空文本"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-4">
            <span>💡 提示：支持 [00:05-00:08] 和 [5-8] 两种时间格式</span>
          </div>
        </div>

        {/* 快速测试按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={runTest}
            disabled={!testText.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>执行测试</span>
            </span>
          </button>
          {testResult && (
            <button
              onClick={() => setTestResult(null)}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>
      </div>
      
      {/* 测试结果 */}
      {testResult && (
        <div className="mt-8 space-y-6">
          {/* 结果总览 */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <span>📊</span>
                <span>测试结果</span>
              </h3>
            </div>
            
            {/* 主要结果卡片 */}
            <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-red-50 to-orange-50 border-red-200' 
                : 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-red-100' : 'bg-green-100'
                  }`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-red-600' : 'text-green-600'}`}>
                      {testResult.matched ? '⚠️' : '✓'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-red-800' : 'text-green-800'}`}>
                      {testResult.matched ? '语速超标' : '语速正常'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-red-700' : 'text-green-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      testResult.matched 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {testResult.matched ? '❌ 超标' : '✅ 正常'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 详细统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-blue-500">📈</span>
                <h4 className="font-semibold text-gray-800">最高语速</h4>
              </div>
              <div className="text-2xl font-bold text-blue-600">{testResult.speedInfo.maxSpeed}</div>
              <div className="text-xs text-gray-500">字/分钟</div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-purple-500">📊</span>
                <h4 className="font-semibold text-gray-800">平均语速</h4>
              </div>
              <div className="text-2xl font-bold text-purple-600">{testResult.speedInfo.avgSpeed}</div>
              <div className="text-xs text-gray-500">字/分钟</div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-orange-500">🎯</span>
                <h4 className="font-semibold text-gray-800">超标句数</h4>
              </div>
              <div className="text-2xl font-bold text-orange-600">{testResult.speedInfo.fastSentences.length}</div>
              <div className="text-xs text-gray-500">/ {testResult.speedInfo.testedSentences} 句</div>
            </div>
          </div>

          {/* 超标句子详情 */}
          {testResult.speedInfo.fastSentences.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-red-500">⚠️</span>
                <h4 className="font-semibold text-gray-800">超标句子</h4>
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {testResult.speedInfo.fastSentences.length} 条
                </span>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {testResult.speedInfo.fastSentences.map((item, i) => (
                  <div key={i} className="p-3 bg-red-50 border border-red-100 rounded-md">
                    <div className="text-sm text-gray-800 font-medium mb-1">
                      {item.sentence}
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-gray-600">
                      <span>语速: <span className="font-medium text-red-600">{item.speed}字/分钟</span></span>
                      <span>时长: {item.duration}秒</span>
                      <span>字数: {item.charCount}字</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 配置摘要 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-gray-500">⚙️</span>
              <h4 className="font-semibold text-gray-800">当前配置</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">检测角色:</span>
                  <span className="font-medium">{config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">检测范围:</span>
                  <span className="font-medium">{config.detectionScope === 'full' ? '全文' : `第${config.rangeStart}-${config.rangeEnd}句`}</span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">语速阈值:</span>
                  <span className="font-medium">{config.speedThreshold}字/分钟</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">检测方式:</span>
                  <span className="font-medium">{config.checkMode === 'single' ? '单句检测' : '平均检测'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SpeechSpeedTester; 