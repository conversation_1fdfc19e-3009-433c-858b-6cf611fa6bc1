import React from 'react';
import { ShieldCheck, Regex, FileText, Repeat, Search, Scale, Sliders, Ban, ListChecks, CheckSquare, XSquare, MessageSquare, BookOpen } from 'lucide-react';

/**
 * 规则配置项展示组件
 * @param {object} props - 组件属性
 * @param {React.ElementType} props.icon - 图标
 * @param {string} props.label - 配置项标签
 * @param {string | React.ReactNode} props.value - 配置值
 * @param {string} props.reason - 配置原因
 */
const ConfigItem: React.FC<{
  icon: React.ElementType;
  label: string;
  value: string | React.ReactNode;
  reason: string;
}> = ({ icon: Icon, label, value, reason }) => (
  <div className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
    <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
      <Icon className="w-5 h-5 text-gray-600" />
    </div>
    <div className="flex-1">
      <div className="flex justify-between items-center">
        <p className="text-sm font-semibold text-gray-800">{label}</p>
        <div className="text-sm font-mono bg-blue-50 text-blue-800 px-2 py-0.5 rounded">{value}</div>
      </div>
      <p className="mt-1 text-xs text-gray-600 leading-relaxed">
        <span className="font-semibold">配置原因:</span> {reason}
      </p>
    </div>
  </div>
);

/**
 * 对话行与高亮标注组件
 */
const ConversationLine: React.FC<{
  line: { speaker: string; text: string };
  highlights: { rule: string; text: string; label: string; details: string; color: string }[];
}> = ({ line, highlights }) => {
  const highlight = highlights.find(h => line.text.includes(h.text));
  const isCustomer = line.speaker === '客户';

  return (
    <div className={`flex items-start gap-3 my-3 ${isCustomer ? 'justify-end' : 'justify-start'}`}>
      {!isCustomer && (
        <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center flex-shrink-0 text-sm font-bold">
          客
        </div>
      )}
      <div className={`relative max-w-xl p-3 rounded-lg ${isCustomer ? 'bg-green-100' : 'bg-white border'}`}>
        <p className="text-sm text-gray-800">{line.text}</p>
        {highlight && (
          <div 
            className={`absolute -top-3 -right-3 flex items-center space-x-1 px-2 py-1 text-xs font-semibold rounded-full border-2 border-white ${highlight.color}`}
            title={highlight.details}
          >
            <span>{highlight.rule}</span>
            <span>{highlight.label}</span>
          </div>
        )}
      </div>
      {isCustomer && (
        <div className="w-8 h-8 rounded-full bg-green-500 text-white flex items-center justify-center flex-shrink-0 text-sm font-bold">
          您
        </div>
      )}
    </div>
  );
};

/**
 * 综合示例：金融产品销售合规性质检
 * 演示如何组合多个算子解决复杂的业务场景
 */
export const ComprehensiveComplianceExample: React.FC = () => {

  const sampleConversation = `客服：您好，王女士，为了保障您的账户安全，我们需要先核实一下您的身份信息，请您提供一下您的身份证号码可以吗？
客户：哦，好的，我的身份证是 440106199003071234。
客服：好的，感谢您的配合，已确认是您本人。接下来，我将为您介绍我行的"稳健增值"理财产品。在介绍前，请允许我宣读风险告知声明：理财非存款，产品有风险，投资须谨慎。
客户：嗯，好的，你说吧。
客服：这款产品是我们目前的主力推荐，历史年化收益率非常可观，能达到5%左右。
客户：听起来还不错，那能保证收益吗？
客服：王女士，请您理解，所有理财产品都不能保证收益，其历史表现不代表未来收益，但我们会尽力为您提供专业的资产配置建议。
客户：好的，我明白了。
客服：请问您还有其他问题吗？
客户：暂时没有了。
客服：好的，那祝您生活愉快，再见。
客户：再见。`;

  const conversationLines = sampleConversation.split('\n').map(line => {
    const parts = line.split('：');
    return { speaker: parts[0], text: parts.slice(1).join('：') };
  });

  const highlights = [
    { 
      rule: 'A', 
      text: '440106199003071234', 
      label: '正则命中', 
      details: '通过正则表达式成功匹配到客户提供的18位身份证号码。',
      color: 'bg-blue-200 text-blue-800'
    },
    { 
      rule: 'B', 
      text: '理财非存款，产品有风险，投资须谨慎。', 
      label: '相似度命中', 
      details: '客服宣读内容与标准风险告知模板高度相似，判定为命中。',
      color: 'bg-indigo-200 text-indigo-800'
    },
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-full">
      <div className="max-w-5xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">金融产品销售合规性质检</h1>
          <p className="mt-2 text-md text-gray-600">
            演示如何通过组合算子 <strong>(A && B) && !C && !D</strong> 实现复杂业务场景的质检
          </p>
        </div>

        {/* 场景与逻辑 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center space-x-3 mb-3">
              <BookOpen className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold">业务场景与质检目标</h2>
            </div>
            <p className="text-sm text-gray-700 leading-relaxed">
              在金融产品电话销售过程中，为确保合规性，需要对通话录音进行质检。
              <br /><strong>目标：</strong>确保客服完成了必要的身份核验和风险告知，同时未出现违规承诺和机械式沟通。
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center space-x-3 mb-3">
              <Sliders className="w-6 h-6 text-purple-600" />
              <h2 className="text-xl font-semibold">逻辑规则</h2>
            </div>
            <p className="font-mono text-center text-lg p-2 bg-purple-50 text-purple-800 rounded-md border border-purple-200">
              ( A && B ) && !C && !D
            </p>
            <ul className="mt-2 text-xs space-y-1 text-gray-600">
              <li><strong>A. 正则检查:</strong> 客服必须获取客户身份证号</li>
              <li><strong>B. 文本相似度:</strong> 客服必须宣读风险告知</li>
              <li><strong>!C. 关键词检查:</strong> 客服<span className="text-red-600 font-bold">不能</span>使用违规承诺词</li>
              <li><strong>!D. 上下文重复:</strong> 客服<span className="text-red-600 font-bold">不能</span>机械重复话术</li>
            </ul>
          </div>
        </div>
        
        {/* 示例对话 */}
        <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-3 flex items-center"><MessageSquare className="w-5 h-5 mr-2 text-gray-500"/>示例对话与规则匹配</h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              {conversationLines.map((line, index) => (
                <ConversationLine key={index} line={line} highlights={highlights} />
              ))}
              <div className="mt-4 pt-4 border-t border-dashed">
                 <p className="text-xs text-gray-600 flex items-start space-x-2">
                    <span className="w-5 h-5 flex-shrink-0 rounded-full bg-red-100 text-red-700 flex items-center justify-center font-bold">!C</span>
                    <span><strong>关键词检查(未命中):</strong> 整个对话中，客服未使用 "保证收益"、"稳赚" 等违规承诺词汇，符合规则要求。</span>
                 </p>
                 <p className="text-xs text-gray-600 flex items-start space-x-2 mt-2">
                    <span className="w-5 h-5 flex-shrink-0 rounded-full bg-red-100 text-red-700 flex items-center justify-center font-bold">!D</span>
                    <span><strong>上下文重复检查(未命中):</strong> 客服的沟通自然流畅，未出现机械地、高相似度地重复话术，符合规则要求。</span>
                 </p>
              </div>
            </div>
        </div>

        {/* 详细配置 */}
        <div>
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">规则配置详解</h2>
          <div className="space-y-8">
            {/* A. 正则表达式检查 */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="w-8 h-8 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center font-bold mr-3">A</span>
                正则表达式检查: 核实客户身份
                <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">必须命中</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ConfigItem icon={Search} label="检测角色" value="客服" reason="需核实客服是否从客户处获取了信息，但检测主体是客服侧的录音文本。" />
                <ConfigItem icon={Scale} label="前置条件" value="无" reason="身份核验是强制步骤，无需任何前置条件即可触发。" />
                <ConfigItem icon={Sliders} label="检测范围" value="全文" reason="客户可能在对话的任何阶段提供身份信息，需覆盖整个通话。" />
                <ConfigItem icon={Regex} label="命中规则" value={<code>/[1-9]\\d{'{5}'}(...)\\d{'{3}'}[\\dX]/i</code>} reason="使用标准的18位身份证号码正则表达式，确保捕获的号码格式正确。" />
                <ConfigItem icon={Ban} label="排除规则" value="无" reason="在此场景下，任何出现的身份证号都应被视为有效信息，无需排除特定模式。" />
                <ConfigItem icon={ListChecks} label="扩展功能" value="单句话内生效: 关" reason="身份证号是一个完整的字符串，不应被标点符号分割，需在整个句子中进行匹配。" />
              </div>
            </div>

            {/* B. 文本相似度检查 */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="w-8 h-8 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center font-bold mr-3">B</span>
                文本相似度检查: 宣读风险告知
                <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">必须命中</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                 <ConfigItem icon={Search} label="检测角色" value="客服" reason="风险告知是客服的法定义务，必须由客服完整宣读。" />
                 <ConfigItem icon={Scale} label="前置条件" value="无" reason="此为强制合规项，应在所有销售对话中进行检查。" />
                 <ConfigItem icon={Sliders} label="检测范围" value="全文" reason="风险告知可能在对话开始或介绍产品前进行，需检查整个通话。" />
                 <ConfigItem icon={ShieldCheck} label="相似度达到" value="90%" reason="法律文书要求高度准确，90%的相似度允许微小口语化差异但保证核心内容不变。" />
                 <ConfigItem icon={FileText} label="相似话术" value="理财非存款，产品有风险..." reason="设置标准的、必须宣读的风险告知法定文本作为比对模板。" />
                 <ConfigItem icon={ListChecks} label="扩展功能" value="单句话内生效: 开" reason="风险告知通常是一句完整的话。开启此功能可确保这句话是独立、清晰地被读出，而非夹杂在其他话语中。" />
              </div>
            </div>

            {/* C. 关键词检查 */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="w-8 h-8 rounded-full bg-red-100 text-red-700 flex items-center justify-center font-bold mr-3">C</span>
                关键词检查: 杜绝违规承诺
                <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">不能命中</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ConfigItem icon={Search} label="检测角色" value="客服" reason="检查点是客服是否使用了违规词汇。" />
                <ConfigItem icon={Scale} label="前置条件" value="无" reason="任何时候都不能有违规承诺。" />
                <ConfigItem icon={Sliders} label="检测范围" value="全文" reason="在整个通话过程中都必须监控违规词汇。" />
                <ConfigItem icon={Ban} label="关键词" value="保证收益, 稳赚, 无风险..." reason="列出金融销售中典型的、被明令禁止的承诺性、诱导性词语。" />
                <ConfigItem icon={CheckSquare} label="分析方式" value="单句分析" reason="只要在任何一句话中出现违规词，就应立即标记，单句分析效率最高。" />
                <ConfigItem icon={XSquare} label="检测类型" value="包含任意一个关键词" reason="任何一个违规词的出现都构成违规，因此使用'或'逻辑。" />
                <ConfigItem icon={ListChecks} label="扩展功能" value="默认关闭" reason="此场景下无需'单句话内生效'或'限定命中次数'等复杂功能，默认配置即可满足需求。" />
              </div>
            </div>
            
            {/* D. 上下文重复检查 */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="w-8 h-8 rounded-full bg-red-100 text-red-700 flex items-center justify-center font-bold mr-3">D</span>
                上下文重复检查: 避免机械沟通
                <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">不能命中</span>
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ConfigItem icon={Search} label="检测角色" value="客服" reason="评估客服的服务质量和沟通技巧。" />
                  <ConfigItem icon={Scale} label="前置条件" value="无" reason="沟通质量是全程需要关注的。" />
                  <ConfigItem icon={Sliders} label="检测范围" value="全文" reason="监控整个对话过程以发现不自然的重复。" />
                </div>
                <div>
                  <h4 className="text-md font-semibold text-gray-800 mb-2 pl-1">检查逻辑</h4>
                  <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-sm text-gray-700">
                    在 <strong className="text-indigo-600">5</strong> 句以内重复，重复句子的相似度大于等于 <strong className="text-indigo-600">0.9</strong>，
                    并且在第 <strong className="text-indigo-600">2</strong> 次重复出现时算违规，
                    当一句话少于或等于 <strong className="text-indigo-600">8</strong> 个字时不检测。
                  </div>
                  <p className="mt-2 text-xs text-gray-600 leading-relaxed pl-1">
                    <span className="font-semibold">配置原因:</span> 此组合设定旨在精确识别机械、无效的重复。
                    <strong>5句内</strong> 是一个合理的上下文窗口；<strong>0.9相似度</strong> 确保只捕获近乎原文的重复；
                    第 <strong>2次</strong> 违规允许偶然的重复（例如为了强调），但惩罚多次重复；
                    <strong>8个字</strong> 的下限排除了"好的"、"是的"等常用短语的干扰。
                  </p>
                </div>
                <div>
                    <h4 className="text-md font-semibold text-gray-800 mb-2 pl-1">例外句子</h4>
                    <ConfigItem icon={Repeat} label="例外句子列表" value="请问您还有其他问题吗？" reason="这是标准的、常用的询问句，在对话不同阶段重复出现是合理的，应将其排除在重复检测之外以避免误判。" />
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveComplianceExample; 