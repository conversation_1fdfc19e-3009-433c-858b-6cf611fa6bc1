import React from 'react';
import type { Condition } from '../types';
import { ConditionType, DetectionRole, PrerequisiteCondition, DetectionScope, AnalysisMethod, KeywordDetectionType } from '../types';
import { ChevronDownIcon, ChevronUpIcon, TrashIcon, QuestionMarkCircleIcon, SparklesIcon, XMarkIcon } from './icons';
import { Tooltip } from './common/Tooltip';

interface ConditionBlockProps {
  condition: Condition;
  conditionIndex: number;
  onChange: <K extends keyof Condition>(index: number, field: K, value: Condition[K]) => void;
  onAddKeyword: (conditionIndex: number, keyword: string) => void;
  onRemoveKeyword: (conditionIndex: number, keywordToRemove: string) => void;
  onRemove: () => void;
  onToggleCollapse: () => void;
  isEven: boolean;
}

const LabelledInput: React.FC<{ label: string; children: React.ReactNode; tooltip?: string; htmlFor?: string; className?: string }> = ({ label, children, tooltip, htmlFor, className = "" }) => (
  <div className={`grid grid-cols-1 md:grid-cols-4 items-center gap-2 md:gap-4 ${className}`}>
    <label htmlFor={htmlFor || ""} className="text-sm font-medium text-gray-600 flex items-center">
      {label}
      {tooltip && (
        <Tooltip text={tooltip}>
          <QuestionMarkCircleIcon className="w-4 h-4 ml-1 text-gray-400" />
        </Tooltip>
      )}
    </label>
    <div className="md:col-span-3">{children}</div>
  </div>
);

export const ConditionBlock: React.FC<ConditionBlockProps> = ({
  condition,
  conditionIndex,
  onChange,
  onAddKeyword,
  onRemoveKeyword,
  onRemove,
  onToggleCollapse,
  isEven,
}) => {
  const baseId = `condition-${condition.id}-${conditionIndex}`;

  const handleInputChange = <K extends keyof Condition>(field: K, value: Condition[K] | string | number | boolean) => {
    onChange(conditionIndex, field, value as Condition[K]);
  };
  
  const handleNumericInputChange = <K extends keyof Condition>(field: K, value: string) => {
    const num = parseInt(value, 10);
    if (!isNaN(num)) {
      onChange(conditionIndex, field, num as Condition[K]);
    } else if (value === '') {
       onChange(conditionIndex, field, 0 as Condition[K]); // Or handle empty string as needed
    }
  };

  const currentKeywords = condition.type === ConditionType.KEYWORD ? condition.keywords || [] : [];
  const currentKeywordInput = condition.type === ConditionType.KEYWORD ? condition.currentKeywordInput || '' : '';

  return (
    <div className={`p-4 rounded-md shadow ${isEven ? 'bg-white' : 'bg-slate-50'}`}>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center">
          <button onClick={onToggleCollapse} className="mr-2 text-blue-600">
            {condition.isCollapsed ? <ChevronDownIcon className="w-5 h-5" /> : <ChevronUpIcon className="w-5 h-5" />}
          </button>
          <span className="font-semibold text-blue-700">{condition.id}. {condition.name}</span>
           <span className="ml-2 text-xs text-gray-500">({condition.type === ConditionType.REGEX ? '正则表达式检查' : '关键词检查'})</span>
        </div>
        <button onClick={onRemove} className="text-gray-400 hover:text-red-500">
          <TrashIcon className="w-5 h-5" />
        </button>
      </div>

      {!condition.isCollapsed && (
        <div className="space-y-4 pl-2">
          <LabelledInput label="检测角色" htmlFor={`${baseId}-role`}>
            <select
              id={`${baseId}-role`}
              value={condition.detectionRole}
              onChange={(e) => handleInputChange('detectionRole', e.target.value as DetectionRole)}
              className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
            >
              {Object.values(DetectionRole).map(role => <option key={role} value={role}>{role}</option>)}
            </select>
          </LabelledInput>

          {/* 条件A不显示前置条件，因为它是第一个条件 */}
          {condition.prerequisite !== PrerequisiteCondition.NONE && (
            <LabelledInput label="前置条件" htmlFor={`${baseId}-prerequisite`}>
              <select
                id={`${baseId}-prerequisite`}
                value={condition.prerequisite}
                onChange={(e) => handleInputChange('prerequisite', e.target.value as PrerequisiteCondition)}
                className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
              >
                 {Object.values(PrerequisiteCondition).map(prereq => <option key={prereq} value={prereq}>{prereq}</option>)}
              </select>
            </LabelledInput>
          )}

          {/* 条件A不显示"条件"字段，因为正则表达式检查通常不需要指定命中次数 */}
          {condition.prerequisite !== PrerequisiteCondition.NONE && (
            <LabelledInput label="条件" htmlFor={`${baseId}-hitOrder`} tooltip="第几次命中该条件">
               <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">第</span>
                  <input
                      type="number"
                      id={`${baseId}-hitOrder`}
                      value={condition.hitOrder}
                      onChange={(e) => handleNumericInputChange('hitOrder', e.target.value)}
                      className="w-20 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                      min="1"
                  />
                  <span className="text-sm text-gray-700">次命中</span>
              </div>
            </LabelledInput>
          )}

          <LabelledInput label="检测范围" htmlFor={`${baseId}-scope`}>
            <select
              id={`${baseId}-scope`}
              value={condition.detectionScope}
              onChange={(e) => handleInputChange('detectionScope', e.target.value as DetectionScope)}
              className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
            >
              {condition.prerequisite === PrerequisiteCondition.NONE ? (
                // 没有前置条件：显示"全文"和"指定范围"
                <>
                  <option value={DetectionScope.ENTIRE_TEXT}>{DetectionScope.ENTIRE_TEXT}</option>
                  <option value={DetectionScope.SPECIFIED_RANGE}>{DetectionScope.SPECIFIED_RANGE}</option>
                </>
              ) : (
                // 有前置条件：显示前置条件相关的选项
                <>
                  <option value={DetectionScope.BEFORE_HIT}>{DetectionScope.BEFORE_HIT}</option>
                  <option value={DetectionScope.AFTER_HIT}>{DetectionScope.AFTER_HIT}</option>
                  <option value={DetectionScope.AROUND_HIT}>{DetectionScope.AROUND_HIT}</option>
                </>
              )}
            </select>
          </LabelledInput>
          
          {/* 当检测范围为"指定范围"或前置条件相关范围时显示范围设置 */}
          {(condition.detectionScope === DetectionScope.SPECIFIED_RANGE || 
            condition.detectionScope === DetectionScope.BEFORE_HIT ||
            condition.detectionScope === DetectionScope.AFTER_HIT ||
            condition.detectionScope === DetectionScope.AROUND_HIT) && (
            <LabelledInput label="范围" tooltip="检测范围内的第几句到第几句">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">第</span>
                <input 
                  type="number" 
                  value={condition.rangeStart} 
                  onChange={(e) => handleNumericInputChange('rangeStart', e.target.value)}
                  className="w-20 p-2 border border-gray-300 rounded-md text-sm bg-white" min="1" 
                />
                <span className="text-sm text-gray-700">~</span>
                <input 
                  type="number" 
                  value={condition.rangeEnd} 
                  onChange={(e) => handleNumericInputChange('rangeEnd', e.target.value)}
                  className="w-20 p-2 border border-gray-300 rounded-md text-sm bg-white" min={condition.rangeStart || 1}
                />
                <span className="text-sm text-gray-700">句</span>
              </div>
            </LabelledInput>
          )}

          {/* Regex Specific Fields */}
          {condition.type === ConditionType.REGEX && (
            <>
              <LabelledInput label="* 命中" htmlFor={`${baseId}-hitPattern`}>
                <textarea
                  id={`${baseId}-hitPattern`}
                  rows={2}
                  value={condition.hitPattern || ''}
                  onChange={(e) => handleInputChange('hitPattern', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                  placeholder=".*名下.*(房|车).*"
                />
                 <p className="text-xs text-gray-500 mt-1">默认使用的匹配策略是：不区分大小写、多行匹配、换行符\n也算作一个字符。 <a href="#" className="text-blue-600 hover:underline">如何使用正则表达式</a></p>
              </LabelledInput>
              <LabelledInput label="排除" htmlFor={`${baseId}-excludePattern`}>
                <div className="relative">
                    <textarea
                        id={`${baseId}-excludePattern`}
                        rows={2}
                        value={condition.excludePattern || ''}
                        onChange={(e) => handleInputChange('excludePattern', e.target.value)}
                        className="w-full p-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                        placeholder="输入自然语言，AI帮您自动优化正则表达式"
                    />
                    <SparklesIcon className="w-5 h-5 text-purple-500 absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer" />
                </div>
              </LabelledInput>
              <LabelledInput label="扩展功能" tooltip="是否仅在单句话内生效">
                <label className="flex items-center text-sm">
                  <input
                    type="checkbox"
                    checked={condition.effectiveInSingleSentence}
                    onChange={(e) => handleInputChange('effectiveInSingleSentence', e.target.checked)}
                    className="mr-1.5 h-4 w-4 bg-white border border-gray-300 text-blue-600 rounded focus:ring-blue-500"
                  />
                  单句话内生效
                </label>
              </LabelledInput>

              <LabelledInput label="测试" htmlFor={`${baseId}-testInput`}>
                <div className="flex space-x-2">
                    <input
                        type="text"
                        id={`${baseId}-testInput`}
                        value={condition.testInput || ''}
                        onChange={(e) => handleInputChange('testInput', e.target.value)}
                        className="flex-grow p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                        placeholder="输入测试语句按enter键进行测试"
                    />
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">测试</button>
                </div>
              </LabelledInput>
            </>
          )}

          {/* Keyword Specific Fields */}
          {condition.type === ConditionType.KEYWORD && (
            <>
              <LabelledInput label="关键词" htmlFor={`${baseId}-keywords`}>
                <div className="relative">
                  <input
                    type="text"
                    id={`${baseId}-keywords`}
                    value={currentKeywordInput}
                    onChange={(e) => handleInputChange('currentKeywordInput', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && currentKeywordInput.trim()) {
                        e.preventDefault();
                        onAddKeyword(conditionIndex, currentKeywordInput);
                      }
                    }}
                    className="w-full p-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                    placeholder="输入按enter键添加关键词, 多个可用逗号隔开"
                  />
                  <button 
                    type="button" 
                    onClick={() => {if(currentKeywordInput.trim()) onAddKeyword(conditionIndex, currentKeywordInput);}}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-purple-500 hover:text-purple-700"
                    title="添加关键词"
                  >
                    <SparklesIcon className="w-5 h-5" />
                  </button>
                </div>
                {currentKeywords.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {currentKeywords.map(kw => (
                      <span key={kw} className="flex items-center bg-gray-200 text-gray-700 text-sm px-2 py-1 rounded-full">
                        {kw}
                        <button onClick={() => onRemoveKeyword(conditionIndex, kw)} className="ml-1.5 text-gray-500 hover:text-gray-700">
                          <XMarkIcon className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </LabelledInput>

              <LabelledInput label="分析方式" tooltip="选择分析的粒度">
                <div className="flex space-x-4">
                  {(Object.keys(AnalysisMethod) as Array<keyof typeof AnalysisMethod>).map(key => (
                     <label key={key} className="flex items-center text-sm">
                        <input
                        type="radio"
                        name={`${baseId}-analysisMethod`}
                        value={AnalysisMethod[key]}
                        checked={condition.analysisMethod === AnalysisMethod[key]}
                        onChange={() => handleInputChange('analysisMethod', AnalysisMethod[key])}
                        className="mr-1.5 h-4 w-4 bg-white border border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        {AnalysisMethod[key]}
                    </label>
                  ))}
                </div>
              </LabelledInput>

              <LabelledInput label="检测类型" tooltip="定义关键词如何被匹配">
                <div className="flex flex-wrap gap-x-4 gap-y-2">
                  {(Object.keys(KeywordDetectionType) as Array<keyof typeof KeywordDetectionType>).map(key => {
                    // 条件B中"包含任意N个关键词"选项应该禁用
                    const isDisabled = condition.id === 'b' && KeywordDetectionType[key] === KeywordDetectionType.CONTAINS_ANY_N;
                    return (
                      <label key={key} className={`flex items-center text-sm ${isDisabled ? 'text-gray-400' : ''}`}>
                          <input
                          type="radio"
                          name={`${baseId}-keywordDetectionType`}
                          value={KeywordDetectionType[key]}
                          checked={condition.keywordDetectionType === KeywordDetectionType[key]}
                          onChange={() => handleInputChange('keywordDetectionType', KeywordDetectionType[key])}
                          disabled={isDisabled}
                          className={`mr-1.5 h-4 w-4 border border-gray-300 text-blue-600 focus:ring-blue-500 ${isDisabled ? 'bg-gray-100' : 'bg-white'}`}
                          />
                          {KeywordDetectionType[key]}
                           {KeywordDetectionType[key] === KeywordDetectionType.CONTAINS_ANY_N && condition.id !== 'b' && condition.id !== 'c' && condition.id !== 'd' && condition.id !== 'e' && condition.id !== 'f' && condition.id !== 'h' && (
                              <input 
                                  type="number" 
                                  value={condition.nValueForKeyword || 1}
                                  onChange={(e) => handleNumericInputChange('nValueForKeyword', e.target.value)}
                                  className="ml-2 w-12 p-1 border border-gray-300 rounded-md text-sm bg-white"
                                  min="1"
                              />
                          )}
                      </label>
                    );
                  })}
                </div>
              </LabelledInput>

              <LabelledInput label="扩展功能" tooltip="是否仅在单句话内生效">
                <label className={`flex items-center text-sm ${(condition.id === 'b' || condition.id === 'c' || condition.id === 'd' || condition.id === 'e') ? 'text-gray-400' : ''}`}>
                  <input
                    type="checkbox"
                    checked={condition.effectiveInSingleSentence}
                    onChange={(e) => handleInputChange('effectiveInSingleSentence', e.target.checked)}
                    disabled={condition.id === 'b' || condition.id === 'c' || condition.id === 'd' || condition.id === 'e'}
                    className={`mr-1.5 h-4 w-4 border border-gray-300 text-blue-600 rounded focus:ring-blue-500 ${(condition.id === 'b' || condition.id === 'c' || condition.id === 'd' || condition.id === 'e') ? 'bg-gray-100' : 'bg-white'}`}
                  />
                  单句话内生效
                </label>
              </LabelledInput>
            </>
          )}
        </div>
      )}
    </div>
  );
};
