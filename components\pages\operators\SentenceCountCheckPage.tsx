import React, { useState } from 'react';
import { PageHeader } from '../../PageHeader';
import SentenceCountConcepts from './sentenceCountCheck/SentenceCountConcepts';
import SentenceCountConfig from './sentenceCountCheck/SentenceCountConfig';
import SentenceCountTester from './sentenceCountCheck/SentenceCountTester';
import SentenceCountCases from './sentenceCountCheck/SentenceCountCases';
import SentenceCountTips from './sentenceCountCheck/SentenceCountTips';

/**
 * @typedef {object} SentenceCountConfigType
 * @property {'agent' | 'customer' | 'all'} detectionRole
 * @property {'full' | 'range'} detectionScope
 * @property {number} rangeStart
 * @property {number} rangeEnd
 * @property {'greater' | 'less' | 'equal'} operator
 * @property {number} count
 */
export interface SentenceCountConfigType {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  operator: 'greater' | 'less' | 'equal';
  count: number;
}

/**
 * @typedef {object} TestResultType
 * @property {boolean} matched
 * @property {number} actualCount
 * @property {string} details
 */
export interface TestResultType {
  matched: boolean;
  actualCount: number;
  details: string;
}

/**
 * 对话语句数检测页面
 * @returns {React.ReactElement}
 */
const SentenceCountCheckPage: React.FC = () => {
  /** @type {[SentenceCountConfigType, React.Dispatch<React.SetStateAction<SentenceCountConfigType>>]} */
  const [config, setConfig] = useState<SentenceCountConfigType>({
    detectionRole: 'all',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 5,
    operator: 'greater',
    count: 10,
  });

  /** @type {[string, React.Dispatch<React.SetStateAction<string>>]} */
  const [testText, setTestText] = useState(
    '客服：您好，欢迎使用我们的服务。\n客户：你好，我想咨询一个问题。\n客服：请讲。\n客户：我的订单状态是什么？\n客服：正在为您查询，请稍候。\n客服：您的订单已发货。\n客户：好的，谢谢。\n客服：不客气。'
  );

  /** @type {[TestResultType | null, React.Dispatch<React.SetStateAction<TestResultType | null>>]} */
  const [testResult, setTestResult] = useState<TestResultType | null>(null);

  const pageInfo = {
    title: '💬 对话语句数检测',
    description: '检测指定角色在对话中的语句数量是否满足特定条件。',
    tag: '基础入门',
    badge: '语音检查',
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader {...pageInfo} />

      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧区域 */}
          <div className="w-[60%] space-y-6">
            <SentenceCountConcepts />
            <SentenceCountConfig config={config} setConfig={setConfig} />
            <SentenceCountTester
              config={config}
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              setTestResult={setTestResult}
            />
          </div>

          {/* 右侧区域 */}
          <div className="w-[40%] space-y-6">
            <SentenceCountCases setConfig={setConfig} setTestText={setTestText} />
            <SentenceCountTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2 text-sm">
                <p>• <strong>理解基础</strong>：首先明确"检测角色"和"检测范围"如何筛选句子。</p>
                <p>• <strong>掌握逻辑</strong>：多尝试"大于"、"小于"、"等于"三种不同逻辑，观察其对结果的影响。</p>
                <p>• <strong>实践应用</strong>：使用"小于 1 句"来快速筛选客服或客户未发言的无效通话。</p>
                <p>• <strong>组合思考</strong>：思考如何将"对话语句数检测"与"录音时长检测"等其他算子结合，以构建更全面的质检规则。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SentenceCountCheckPage; 