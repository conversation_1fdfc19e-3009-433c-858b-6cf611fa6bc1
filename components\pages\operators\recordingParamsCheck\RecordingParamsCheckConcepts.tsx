import React from 'react';

/**
 * 随录参数检查 - 核心概念组件
 * @constructor
 */
export const RecordingParamsCheckConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          核心概念
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>

      <div className="space-y-6">
        {/* 功能介绍 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                结合系统中传过来的随录参数字段进行质检。产品的功能价值是，可以深度结合系统里的业务字段，进行灵活的检测。支持的随录字段包括两类：（一）常规字段：系统默认字段+自定义 25 个字段，客服姓名、客服 ID、技能组名称、呼叫类型等；（二）更多自定义字段：即大 JSON 传过来的字段。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">不同业务线话术检查</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">特定技能组质检</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">根据呼叫类型筛选</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">验证自定义业务数据</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">与业务紧密集成</span>
                    <span className="text-xs text-gray-500 ml-1">- 深入业务场景</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">高度灵活性</span>
                    <span className="text-xs text-gray-500 ml-1">- 支持多种字段和操作符</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">配置简单直观</span>
                    <span className="text-xs text-gray-500 ml-1">- 快速上手</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 检测原理 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🔧</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">检测原理</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                算子通过读取通话中附加的随录参数（通常是一个JSON对象），获取指定字段的值。然后，根据配置的比较操作符（如等于、包含等）和设定的目标值进行逻辑判断。当所有条件都满足时，规则命中。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 