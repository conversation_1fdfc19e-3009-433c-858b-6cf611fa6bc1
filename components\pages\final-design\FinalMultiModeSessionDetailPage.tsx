import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useParams, useLocation, Link, useNavigate } from 'react-router-dom';
import {
    User, HardDrive, Phone, Calendar, Clock, AlertTriangle, Play, Pause, Rewind, FastForward, UserCircle, Bot,
    Smile, Frown, MicOff, MessageSquare, Target, Sparkles, ChevronLeft, Check, X, CheckCircle, XCircle, ChevronRight, FileText, Shield, Flag, ClipboardList
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Tooltip } from '../../common/Tooltip';

// --- Types ---
interface Alert {
    type: 'sentiment' | 'keyword';
    message: string;
    timestamp: number;
    severity: 'high' | 'medium' | 'low';
}

interface BaseTranscriptItem {
    speaker: 'agent' | 'customer' | 'system';
    text: string;
    timestamp: number;
}

interface TranscriptItem extends BaseTranscriptItem {
    id: number;
    sentiment?: 'positive' | 'neutral' | 'negative';
    interrupt?: boolean;
    silence_start?: boolean;
    silence_end?: boolean;
    keyword?: string;
}

interface ScoringRule {
    id: string;
    name: string;
    description: string;
    isHit: boolean;
    score: number;
    timestamp?: number;
}

interface ReviewDetails {
    reviewer: { id: string; name: string };
    timestamp: string;
    notes: string;
}

interface AppealDetails {
    appellant: { id: string; name: string; teamName: string; };
    timestamp: string;
    reason: string;
    status?: 'approved' | 'rejected';
    processor?: { id:string; name: string };
    processedTimestamp?: string;
    processorNotes?: string;
}

interface TaskDetails {
    taskId: string;
    taskName: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    statusReason?: string;
    scopeSummary: string;
    scoringSchemeName: string;
    source: 'manual' | 'from_plan';
    creator: { id: string; name: string };
    creationDate: string;
    dueDate: string;
    baseScore?: number;
    passingScore?: number;
    assignTime?: string;
}

interface SessionData {
    id: string;
    agent: { id: string; name: string; teamName: string; };
    customer: { id: string; phone: string };
    startTime: string;
    duration: number;
    machineScore?: number;
    reviewScore?: number;
    appealScore?: number;
    finalScore: number;
    finalScoreSource: 'machine' | 'review' | 'appeal';
    alerts: Alert[];
    reviewTriggers?: string[];
    transcript: TranscriptItem[];
    scoringRules: ScoringRule[]; // Machine-scored rules
    reviewedScoringRules?: ScoringRule[]; // Manually reviewed rules
    reviewDetails?: ReviewDetails;
    appealDetails?: AppealDetails;
    taskDetails?: TaskDetails;
    appealStatus: 'eligible' | 'in_progress' | 'processed' | 'expired';
    appealDeadline?: string;
}

// --- Mock Data ---
const mockSessionData: SessionData & { transcript: TranscriptItem[] } = {
    id: 'S_A001_01',
    agent: { id: 'A001', name: '王伟', teamName: 'A组' },
    customer: { id: 'C_12345', phone: '138****1234' },
    startTime: '2023-10-27 14:30:05',
    duration: 215, // seconds
    machineScore: 72,
    reviewScore: 80, // Adjusted from 85 to 80 for consistency (100 - 5 - 10 - 5 = 80)
    appealScore: 90,
    finalScore: 90,
    finalScoreSource: 'appeal',
    alerts: [
        { type: 'sentiment', message: '客户情绪激动，评分为-0.8', timestamp: 125, severity: 'high' },
        { type: 'keyword', message: '客户提及关键词 "投诉"', timestamp: 142, severity: 'medium' },
    ],
    reviewTriggers: [
        '随机抽样复核',
        '低分触发复核',
    ],
    appealStatus: 'eligible',
    appealDeadline: '2023-10-30 18:00:00',
    scoringRules: [
        { id: 'RULE-001', name: '标准开场白检测', description: '检测坐席开场白是否符合标准句式。', isHit: true, score: -5, timestamp: 5 },
        { id: 'RULE-002', name: '合规声明-录音告知', description: '验证坐席是否在通话开始时进行了录音告知。', isHit: false, score: 0 },
        { id: 'RULE-003', name: '上下文重复询问', description: '检测坐席是否在短时间内重复询问客户相同问题。', isHit: true, score: -10, timestamp: 28 },
        { id: 'RULE-004', name: '客户情绪-激动模型', description: '使用AI模型判断客户是否存在激动情绪。', isHit: true, score: -13, timestamp: 125 },
        { id: 'RULE-005', name: '客服辱骂检测模型', description: '监控客服在对话中是否存在不文明用语。', isHit: false, score: 0 },
    ],
    reviewedScoringRules: [
        { id: 'RULE-001', name: '标准开场白检测', description: '检测坐席开场白是否符合标准句式。', isHit: true, score: -5, timestamp: 5 },
        { id: 'RULE-002', name: '合规声明-录音告知', description: '验证坐席是否在通话开始时进行了录音告知。', isHit: false, score: 0 },
        { id: 'RULE-003', name: '上下文重复询问', description: '检测坐席是否在短时间内重复询问客户相同问题。', isHit: true, score: -10, timestamp: 28 },
        { id: 'RULE-004', name: '客户情绪-激动模型', description: '使用AI模型判断客户是否存在激动情绪。', isHit: true, score: -5, timestamp: 125 },
        { id: 'RULE-005', name: '客服辱骂检测模型', description: '监控客服在对话中是否存在不文明用语。', isHit: false, score: 0 },
    ],
    reviewDetails: {
        reviewer: { id: 'MGR-002', name: '李经理' },
        timestamp: '2023-10-27 16:10:25',
        notes: '经核实，客户情绪确实激动，但坐席应对基本得体，未激化矛盾。将"客户情绪-激动模型"扣分由-13调整为-5。同时，坐席未能在第一时间有效安抚，酌情保留扣分。'
    },
    appealDetails: {
        appellant: { id: 'A001', name: '王伟', teamName: 'A组' },
        timestamp: '2023-10-28 09:30:15',
        reason: '客户本人情绪非常激动，我已尽力安抚，但客户仍多次打断。我认为情绪模型的-5分扣分过重，非我之过。',
        status: 'approved',
        processor: { id: 'DIR-001', name: '赵总监' },
        processedTimestamp: '2023-10-28 14:00:00',
        processorNotes: '申诉通过。考虑到客户的特殊情况，坐席在受压下仍保持了较高服务水平。本次扣分予以撤销，最终成绩调整为90分。已同步更新AI模型样本。'
    },
    taskDetails: {
        taskId: 'T-20231101-001',
        taskName: '11月营销活动通话质检',
        status: 'in_progress',
        scopeSummary: '坐席组: A组, B组 (等3个) | 通话时间: 2023.11.01-11.10',
        scoringSchemeName: '标准服务流程质检方案',
        source: 'manual',
        creator: { id: 'MGR-001', name: '张主管' },
        creationDate: '2023-11-01 10:30',
        dueDate: '2023-11-30 18:00:00',
        baseScore: 100,
        passingScore: 80,
        assignTime: '2023-10-27 15:00:00'
    },
    transcript: [
        { id: 1, speaker: 'agent', text: '您好，这里是xx银行贷后服务中心，请问是王先生吗？', timestamp: 5 },
        { id: 2, speaker: 'customer', text: '是我，你们怎么又打电话来了？我都说了好几遍了，下个月才还得起！', timestamp: 15 },
        { id: 3, speaker: 'agent', text: '王先生您别激动，我们只是按照规定进行提醒，没有催促您的意思。', timestamp: 28 },
        { id: 4, speaker: 'customer', text: '别跟我说这些！每次都一样的话术！你们这样天天骚扰，我受不了了！', timestamp: 45, sentiment: 'neutral' },
        { id: 5, speaker: 'agent', text: '真的很抱歉给您带来了不好的感受。我们也是希望能够帮助您...', timestamp: 62 },
        { id: 6, speaker: 'customer', text: '帮助？你们就是催债！烦死了！再打电话过来，我就要去银监会投诉你们！', timestamp: 125, sentiment: 'negative' },
        { id: 7, speaker: 'agent', text: '您先消消气，我们绝对没有骚扰您的意思。关于您的困难，我们可以一起看看有没有别的解决办法。', timestamp: 138 },
        { id: 8, speaker: 'customer', text: '别说了，我不想听！总之，下个月之前别再打了！', timestamp: 142, keyword: '投诉' },
        { id: 9, speaker: 'agent', text: '好的好的，王先生，我们尊重您的意见。那我们下个月再联系您。', timestamp: 160 },
        { id: 10, speaker: 'customer', text: '嗯。', timestamp: 175, silence_start: true },
        { id: 11, speaker: 'agent', text: '那祝您生活愉快，再见。', timestamp: 200, silence_end: true },
    ],
};

const EventTag: React.FC<{item: TranscriptItem}> = ({ item }) => {
    if (item.sentiment === 'negative') {
        return <div className="mt-2 flex items-center text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full w-fit"><Frown className="w-4 h-4 mr-1.5" />负面情绪</div>;
    }
    if (item.keyword) {
        return <div className="mt-2 flex items-center text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full w-fit"><Target className="w-4 h-4 mr-1.5" />关键词: {item.keyword}</div>;
    }
    if (item.silence_start) {
        return <div className="mt-2 flex items-center text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full w-fit"><MicOff className="w-4 h-4 mr-1.5" />长时静默</div>;
    }
    return null;
}

// --- Score Panel Component ---
interface ScorePanelProps {
    machineScore?: number;
    reviewScore?: number;
    appealScore?: number;
    finalScore: number;
    finalScoreSource: 'machine' | 'review' | 'appeal';
    passingScore?: number;
}

const ScorePanel: React.FC<ScorePanelProps> = ({ machineScore, reviewScore, appealScore, finalScore, finalScoreSource, passingScore = 80 }) => {
    
    const getSourceLabel = (source: typeof finalScoreSource) => {
        switch (source) {
            case 'appeal': return '申诉裁定';
            case 'review': return '人工复核';
            case 'machine': default: return 'AI初检';
        }
    };

    const sourceLabel = getSourceLabel(finalScoreSource);
    const sourceColorClass = {
        appeal: 'bg-green-100 text-green-800 border-green-200',
        review: 'bg-indigo-100 text-indigo-800 border-indigo-200',
        machine: 'bg-gray-100 text-gray-800 border-gray-200',
    }[finalScoreSource];

    const isStepActive = (step: 'machine' | 'review' | 'appeal') => {
        if (finalScoreSource === 'appeal') return true;
        if (finalScoreSource === 'review') return step !== 'appeal';
        if (finalScoreSource === 'machine') return step === 'machine';
        return false;
    };

    const isStepFinal = (step: 'machine' | 'review' | 'appeal') => step === finalScoreSource;

    // 判断是否合格
    const isPassing = finalScore >= passingScore;
    const qualityStatus = isPassing ? '合格' : '不合格';
    const qualityColorClass = isPassing 
        ? 'bg-green-100 text-green-800 border-green-200' 
        : 'bg-red-100 text-red-800 border-red-200';

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
            {/* Final Score Display */}
            <div className="text-center">
                <p className="text-sm font-semibold text-gray-500">最终得分</p>
                <div className="my-2 text-6xl font-bold text-gray-800">{finalScore}</div>
                <div className="flex justify-center gap-2 mb-2">
                    <div className={`inline-block px-3 py-1 text-sm font-bold rounded-full border ${sourceColorClass}`}>
                        {sourceLabel}
                    </div>
                    <div className={`inline-block px-3 py-1 text-sm font-bold rounded-full border ${qualityColorClass}`}>
                        {qualityStatus}
                    </div>
                </div>
                <p className="text-xs text-gray-500">合格分数线: {passingScore}分</p>
            </div>

            {/* Score Evolution Flow */}
            <div className="mt-6">
                <h4 className="text-sm font-bold text-gray-600 mb-3">分数演进</h4>
                <div className="flex items-center justify-between text-xs text-center">
                    {/* Machine Score */}
                    <div className={`flex flex-col items-center transition-opacity ${isStepActive('machine') ? 'opacity-100' : 'opacity-40'}`}>
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${isStepFinal('machine') ? 'bg-blue-500 text-white border-blue-600' : 'bg-gray-100 border-gray-300'}`}>
                            <span className="font-bold text-lg">{machineScore ?? 'N/A'}</span>
                        </div>
                        <p className="mt-1 font-semibold">AI初检</p>
                    </div>

                    <div className={`flex-1 h-0.5 mx-2 ${isStepActive('review') ? 'bg-blue-400' : 'bg-gray-200'}`}></div>

                    {/* Review Score */}
                    <div className={`flex flex-col items-center transition-opacity ${isStepActive('review') ? 'opacity-100' : 'opacity-40'}`}>
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${isStepFinal('review') ? 'bg-indigo-500 text-white border-indigo-600' : 'bg-gray-100 border-gray-300'}`}>
                            <span className="font-bold text-lg">{reviewScore ?? 'N/A'}</span>
                        </div>
                        <p className="mt-1 font-semibold">复核</p>
                    </div>

                    <div className={`flex-1 h-0.5 mx-2 ${isStepActive('appeal') ? 'bg-blue-400' : 'bg-gray-200'}`}></div>
                    
                    {/* Appeal Score */}
                    <div className={`flex flex-col items-center transition-opacity ${isStepActive('appeal') ? 'opacity-100' : 'opacity-40'}`}>
                         <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${isStepFinal('appeal') ? 'bg-green-500 text-white border-green-600' : 'bg-gray-100 border-gray-300'}`}>
                            <span className="font-bold text-lg">{appealScore ?? 'N/A'}</span>
                        </div>
                        <p className="mt-1 font-semibold">申诉</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

// --- Sub-components ---
interface AudioPlayerProps {
    duration: number;
    currentTime: number;
    setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
    isPlaying: boolean;
    setIsPlaying: React.Dispatch<React.SetStateAction<boolean>>;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ duration, currentTime, setCurrentTime, isPlaying, setIsPlaying }) => {
    const progressRef = useRef<HTMLDivElement>(null);
    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (!progressRef.current) return;
        const rect = progressRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const newTime = (x / rect.width) * duration;
        setCurrentTime(newTime);
    };
    return (
        <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-center gap-6">
                <button className="text-gray-600 hover:text-black"><Rewind /></button>
                <button onClick={() => setIsPlaying(!isPlaying)} className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center shadow-lg">
                    {isPlaying ? <Pause size={32} /> : <Play size={32} className="ml-1" />}
                </button>
                <button className="text-gray-600 hover:text-black"><FastForward /></button>
            </div>
            <div className="flex items-center gap-4 mt-4 text-sm">
                <span className="font-mono text-gray-600">{new Date(currentTime * 1000).toISOString().substr(14, 5)}</span>
                <div ref={progressRef} onClick={handleProgressClick} className="w-full h-2 bg-gray-200 rounded-full cursor-pointer">
                    <motion.div
                        className="h-2 bg-blue-500 rounded-full"
                        style={{ width: `${(currentTime / duration) * 100}%` }}
                    />
                </div>
                <span className="font-mono text-gray-600">{new Date(duration * 1000).toISOString().substr(14, 5)}</span>
            </div>
        </div>
    );
};

interface TranscriptPanelProps {
    transcript: TranscriptItem[];
    currentTime: number;
    setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
}

const TranscriptPanel: React.FC<TranscriptPanelProps> = ({ transcript, currentTime, setCurrentTime }) => {
    const activeRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        if (activeRef.current) {
            activeRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, [currentTime]);
    return (
        <div className="h-[125vh] overflow-y-auto pr-4 space-y-4">
            {transcript.map((item, i) => {
                const isAgent = item.speaker === 'agent';
                const isActive = currentTime >= item.timestamp && (transcript[i + 1] ? currentTime < transcript[i + 1].timestamp : true);
                return (
                    <div
                        key={i}
                        ref={isActive ? activeRef : null}
                        onClick={() => setCurrentTime(item.timestamp)}
                        className={`flex gap-3 cursor-pointer p-3 rounded-lg transition-all duration-300 ${isActive ? 'bg-blue-100' : 'hover:bg-gray-50'}`}
                    >
                        {isAgent ? <Bot className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" /> : <UserCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />}
                        <div className="w-full">
                            <div className="flex justify-between items-center">
                                <span className={`font-semibold ${isAgent ? 'text-blue-700' : 'text-green-700'}`}>{isAgent ? '坐席' : '客户'}</span>
                                <span className="text-xs text-gray-500 font-mono">{new Date(item.timestamp * 1000).toISOString().substr(14, 5)}</span>
                            </div>
                            <p className="mt-1 text-gray-800">{item.text}</p>
                            <EventTag item={item} />
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

interface ReviewPanelProps {
    machineScore?: number;
    reviewScore: number;
    onSubmit: (notes: string) => void;
    onCancel: () => void;
}

const ReviewPanel: React.FC<ReviewPanelProps> = ({ machineScore, reviewScore, onSubmit, onCancel }) => {
  const [reviewNotes, setReviewNotes] = useState('');
  
  const handleSubmit = () => {
    onSubmit(reviewNotes);
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <h2 className="text-xl font-bold text-gray-800 mb-4">复核操作</h2>
      <div className="space-y-4">
        {machineScore !== undefined && (
            <div>
                <label className="text-sm font-medium text-gray-500">AI初检得分</label>
                <div className="mt-1 p-3 bg-gray-100 rounded-lg text-lg font-bold text-gray-800">{machineScore}</div>
            </div>
        )}
        <div>
            <label className="text-sm font-medium text-gray-500">复核后得分</label>
            <div className="mt-1 p-3 bg-blue-50 border border-blue-200 rounded-lg text-2xl font-bold text-blue-600">{reviewScore}</div>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-700" htmlFor="review-notes">复核意见</label>
          <textarea id="review-notes" value={reviewNotes} onChange={(e) => setReviewNotes(e.target.value)} rows={4} placeholder="输入详细的复核意见或备注..." className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
        </div>
        <div className="flex justify-end gap-3 pt-2">
            <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">取消</button>
            <button onClick={handleSubmit} className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">提交复核结果</button>
        </div>
      </div>
    </div>
  );
}

interface AppealPanelProps {
    scoreToAppeal: number;
    scoreType: 'machine' | 'review';
    onSubmit: (decision: 'approve' | 'reject', newScore: number | null, notes: string) => void;
    onCancel: () => void;
}
  
const AppealPanel: React.FC<AppealPanelProps> = ({ scoreToAppeal, scoreType, onSubmit, onCancel }) => {
    const [decision, setDecision] = useState<'approve' | 'reject' | null>(null);
    const [notes, setNotes] = useState('');
    const [newScore, setNewScore] = useState<number | null>(null);
    const scoreLabel = scoreType === 'review' ? '复核后得分' : 'AI初检得分';

    const handleSubmit = () => {
        if (!decision) return;
        onSubmit(decision, newScore, notes);
    };

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-800 mb-4">申诉处理</h2>
            <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm font-semibold text-yellow-800">坐席申诉理由：</p>
                    <p className="mt-1 text-sm text-yellow-900">"客户实际已经表达了满意的意愿，但模型误判为负面情绪，导致扣分，申请核实。"</p>
                </div>
                <div>
                    <label className="text-sm font-medium text-gray-500">{scoreLabel}</label>
                    <div className="mt-1 p-3 bg-gray-100 rounded-lg text-lg font-bold text-gray-800">{scoreToAppeal}</div>
                </div>
                <div>
                    <span className="text-sm font-medium text-gray-700">处理决定</span>
                    <div className="mt-2 grid grid-cols-2 gap-3">
                        <button onClick={() => setDecision('approve')} className={`px-4 py-2 text-sm font-semibold rounded-lg border-2 transition-colors ${decision === 'approve' ? 'bg-green-600 text-white border-green-600' : 'bg-white text-green-700 border-gray-300 hover:bg-green-50'}`}>同意申诉</button>
                        <button onClick={() => setDecision('reject')} className={`px-4 py-2 text-sm font-semibold rounded-lg border-2 transition-colors ${decision === 'reject' ? 'bg-red-600 text-white border-red-600' : 'bg-white text-red-700 border-gray-300 hover:bg-red-50'}`}>驳回申诉</button>
                    </div>
                </div>
                {decision === 'approve' && (
                    <div>
                        <label className="text-sm font-medium text-gray-700">调整后分数</label>
                        <div className="mt-2">
                            <input
                                type="number"
                                min="0"
                                max="100"
                                value={newScore ?? ''}
                                onChange={(e) => setNewScore(e.target.value ? Number(e.target.value) : null)}
                                placeholder="输入新的分数"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg font-bold text-blue-600"
                            />
                        </div>
                        <p className="mt-1 text-xs text-gray-500">请输入0-100之间的分数</p>
                    </div>
                )}
                <div>
                    <label className="text-sm font-medium text-gray-700" htmlFor="appeal-notes">处理意见</label>
                    <textarea id="appeal-notes" value={notes} onChange={(e) => setNotes(e.target.value)} rows={3} placeholder="输入处理说明..." className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
                </div>
                <div className="flex justify-end gap-3 pt-2">
                    <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">取消</button>
                    <button
                        onClick={handleSubmit}
                        className={`px-6 py-2 text-sm font-semibold text-white rounded-lg transition-colors ${
                            !decision || (decision === 'approve' && newScore === null)
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                        disabled={!decision || (decision === 'approve' && newScore === null)}
                    >
                        提交处理结果
                    </button>
                </div>
            </div>
        </div>
    );
}

interface PerformanceCheckPanelProps {
    appealStatus: SessionData['appealStatus'];
    onInitiateAppeal: () => void;
}

const PerformanceCheckPanel: React.FC<PerformanceCheckPanelProps> = ({ appealStatus, onInitiateAppeal }) => {
    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800">申诉操作</h3>
            <p className="mt-2 text-sm text-gray-500">回顾本次质检，并采取下一步行动。</p>
            <div className="mt-4 space-y-3">
                {appealStatus === 'eligible' && (
                    <button 
                        onClick={onInitiateAppeal}
                        className="w-full px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                        发起申诉
                    </button>
                )}
            </div>
        </div>
    );
};

interface InitiateAppealPanelProps {
    onSubmit: (reason: string) => void;
    onCancel: () => void;
}

const InitiateAppealPanel: React.FC<InitiateAppealPanelProps> = ({ onSubmit, onCancel }) => {
    const [reason, setReason] = useState('');
    
    const handleSubmit = () => {
        if (!reason.trim()) {
            alert('申诉理由不能为空');
            return;
        }
        onSubmit(reason);
    }

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-800 mb-4">发起申诉</h2>
            <div className="space-y-4">
                <div>
                    <label className="text-sm font-medium text-gray-700" htmlFor="appeal-reason">申诉理由</label>
                    <textarea 
                        id="appeal-reason" 
                        value={reason} 
                        onChange={(e) => setReason(e.target.value)} 
                        rows={5} 
                        placeholder="请详细填写您的申诉理由，例如说明您认为评分不准确的具体情况和原因。" 
                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                    />
                </div>
                <div className="flex justify-end gap-3 pt-2">
                    <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">取消</button>
                    <button onClick={handleSubmit} className="px-6 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">提交申诉</button>
                </div>
            </div>
        </div>
    );
}

const BasicViewPanel = () => {
    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800">可用操作</h3>
            <p className="mt-2 text-sm text-gray-500">当前为只读查看模式，无可用操作。</p>
        </div>
    );
};

const ReviewTriggerPanel: React.FC<{ triggers: string[] }> = ({ triggers }) => {
    if (!triggers || triggers.length === 0) {
        return null;
    }

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <Flag className="w-6 h-6 text-orange-500" />
                <span>触发复核原因</span>
            </h3>
            <div className="space-y-3">
                {triggers.map((trigger, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-orange-50/70 rounded-lg border border-orange-200/60">
                        <div className="w-5 h-5 mt-0.5 rounded-full bg-orange-500 text-white flex items-center justify-center flex-shrink-0 text-xs font-bold">
                            !
                        </div>
                        <span className="text-gray-700 font-medium">{trigger}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};

interface ScoringDetailsPanelProps {
    rules: ScoringRule[],
    originalRules?: ScoringRule[],
    onRuleClick: (ts: number) => void,
    viewMode: string,
    taskStatus: string | null,
    onReviewChange?: (ruleId: string, newIsHit: boolean) => void,
    onReviewScoreChange?: (ruleId: string, newScore: number) => void
}

const ScoringDetailsPanel: React.FC<ScoringDetailsPanelProps> = ({ rules, originalRules, onRuleClick, viewMode, taskStatus, onReviewChange, onReviewScoreChange }) => {
    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800 mb-4">评分详情</h3>
            <div className="space-y-3">
                {rules.map(rule => {
                    const isClickable = rule.isHit && typeof rule.timestamp === 'number';
                    const canReview = viewMode === 'review' && taskStatus !== 'completed' && onReviewChange && onReviewScoreChange;
                    
                    // Logic to check for changes against original machine-scored rules
                    const baseRules = originalRules || [];
                    const originalRule = baseRules.find(r => r.id === rule.id);
                    const hasChanged = originalRule && (originalRule.isHit !== rule.isHit || (rule.isHit && originalRule.score !== rule.score));

                    const backgroundClass = rule.isHit ? 'bg-red-50/50' : 'bg-green-50/50';
                    const borderClass = hasChanged 
                        ? 'border-blue-400' 
                        : (rule.isHit ? 'border-red-200/60' : 'border-green-200/60');

                    return (
                        <div
                            key={rule.id}
                            className={`p-4 rounded-lg flex items-start gap-4 transition-all duration-200 ${backgroundClass} border ${borderClass}`}
                        >
                            <div
                                onClick={() => isClickable && onRuleClick(rule.timestamp!)}
                                className={`flex-grow flex items-start gap-4 ${isClickable ? 'cursor-pointer' : ''}`}
                            >
                                <div>
                                    {rule.isHit ? (
                                        <div className="w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center flex-shrink-0 shadow-sm">
                                            <X className="w-4 h-4" />
                                        </div>
                                    ) : (
                                        <div className="w-6 h-6 rounded-full bg-green-500 text-white flex items-center justify-center flex-shrink-0 shadow-sm">
                                            <Check className="w-4 h-4" />
                                        </div>
                                    )}
                                </div>
                                <div className="flex-grow">
                                    <p className="font-semibold text-gray-800">{rule.name}</p>
                                    <p className="text-sm text-gray-500 mt-1">{rule.description}</p>
                                </div>
                                <div className="ml-auto text-right flex-shrink-0 w-24">
                                     {canReview ? ( // In Review Mode
                                        <div>
                                            {rule.isHit ? (
                                                <input
                                                    type="number"
                                                    value={rule.score}
                                                    onChange={(e) => onReviewScoreChange(rule.id, parseInt(e.target.value, 10))}
                                                    className="w-full text-xl font-bold text-red-600 bg-transparent text-right border-b-2 border-dashed border-red-300 focus:border-red-500 focus:outline-none appearance-none"
                                                    style={{ MozAppearance: 'textfield' }}
                                                />
                                            ) : (
                                                <p className="text-xl font-bold text-green-600">+0</p>
                                            )}
                                            <p className="text-xs text-gray-500 mt-0.5">{rule.isHit ? '复核扣分' : '复核通过'}</p>
                                            {hasChanged && originalRule && (
                                                <p className="text-xs text-gray-400 mt-1 line-through">
                                                    机审: {originalRule.isHit ? originalRule.score : '+0'}
                                                </p>
                                            )}
                                        </div>
                                    ) : ( // Read-only views
                                        <div>
                                            <p className={`text-xl font-bold ${rule.isHit ? 'text-red-600' : 'text-green-600'}`}>
                                                {rule.isHit ? rule.score : '+0'}
                                            </p>
                                            <p className="text-xs text-gray-500 mt-0.5">{rule.isHit ? (hasChanged ? '复核扣分' : '命中扣分') : (hasChanged ? '复核通过' : '通过')}</p>
                                            {hasChanged && originalRule && (
                                                 <p className="text-xs text-gray-400 mt-1 line-through">
                                                    机审: {originalRule.isHit ? originalRule.score : '+0'}
                                                </p>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {canReview && (
                                <div className="border-l pl-3 ml-3 flex items-center">
                                    {rule.isHit ? (
                                        <Tooltip text="标记为通过">
                                            <button onClick={() => onReviewChange(rule.id, false)} className="p-2 text-green-600 hover:bg-green-100 rounded-full transition-colors">
                                                <CheckCircle className="w-5 h-5" />
                                            </button>
                                        </Tooltip>
                                    ) : (
                                        <Tooltip text="标记为违规">
                                            <button onClick={() => onReviewChange(rule.id, true)} className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors">
                                                <XCircle className="w-5 h-5" />
                                            </button>
                                        </Tooltip>
                                    )}
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

// --- New Info Panels for Read-only Views ---
const ProcessTimeline: React.FC<{ session: SessionData, viewMode: string, taskStatus: string | null }> = ({ session, viewMode, taskStatus }) => (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h3 className="text-lg font-bold text-gray-800 mb-5">处理时间轴</h3>
        <ul className="space-y-4">
            <li className="flex gap-4">
                <div className="flex flex-col items-center">
                    <div className="w-4 h-4 rounded-full bg-gray-300 flex-shrink-0"></div>
                    <div className="w-0.5 flex-1 bg-gray-200"></div>
                </div>
                <div>
                    <p className="font-semibold text-gray-700">AI初检完成</p>
                    <p className="text-xs text-gray-500">{session.startTime}</p>
                </div>
            </li>
            {session.reviewDetails && (
                 <li className="flex gap-4">
                    <div className="flex flex-col items-center">
                        <div className="w-4 h-4 rounded-full bg-indigo-400 flex-shrink-0"></div>
                        <div className="w-0.5 flex-1 bg-gray-200"></div>
                    </div>
                    <div>
                        <p className="font-semibold text-gray-700">人工复核 by {session.reviewDetails.reviewer.name}</p>
                        <p className="text-xs text-gray-500">{session.reviewDetails.timestamp}</p>
                    </div>
                </li>
            )}
             {session.appealDetails && (
                <>
                    <li className="flex gap-4">
                        <div className="flex flex-col items-center">
                            <div className="w-4 h-4 rounded-full bg-yellow-400 flex-shrink-0"></div>
                            <div className="w-0.5 flex-1 bg-gray-200"></div>
                        </div>
                        <div>
                            <p className="font-semibold text-gray-700">坐席提起申诉 by {session.appealDetails.appellant.name}</p>
                            <p className="text-xs text-gray-500">{session.appealDetails.timestamp}</p>
                        </div>
                    </li>
                    {session.appealDetails.processor && (viewMode !== 'appeal_processing' || taskStatus === 'completed') && (
                        <li className="flex gap-4">
                            <div className="flex flex-col items-center">
                                <div className={`w-4 h-4 rounded-full ${session.appealDetails.status === 'approved' ? 'bg-green-500' : 'bg-red-500'} flex-shrink-0`}></div>
                            </div>
                            <div>
                                <p className="font-semibold text-gray-700">申诉处理完成 by {session.appealDetails.processor.name}</p>
                                <p className="text-xs text-gray-500">{session.appealDetails.processedTimestamp}</p>
                            </div>
                        </li>
                    )}
                </>
            )}
        </ul>
    </div>
);

const ReviewInfoPanel: React.FC<{ details: ReviewDetails }> = ({ details }) => (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
         <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
            <FileText className="w-5 h-5 text-indigo-500" />
            <span>复核详情</span>
        </h3>
        <div className="space-y-3 text-sm">
            <p><span className="font-semibold text-gray-600">复核人：</span>{details.reviewer.name}</p>
            <p><span className="font-semibold text-gray-600">复核时间：</span>{details.timestamp}</p>
            <div className="pt-2">
                <p className="font-semibold text-gray-600 mb-1">复核意见：</p>
                <p className="p-3 bg-gray-50 rounded-md border border-gray-200/80 text-gray-700 whitespace-pre-wrap">{details.notes}</p>
            </div>
        </div>
    </div>
);

const AppealInfoPanel: React.FC<{ details: AppealDetails }> = ({ details }) => (
     <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
            <Shield className="w-5 h-5 text-yellow-500" />
            <span>申诉详情</span>
        </h3>
        <div className="space-y-4 text-sm">
            <div>
                 <p className="font-semibold text-gray-600 mb-1">坐席申诉理由 ({details.appellant.name} at {details.timestamp}):</p>
                 <p className="p-3 bg-yellow-50 rounded-md border border-yellow-200/80 text-yellow-900 whitespace-pre-wrap">{details.reason}</p>
            </div>
            {details.status && details.processor && (
                <div className="border-t pt-4">
                    <p className="font-semibold text-gray-600 mb-1">
                        处理结果： 
                        <span className={`ml-2 font-bold ${details.status === 'approved' ? 'text-green-600' : 'text-red-600'}`}>
                            {details.status === 'approved' ? '申诉通过' : '申诉驳回'}
                        </span>
                    </p>
                    <p><span className="font-semibold text-gray-600">处理人：</span>{details.processor.name}</p>
                    <p><span className="font-semibold text-gray-600">处理时间：</span>{details.processedTimestamp}</p>
                    <div className="pt-2">
                        <p className="font-semibold text-gray-600 mb-1">处理意见：</p>
                        <p className="p-3 bg-gray-50 rounded-md border border-gray-200/80 text-gray-700 whitespace-pre-wrap">{details.processorNotes}</p>
                    </div>
                </div>
            )}
        </div>
    </div>
);

const TaskInfoPanel: React.FC<{ details: TaskDetails; session: SessionData; viewMode: string; }> = ({ details, session, viewMode }) => {
    
    const statusMap = {
        eligible: { text: '可申诉', color: 'blue' },
        in_progress: { text: '申诉中', color: 'yellow' },
        processed: { text: '已处理', color: 'gray' },
        expired: { text: '已过期', color: 'red' },
    };
    const appealStatusInfo = statusMap[session.appealStatus];

    const isAppealView = viewMode === 'appeal_processing';
    const isPerformanceView = viewMode === 'performance_check';

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <ClipboardList className="w-5 h-5 text-purple-500" />
                <span>任务信息</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-5 text-sm">
                <div>
                    <p className="text-gray-500">记录编号</p>
                    <p className="font-medium text-gray-800 mt-1">{session.id}</p>
                </div>
                <div>
                    <p className="text-gray-500">所属任务</p>
                    <p className="font-medium text-gray-800 mt-1">{details.taskName}</p>
                </div>
                <div>
                    <p className="text-gray-500">质检方案</p>
                    <p className="font-medium text-gray-800 mt-1">{details.scoringSchemeName}</p>
                </div>
                <div>
                    <p className="text-gray-500">基准总分</p>
                    <p className="font-medium text-gray-800 mt-1">{details.baseScore}</p>
                </div>
                <div>
                    <p className="text-gray-500">合格分数线</p>
                    <p className="font-medium text-gray-800 mt-1">{details.passingScore}</p>
                </div>
                
                {isAppealView && (
                    <div>
                        <p className="text-gray-500">申请时间</p>
                        <p className="font-medium text-gray-800 mt-1">{session.appealDetails?.timestamp}</p>
                    </div>
                )}
                
                {isPerformanceView && (
                    <>
                        <div>
                            <p className="text-gray-500">申诉状态</p>
                            <p className="font-medium text-gray-800 mt-1">
                                <span className={`px-2 py-1 text-xs font-medium bg-${appealStatusInfo.color}-100 text-${appealStatusInfo.color}-800 rounded-full`}>
                                    {appealStatusInfo.text}
                                </span>
                            </p>
                        </div>
                        <div>
                            <p className="text-gray-500">申诉有效期</p>
                            <p className="font-medium text-gray-800 mt-1">{session.appealDeadline || 'N/A'}</p>
                        </div>
                    </>
                )}

                { !isAppealView && !isPerformanceView && viewMode !== 'basic_view' && (
                     <div>
                        <p className="text-gray-500">分配时间</p>
                        <p className="font-medium text-gray-800 mt-1">{details.assignTime}</p>
                    </div>
                )}
            </div>
        </div>
    );
};

// 在其他面板组件后添加新的CallInfoPanel组件
const CallInfoPanel: React.FC<{ session: SessionData; viewMode: string; }> = ({ session, viewMode }) => {
    const isAppeal = viewMode === 'appeal_processing' && session.appealDetails;
    const isPerformance = viewMode === 'performance_check';

    const getAgentLabel = () => {
        if (isPerformance) return '坐席';
        if (isAppeal) return '申请坐席';
        return '被检坐席';
    };

    const getAgentInfo = () => {
        const agent = isAppeal ? session.appealDetails!.appellant : session.agent;
        return `${agent.name}/${agent.id}`;
    };

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <Phone className="w-5 h-5 text-blue-500" />
                <span>通话信息</span>
            </h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                    <p className="text-gray-500 mb-1">{getAgentLabel()}</p>
                    <p className="font-medium text-gray-800 mt-1">{getAgentInfo()}</p>
                </div>
                <div>
                    <p className="text-gray-500 mb-1">所属班组</p>
                    <p className="font-medium text-gray-800 mt-1">{isAppeal ? session.appealDetails!.appellant.teamName : session.agent.teamName}</p>
                </div>
                <div>
                    <p className="text-gray-500 mb-1">客户号码</p>
                    <p className="font-medium text-gray-800 mt-1">{session.customer.phone}</p>
                </div>
                <div>
                    <p className="text-gray-500 mb-1">通话开始时间</p>
                    <p className="font-medium text-gray-800 mt-1">{session.startTime}</p>
                </div>
                <div>
                    <p className="text-gray-500 mb-1">通话时长</p>
                    <p className="font-medium text-gray-800 mt-1">{Math.floor(session.duration / 60)}分{session.duration % 60}秒</p>
                </div>
            </div>
        </div>
    );
};

// --- Main Component ---
const MultiModeSessionDetailPage: React.FC = () => {
    const navigate = useNavigate();
    const { sessionId } = useParams<{ sessionId: string }>();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const viewMode = queryParams.get('viewMode') || 'basic_view';
    const taskStatus = queryParams.get('status');

    const [session, setSession] = useState<SessionData>(mockSessionData);
    const [currentTime, setCurrentTime] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isInitiatingAppeal, setIsInitiatingAppeal] = useState(false);
    
    // State for review modifications
    const [reviewedScoringRules, setReviewedScoringRules] = useState<ScoringRule[]>(session.reviewedScoringRules || session.scoringRules);

    const calculatedReviewScore = useMemo(() => {
        const deductions = reviewedScoringRules.reduce((acc, rule) => {
            return acc + (rule.isHit ? Math.abs(rule.score) : 0);
        }, 0);
        return (session.taskDetails?.baseScore ?? 100) - deductions;
    }, [reviewedScoringRules, session.taskDetails?.baseScore]);

    useEffect(() => {
        // Reset review state if session data changes
        setReviewedScoringRules(session.reviewedScoringRules || session.scoringRules);
    }, [session]);
    
    const handleRuleReviewChange = (ruleId: string, newIsHit: boolean) => {
        setReviewedScoringRules(prevRules =>
            prevRules.map(rule =>
                rule.id === ruleId ? { ...rule, isHit: newIsHit } : rule
            )
        );
    };

    const handleRuleScoreChange = (ruleId: string, newScore: number) => {
        setReviewedScoringRules(prevRules =>
            prevRules.map(rule =>
                rule.id === ruleId ? { ...rule, score: isNaN(newScore) ? 0 : newScore } : rule
            )
        );
    };

    const displayedRules = useMemo(() => {
        if (viewMode === 'review') {
            return reviewedScoringRules;
        }
        return session.reviewedScoringRules || session.scoringRules;
    }, [viewMode, reviewedScoringRules, session]);

    useEffect(() => {
        let interval: number;
        if (isPlaying && currentTime < session.duration) {
            interval = window.setInterval(() => {
                setCurrentTime(prevTime => prevTime + 1);
            }, 1000);
        }
        return () => window.clearInterval(interval);
    }, [isPlaying, currentTime, session.duration]);

    const handleInitiateAppeal = () => {
        setIsInitiatingAppeal(true);
    };

    const handleCancelInitiateAppeal = () => {
        setIsInitiatingAppeal(false);
    };

    const handleSubmitInitiateAppeal = (reason: string) => {
        alert(`申诉已提交！\n理由：${reason}`);
        setSession(prev => ({
            ...prev,
            appealStatus: 'in_progress',
            appealDetails: {
                appellant: prev.agent,
                timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
                reason: reason,
            }
        }));
        setIsInitiatingAppeal(false);
    };

    const scorePanelProps: ScorePanelProps = (() => {
        const passingScore = session.taskDetails?.passingScore || 80;
        switch (viewMode) {
            case 'review':
                return {
                    machineScore: session.machineScore,
                    reviewScore: calculatedReviewScore,
                    appealScore: undefined,
                    finalScore: calculatedReviewScore,
                    finalScoreSource: 'review',
                    passingScore,
                };
            case 'appeal_processing':
                if (taskStatus === 'completed') {
                    // 查看已完成的申诉，显示最终结果
                    return {
                        machineScore: session.machineScore,
                        reviewScore: session.reviewScore,
                        appealScore: session.appealScore,
                        finalScore: session.finalScore,
                        finalScoreSource: session.finalScoreSource,
                        passingScore,
                    };
                }
                // 正在处理申诉，显示申诉前的分数
                return {
                    machineScore: session.machineScore,
                    reviewScore: session.reviewScore,
                    appealScore: undefined,
                    finalScore: session.reviewScore || session.machineScore || 0,
                    finalScoreSource: session.reviewScore ? 'review' : 'machine',
                    passingScore,
                };
            case 'performance_check':
                if (isInitiatingAppeal) {
                    return {
                        machineScore: session.machineScore,
                        reviewScore: session.reviewScore,
                        appealScore: undefined,
                        finalScore: session.reviewScore || session.machineScore || 0,
                        finalScoreSource: session.reviewScore ? 'review' : 'machine',
                        passingScore,
                    };
                }
                return {
                    machineScore: session.machineScore,
                    reviewScore: session.reviewScore,
                    appealScore: session.appealScore,
                    finalScore: session.finalScore,
                    finalScoreSource: session.finalScoreSource,
                    passingScore,
                };
            default:
                return {
                    machineScore: session.machineScore,
                    reviewScore: session.reviewScore,
                    appealScore: session.appealScore,
                    finalScore: session.finalScore,
                    finalScoreSource: session.finalScoreSource,
                    passingScore,
                };
        }
    })();

    const renderActionPanel = () => {
        switch (viewMode) {
            case 'review':
                if (taskStatus === 'completed') {
                    return session.reviewDetails ? <ReviewInfoPanel details={session.reviewDetails} /> : null;
                }
                return <ReviewPanel 
                    machineScore={session.machineScore} 
                    reviewScore={calculatedReviewScore} 
                    onSubmit={handleSubmitReview}
                    onCancel={handleCancel}
                />;
            case 'appeal_processing':
                if (taskStatus === 'completed') {
                    return session.appealDetails ? <AppealInfoPanel details={session.appealDetails} /> : null;
                }
                return <AppealPanel 
                    scoreToAppeal={session.reviewScore ?? session.machineScore ?? 0}
                    scoreType={session.reviewScore !== undefined ? 'review' : 'machine'}
                    onSubmit={handleSubmitAppeal}
                    onCancel={handleCancelAppeal}
                />;
            case 'performance_check':
                if (isInitiatingAppeal) {
                    return <InitiateAppealPanel 
                        onSubmit={handleSubmitInitiateAppeal}
                        onCancel={handleCancelInitiateAppeal}
                    />;
                }
                return <PerformanceCheckPanel appealStatus={session.appealStatus} onInitiateAppeal={handleInitiateAppeal} />;
            case 'basic_view':
            default:
                return <BasicViewPanel />;
        }
    };

    const handleCancel = () => {
        navigate('/final-design/my-review-tasks');
    }

    const handleSubmitReview = (notes: string) => {
        alert(`提交成功!\n复核后得分: ${calculatedReviewScore}\n复核备注: ${notes}`);
        navigate('/final-design/my-review-tasks');
    }

    const handleCancelAppeal = () => {
        navigate('/final-design/appeal-processing');
    };

    const handleSubmitAppeal = (decision: 'approve' | 'reject', newScore: number | null, notes: string) => {
        alert(`申诉处理提交成功!\n决定: ${decision === 'approve' ? '同意' : '驳回'}\n调整后分数: ${newScore ?? '未调整'}\n处理备注: ${notes}`);
        navigate('/final-design/appeal-processing');
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col">
            {/* 返回按钮 */}
            <div className="bg-white shadow-sm">
                <div className="max-w-screen-2xl mx-auto px-6">
                    <button
                        onClick={() => navigate(-1)}
                        className="flex items-center text-gray-600 hover:text-gray-900 transition-colors py-4"
                    >
                        <ChevronLeft className="w-5 h-5 mr-1" />
                        <span>返回列表</span>
                    </button>
                </div>
            </div>
            
            {/* 主要内容区域 */}
            <main className="flex-1 p-6 overflow-auto">
                <div className="max-w-screen-2xl mx-auto">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="md:col-span-2 space-y-6">
                            {session.taskDetails && <TaskInfoPanel 
                                details={session.taskDetails} 
                                session={session} 
                                viewMode={viewMode}
                            />}
                            <CallInfoPanel session={session} viewMode={viewMode} />
                            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                                <AudioPlayer duration={session.duration} currentTime={currentTime} setCurrentTime={setCurrentTime} isPlaying={isPlaying} setIsPlaying={setIsPlaying} />
                                <div className="mt-6 border-t border-gray-200 pt-6">
                                    <h3 className="text-lg font-bold text-gray-800 mb-4">通话文本</h3>
                                    <TranscriptPanel transcript={(session as SessionData & { transcript: TranscriptItem[] }).transcript} currentTime={currentTime} setCurrentTime={setCurrentTime} />
                                </div>
                            </div>
                        </div>
                        <div className="w-full space-y-6">
                            {viewMode === 'review' && session.reviewTriggers && <ReviewTriggerPanel triggers={session.reviewTriggers} />}
                            <ScorePanel {...scorePanelProps} />

                            {viewMode !== 'review' && <ProcessTimeline session={session} viewMode={viewMode} taskStatus={taskStatus} />}

                            <ScoringDetailsPanel
                                rules={displayedRules}
                                originalRules={session.scoringRules}
                                onRuleClick={(timestamp) => setCurrentTime(timestamp)}
                                viewMode={viewMode}
                                taskStatus={taskStatus}
                                onReviewChange={handleRuleReviewChange}
                                onReviewScoreChange={handleRuleScoreChange}
                            />

                            {viewMode !== 'review' && session.reviewDetails && <ReviewInfoPanel details={session.reviewDetails} />}
                            {viewMode !== 'review' && viewMode !== 'appeal_processing' && session.appealDetails && <AppealInfoPanel details={session.appealDetails} />}
                            
                            {renderActionPanel()}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default MultiModeSessionDetailPage;