
---

### 页面五：我的复核任务 (FinalMyReviewTasksPage.tsx)

#### 1. 核心定位与目标
该页面是**复核员**的核心工作界面，其目标是提供一个清晰、高效的任务列表，让复核员能够系统地管理和处理所有分配给自己的复核任务。它取代了首页上仅展示几条任务的“快速待办列表”，提供了一个完整的、可筛选、可追溯的任务中心。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "我的复核任务"
*   **副标题**: "管理和处理分配给我的质检复核任务"
*   **图标**: `ClipboardCheck` (带对勾的剪贴板)，象征着待审核的任务列表。
*   **徽章**: 动态显示关键信息，例如：“待处理 2 条”，时刻提醒复核员当前的工作负荷。

**b. Tab切换 (待处理 / 已完成)**
页面主体内容通过两个Tab页进行划分，这是任务管理类页面的经典设计，用于区分待办事项和历史记录。
*   **待处理 (`pending`)**:
    *   **显示内容**: 所有状态为“待处理”的复核任务。
    *   **徽章**: Tab标签上会用数字角标显示待处理任务的数量，例如“待处理 (2)”，非常直观。
*   **已完成 (`completed`)**:
    *   **显示内容**: 所有已由当前复核员处理完毕的任务。
    *   **徽章**: 同样显示已完成任务的总数。

**c. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 提供强大的筛选功能，帮助复核员在大量任务中快速定位到特定任务。
*   **筛选字段**:
    *   **通用字段**: 记录编号、所属任务、被检坐席、所属班组、AI初检得分范围、分配时间范围、质检结果。
    *   **“已完成”Tab专属字段**: 最终得分范围。
*   **交互**:
    *   筛选器默认展开一部分常用条件，更多不常用条件可通过“展开/收起”按钮控制，保持界面整洁。
    *   提供“查询”和“重置”按钮，操作逻辑清晰。

**d. 任务表格 (`Table`)**
这是页面的核心内容区，以表格形式展示任务列表，根据当前选择的Tab页动态显示不同的列。

*   **“待处理”表格列**:
    *   `序号`
    *   `记录编号`: 通话的唯一ID。
    *   `所属任务`: 该记录隶属的质检任务名称。
    *   `被检坐席`: 显示坐席姓名和工号。
    *   `所属班组`
    *   `通话时长`
    *   `触发复核原因`: 关键信息，告知复核员为何需要人工介入（如“低分触发”、“随机抽样”）。
    *   `AI初检得分`: 标出分数，并用不同颜色区分分数段（红/黄/绿），让复核员有初步预期。
    *   `分配时间`
    *   `操作`: 提供一个醒目的 **“处理”** 按钮。

*   **“已完成”表格列**:
    *   除了“待处理”的大部分列外，新增/替换了以下列：
    *   `最终得分`: 显示复核后给出的最终分数。
    *   `质检结果`: 显示最终评定的“合格”或“不合格”。
    *   `处理时间`: 记录复核完成的时间。
    *   `操作`: 按钮变为 **“查看”**，允许复核员回顾自己的历史处理记录。

*   **表格特点**:
    *   **高亮与可读性**: 关键信息如分数、状态、原因等都通过颜色、徽章等方式进行了视觉强化。
    *   **响应式**: 表格支持横向滚动，以适应不同屏幕尺寸。
    *   **空状态**: 当没有任务时，会显示友好的提示信息（如“暂无匹配的任务”）。

**e. 分页组件 (`UnifiedPagination`)**
*   **位置**: 表格下方。
*   **功能**: 当任务数量超过一页时，提供标准的分页功能，包括页码切换、每页显示数量选择等，确保页面加载性能和可用性。

#### 3. 核心交互与操作
*   **任务处理流程**: 复核员的主要动线是：查看“待处理”列表 -> 选择一个任务 -> 点击“处理” -> 跳转到 **[多模式会话详情页]** (`FinalMultiModeSessionDetailPage`) 进行复核 -> 完成后，该任务会自动从“待处理”列表移到“已完成”列表。
*   **信息查询与追溯**: 复核员可以通过切换到“已完成”Tab并利用筛选器，方便地查找和回顾自己过去处理过的任何一条记录。
*   **批量操作 (潜在扩展)**: 当前设计未明确展示，但这类页面通常可以扩展支持批量操作，如批量分配、批量退回等。

---