import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Users,
    BarChart3,
    AlertTriangle,
    Award,
    Download,
    RefreshCw,
    ChevronDown,
    ChevronUp,
    Brain,
    UserCheck,
    MessageSquareX,
    Target,
    Zap,
    Eye,
    Clock,
    Percent,
    Calculator,
    ChevronRight,
    Play,
    Star,
    ArrowUpDown,
    Filter,
    TrendingUp,
    Activity
} from 'lucide-react';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { EnhancedKPICard } from './common/EnhancedKPICard';
import { EnhancedChartContainer } from './common/EnhancedChartContainer';
import { EnhancedRankingCard } from './common/EnhancedRankingCard';
import { EnhancedFilterSection } from './common/EnhancedFilterSection';
import { EnhancedSectionHeader } from './common/EnhancedSectionHeader';
import { EnhancedTooltip } from './common/EnhancedTooltip';
import { chartTheme, chartColors, chartTypeConfigs } from './common/ChartTheme';
import { 
    LineChart,
    Line,
    BarChart,
    Bar,
    XAxis, 
    YAxis, 
    CartesianGrid, 
    Tooltip, 
    Legend, 
    ResponsiveContainer,
    Cell
} from 'recharts';

interface RulePerformance {
    ruleName: string;
    ruleType: string;
    avgScoreRate: number;
    triggerCount: number;
    appealCount: number;
    appealSuccessRate: number;
    severity: 'low' | 'medium' | 'high';
}

interface RuleTrendData {
    date: string;
    scoreRate: number;
    triggerCount: number;
}

interface TeamRanking {
    teamName: string;
    avgScore: number;
    memberCount: number;
    rank: number;
}

interface AgentRanking {
    agentName: string;
    teamName: string;
    avgScore: number;
    triggerCount: number;
    rank: number;
}

interface ProblemCase {
    recordId: string;
    agentName: string;
    teamName: string;
    score: number;
    callTime: string;
    duration: string;
    problemDescription: string;
}

interface PerformanceDistribution {
    scoreRange: string;
    count: number;
    percentage: number;
}

/**
 * 规则表现总览表格组件
 */
const RulePerformanceTable: React.FC<{
    data: RulePerformance[];
    onRuleClick: (rule: RulePerformance) => void;
    expandedRule: string | null;
}> = ({ data, onRuleClick, expandedRule }) => {
    const getSeverityBadge = (severity: 'low' | 'medium' | 'high') => {
        const styles = {
            low: 'bg-green-100 text-green-800',
            medium: 'bg-yellow-100 text-yellow-800',
            high: 'bg-red-100 text-red-800'
        };
        const labels = {
            low: '轻度违规',
            medium: '中度违规',
            high: '严重违规'
        };
        
        return (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[severity]}`}>
                {labels[severity]}
            </span>
        );
    };

    const getScoreRateColor = (rate: number) => {
        if (rate >= 90) return 'text-green-600';
        if (rate >= 80) return 'text-yellow-600';
        return 'text-red-600';
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ y: -2 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
        >
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                    <motion.div 
                        className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                        <BarChart3 className="w-6 h-6 text-white" />
                    </motion.div>
                    <div>
                        <h3 className="text-xl font-bold text-gray-900">规则表现总览</h3>
                        <p className="text-sm text-gray-500">深入分析各项质检规则的表现情况</p>
                    </div>
                </div>
                <div className="flex items-center gap-3">
                    <motion.button 
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 bg-gray-50/80 border border-gray-200/50 rounded-xl hover:bg-gray-100/80 hover:border-gray-300/50 transition-all duration-200 backdrop-blur-sm"
                    >
                        <Filter className="w-4 h-4" />
                        筛选
                    </motion.button>
                    <motion.button 
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 bg-gray-50/80 border border-gray-200/50 rounded-xl hover:bg-gray-100/80 hover:border-gray-300/50 transition-all duration-200 backdrop-blur-sm"
                    >
                        <ArrowUpDown className="w-4 h-4" />
                        排序
                    </motion.button>
                </div>
            </div>
            
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead>
                        <tr className="border-b border-gray-200">
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">规则名称</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">规则类型</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">平均得分率</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">触发次数</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">申诉次数</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">申诉成功率</th>
                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {data.map((rule, index) => (
                            <motion.tr 
                                key={index}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.05)' }}
                                className={`border-b border-gray-100/50 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-transparent transition-all duration-200 cursor-pointer ${
                                    expandedRule === rule.ruleName ? 'bg-gradient-to-r from-blue-100/70 to-blue-50/30 shadow-sm' : ''
                                }`}
                                onClick={() => onRuleClick(rule)}
                            >
                                <td className="py-4 px-4">
                                    <div className="flex items-center gap-3">
                                        <span className="text-sm font-semibold text-gray-900">{rule.ruleName}</span>
                                        <motion.div
                                            whileHover={{ scale: 1.1 }}
                                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                                        >
                                            {getSeverityBadge(rule.severity)}
                                        </motion.div>
                                    </div>
                                </td>
                                <td className="py-3 px-4 text-sm text-gray-700">{rule.ruleType}</td>
                                <td className="py-3 px-4">
                                    <span className={`text-sm font-medium ${getScoreRateColor(rule.avgScoreRate)}`}>
                                        {rule.avgScoreRate}%
                                    </span>
                                </td>
                                <td className="py-3 px-4 text-sm text-gray-700">{rule.triggerCount.toLocaleString()}</td>
                                <td className="py-3 px-4 text-sm text-gray-700">{rule.appealCount}</td>
                                <td className="py-3 px-4 text-sm text-gray-700">{rule.appealSuccessRate}%</td>
                                <td className="py-4 px-4">
                                    <div className="flex items-center gap-3">
                                        <motion.button 
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                            className="flex items-center gap-2 px-3 py-1.5 text-sm text-blue-600 bg-blue-50/50 rounded-lg hover:bg-blue-100/70 hover:text-blue-700 transition-all duration-200 backdrop-blur-sm"
                                        >
                                            <Eye className="w-4 h-4" />
                                            下钻分析
                                        </motion.button>
                                        <motion.div
                                            animate={{ rotate: expandedRule === rule.ruleName ? 180 : 0 }}
                                            transition={{ duration: 0.2 }}
                                        >
                                            <ChevronDown className="w-4 h-4 text-gray-400" />
                                        </motion.div>
                                    </div>
                                </td>
                            </motion.tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </motion.div>
    );
};

/**
 * 规则下钻分析组件
 */
const RuleDrillDownAnalysis: React.FC<{
    rule: RulePerformance;
    trendData: RuleTrendData[];
    teamRankings: TeamRanking[];
    agentRankings: AgentRanking[];
    problemCases: ProblemCase[];
}> = ({ rule, trendData, teamRankings, agentRankings, problemCases }) => {
    return (
        <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="bg-gradient-to-br from-slate-50/80 to-blue-50/30 border-t border-blue-200/50 p-8 backdrop-blur-sm"
        >
            <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="mb-8"
            >
                <div className="flex items-center gap-4 mb-3">
                    <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                        <Target className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-2xl font-bold text-gray-900">
                        规则详细分析：{rule.ruleName}
                    </h4>
                </div>
                <p className="text-gray-600 ml-14">
                    深入分析该规则的表现趋势、团队排名和典型问题案例
                </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* 规则得分趋势图 */}
                <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    whileHover={{ y: -4 }}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <motion.div 
                            className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                            <TrendingUp className="w-6 h-6 text-white" />
                        </motion.div>
                        <div>
                            <h5 className="text-lg font-bold text-gray-900">规则得分趋势</h5>
                            <p className="text-sm text-gray-500">观察得分率变化情况</p>
                        </div>
                    </div>
                    
                    <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                            <LineChart
                                data={trendData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                            >
                                <CartesianGrid
                                    strokeDasharray="3 3"
                                    stroke={chartTheme.grid.stroke}
                                    strokeWidth={chartTheme.grid.strokeWidth}
                                    opacity={0.6}
                                />
                                <XAxis
                                    dataKey="date"
                                    tick={chartTheme.axis.tick}
                                    tickLine={chartTheme.axis.tickLine}
                                    axisLine={chartTheme.axis.axisLine}
                                />
                                <YAxis
                                    tick={chartTheme.axis.tick}
                                    tickLine={chartTheme.axis.tickLine}
                                    axisLine={chartTheme.axis.axisLine}
                                    domain={[0, 100]}
                                    label={{
                                        value: '得分率(%)',
                                        angle: -90,
                                        position: 'insideLeft',
                                        style: chartTheme.axis.label
                                    }}
                                />
                                <Tooltip
                                    content={<EnhancedTooltip
                                        labelFormatter={(label: string) => `日期: ${label}`}
                                        valueFormatter={(value: any, name?: string) => {
                                            return `${value}%`;
                                        }}
                                    />}
                                    cursor={{
                                        stroke: chartTheme.tooltip.cursor.stroke,
                                        strokeWidth: chartTheme.tooltip.cursor.strokeWidth,
                                        strokeDasharray: chartTheme.tooltip.cursor.strokeDasharray
                                    }}
                                />
                                <Line
                                    type="monotone"
                                    dataKey="scoreRate"
                                    name="得分率"
                                    stroke={chartColors.secondary.emerald}
                                    strokeWidth={chartTypeConfigs.line.strokeWidth}
                                    dot={{
                                        ...chartTypeConfigs.line.dot,
                                        fill: chartColors.secondary.emerald
                                    }}
                                    activeDot={{
                                        ...chartTypeConfigs.line.activeDot,
                                        stroke: chartColors.secondary.emerald
                                    }}
                                />
                            </LineChart>
                        </ResponsiveContainer>
                    </div>
                </motion.div>

                {/* 触发次数趋势图 */}
                <motion.div 
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    whileHover={{ y: -4 }}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <motion.div 
                            className="p-3 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl shadow-lg"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                            <Activity className="w-6 h-6 text-white" />
                        </motion.div>
                        <div>
                            <h5 className="text-lg font-bold text-gray-900">触发次数趋势</h5>
                            <p className="text-sm text-gray-500">监控问题发生频率</p>
                        </div>
                    </div>
                    
                    <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                                data={trendData}
                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                            >
                                <defs>
                                    <linearGradient id="orangeGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="0%" stopColor="#f97316" stopOpacity={0.9} />
                                        <stop offset="100%" stopColor="#ea580c" stopOpacity={0.7} />
                                    </linearGradient>
                                </defs>
                                <CartesianGrid
                                    strokeDasharray="3 3"
                                    stroke={chartTheme.grid.stroke}
                                    strokeWidth={chartTheme.grid.strokeWidth}
                                    opacity={0.6}
                                />
                                <XAxis
                                    dataKey="date"
                                    tick={chartTheme.axis.tick}
                                    tickLine={chartTheme.axis.tickLine}
                                    axisLine={chartTheme.axis.axisLine}
                                />
                                <YAxis
                                    tick={chartTheme.axis.tick}
                                    tickLine={chartTheme.axis.tickLine}
                                    axisLine={chartTheme.axis.axisLine}
                                    label={{
                                        value: '触发次数',
                                        angle: -90,
                                        position: 'insideLeft',
                                        style: chartTheme.axis.label
                                    }}
                                />
                                <Tooltip
                                    content={<EnhancedTooltip
                                        labelFormatter={(label: string) => `日期: ${label}`}
                                        valueFormatter={(value: any, name?: string) => {
                                            return `${value}次`;
                                        }}
                                    />}
                                    cursor={{
                                        stroke: chartTheme.tooltip.cursor.stroke,
                                        strokeWidth: chartTheme.tooltip.cursor.strokeWidth,
                                        strokeDasharray: chartTheme.tooltip.cursor.strokeDasharray
                                    }}
                                />
                                <Bar
                                    dataKey="triggerCount"
                                    name="触发次数"
                                    fill="url(#orangeGradient)"
                                    radius={chartTypeConfigs.bar.radius as [number, number, number, number]}
                                    maxBarSize={chartTypeConfigs.bar.maxBarSize}
                                />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </motion.div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                {/* 团队表现排名 */}
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    whileHover={{ y: -4 }}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <motion.div 
                            className="p-3 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl shadow-lg"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                            <Users className="w-6 h-6 text-white" />
                        </motion.div>
                        <div>
                            <h5 className="text-lg font-bold text-gray-900">团队表现排名</h5>
                            <p className="text-sm text-gray-500">各团队综合表现</p>
                        </div>
                    </div>
                    
                    <div className="space-y-4">
                        {teamRankings.map((team, index) => (
                            <motion.div 
                                key={index} 
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.5 + index * 0.1 }}
                                whileHover={{ x: 4, scale: 1.02 }}
                                className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/80 to-purple-50/30 rounded-xl border border-gray-200/50 hover:shadow-md transition-all duration-200"
                            >
                                <div className="flex items-center gap-4">
                                    <motion.div 
                                        className={`w-8 h-8 rounded-xl flex items-center justify-center text-sm font-bold shadow-lg ${
                                            team.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white' :
                                            team.rank === 2 ? 'bg-gradient-to-br from-gray-400 to-gray-500 text-white' :
                                            team.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500 text-white' :
                                            'bg-gradient-to-br from-purple-400 to-purple-500 text-white'
                                        }`}
                                        whileHover={{ scale: 1.1, rotate: 5 }}
                                    >
                                        {team.rank}
                                    </motion.div>
                                    <div>
                                        <div className="text-sm font-semibold text-gray-900">{team.teamName}</div>
                                        <div className="text-xs text-gray-500">{team.memberCount}人团队</div>
                                    </div>
                                </div>
                                <div className="text-lg font-bold text-gray-900">{team.avgScore}%</div>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>

                {/* 坐席表现排名 */}
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    whileHover={{ y: -4 }}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <motion.div 
                            className="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl shadow-lg"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                            <UserCheck className="w-6 h-6 text-white" />
                        </motion.div>
                        <div>
                            <h5 className="text-lg font-bold text-gray-900">坐席表现排名</h5>
                            <p className="text-sm text-gray-500">个人表现TOP5</p>
                        </div>
                    </div>
                    
                    <div className="space-y-4">
                        {agentRankings.slice(0, 5).map((agent, index) => (
                            <motion.div 
                                key={index} 
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.6 + index * 0.1 }}
                                whileHover={{ x: 4, scale: 1.02 }}
                                className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/80 to-red-50/30 rounded-xl border border-gray-200/50 hover:shadow-md transition-all duration-200"
                            >
                                <div className="flex items-center gap-4">
                                    <motion.div 
                                        className={`w-8 h-8 rounded-xl flex items-center justify-center text-sm font-bold shadow-lg ${
                                            agent.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-white' :
                                            agent.rank === 2 ? 'bg-gradient-to-br from-gray-400 to-gray-500 text-white' :
                                            agent.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500 text-white' :
                                            'bg-gradient-to-br from-red-400 to-red-500 text-white'
                                        }`}
                                        whileHover={{ scale: 1.1, rotate: 5 }}
                                    >
                                        {agent.rank}
                                    </motion.div>
                                    <div>
                                        <div className="text-sm font-semibold text-gray-900">{agent.agentName}</div>
                                        <div className="text-xs text-gray-500">{agent.teamName}</div>
                                    </div>
                                </div>
                                <div className="text-lg font-bold text-gray-900">{agent.avgScore}%</div>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>

                {/* 典型问题录音 */}
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    whileHover={{ y: -4 }}
                    className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-6 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
                >
                    <div className="flex items-center gap-3 mb-6">
                        <motion.div 
                            className="p-3 bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl shadow-lg"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                            <AlertTriangle className="w-6 h-6 text-white" />
                        </motion.div>
                        <div>
                            <h5 className="text-lg font-bold text-gray-900">典型问题录音</h5>
                            <p className="text-sm text-gray-500">问题案例分析</p>
                        </div>
                    </div>
                    
                    <div className="space-y-4">
                        {problemCases.slice(0, 3).map((case_, index) => (
                            <motion.div 
                                key={index} 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7 + index * 0.1 }}
                                whileHover={{ scale: 1.02 }}
                                className="p-4 bg-gradient-to-r from-gray-50/80 to-pink-50/30 rounded-xl border border-gray-200/50 hover:shadow-md transition-all duration-200"
                            >
                                <div className="flex items-center justify-between mb-3">
                                    <span className="text-sm font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded-lg">{case_.recordId}</span>
                                    <span className="text-sm font-bold text-red-600 bg-red-50 px-2 py-1 rounded-lg">{case_.score}分</span>
                                </div>
                                <div className="text-sm text-gray-600 mb-3 flex items-center gap-2">
                                    <span className="font-medium">{case_.agentName}</span>
                                    <span className="text-gray-400">·</span>
                                    <span>{case_.teamName}</span>
                                    <span className="text-gray-400">·</span>
                                    <span>{case_.duration}</span>
                                </div>
                                <div className="text-sm text-gray-700 mb-3 leading-relaxed">{case_.problemDescription}</div>
                                <motion.button 
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg transition-all duration-200 font-medium"
                                >
                                    <Play className="w-4 h-4" />
                                    播放录音
                                </motion.button>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>
            </div>
        </motion.div>
    );
};

/**
 * 成员表现分布图组件
 */
const PerformanceDistributionChart: React.FC<{
    data: PerformanceDistribution[];
}> = ({ data }) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            whileHover={{ y: -4 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200/60 p-8 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300"
        >
            <motion.div 
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="flex items-center gap-4 mb-8"
            >
                <motion.div 
                    className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                    <BarChart3 className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                    <h3 className="text-2xl font-bold text-gray-900">成员表现分布图</h3>
                    <p className="text-gray-600">团队成员得分分布情况</p>
                </div>
            </motion.div>
            
            <motion.div 
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 }}
                className="h-80 bg-gradient-to-br from-gray-50/50 to-indigo-50/30 rounded-xl p-4"
            >
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={data}
                        margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    >
                        <CartesianGrid
                            strokeDasharray="3 3"
                            stroke={chartTheme.grid.stroke}
                            strokeWidth={chartTheme.grid.strokeWidth}
                            opacity={0.6}
                        />
                        <XAxis
                            dataKey="scoreRange"
                            tick={chartTheme.axis.tick}
                            tickLine={chartTheme.axis.tickLine}
                            axisLine={chartTheme.axis.axisLine}
                        />
                        <YAxis
                            tick={chartTheme.axis.tick}
                            tickLine={chartTheme.axis.tickLine}
                            axisLine={chartTheme.axis.axisLine}
                            label={{
                                value: '人数',
                                angle: -90,
                                position: 'insideLeft',
                                style: chartTheme.axis.label
                            }}
                        />
                        <Tooltip
                            content={<EnhancedTooltip
                                labelFormatter={(label: string) => `分数区间: ${label}`}
                                valueFormatter={(value: any, name?: string) => {
                                    const item = data.find(d => d.count === value);
                                    return `${value}人 (${item?.percentage}%)`;
                                }}
                            />}
                            cursor={{
                                stroke: chartTheme.tooltip.cursor.stroke,
                                strokeWidth: chartTheme.tooltip.cursor.strokeWidth,
                                strokeDasharray: chartTheme.tooltip.cursor.strokeDasharray
                            }}
                        />
                        <Bar
                            dataKey="count"
                            name="人数"
                            radius={chartTypeConfigs.bar.radius as [number, number, number, number]}
                            maxBarSize={chartTypeConfigs.bar.maxBarSize}
                        >
                            {data.map((entry, index) => (
                                <Cell
                                    key={`cell-${index}`}
                                    fill={
                                        entry.scoreRange === '90-100' ? chartColors.status.success :
                                        entry.scoreRange === '80-89' ? chartColors.primary.blue :
                                        entry.scoreRange === '70-79' ? chartColors.accent.amber :
                                        entry.scoreRange === '60-69' ? chartColors.status.warning :
                                        chartColors.neutral.slate
                                    }
                                />
                            ))}
                        </Bar>
                    </BarChart>
                </ResponsiveContainer>
            </motion.div>
            
            <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="mt-6 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm"
            >
                <motion.div 
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200/50 hover:shadow-md transition-all duration-200"
                >
                    <div
                        className="w-4 h-4 rounded-full shadow-sm"
                        style={{ backgroundColor: chartColors.status.success }}
                    ></div>
                    <span className="font-medium text-green-700">优秀 (90-100分)</span>
                </motion.div>
                <motion.div 
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200/50 hover:shadow-md transition-all duration-200"
                >
                    <div
                        className="w-4 h-4 rounded-full shadow-sm"
                        style={{ backgroundColor: chartColors.primary.blue }}
                    ></div>
                    <span className="font-medium text-blue-700">良好 (80-89分)</span>
                </motion.div>
                <motion.div 
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200/50 hover:shadow-md transition-all duration-200"
                >
                    <div
                        className="w-4 h-4 rounded-full shadow-sm"
                        style={{ backgroundColor: chartColors.accent.amber }}
                    ></div>
                    <span className="font-medium text-yellow-700">一般 (70-79分)</span>
                </motion.div>
                <motion.div 
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200/50 hover:shadow-md transition-all duration-200"
                >
                    <div
                        className="w-4 h-4 rounded-full shadow-sm"
                        style={{ backgroundColor: chartColors.status.warning }}
                    ></div>
                    <span className="font-medium text-orange-700">较差 (60-69分)</span>
                </motion.div>
                <motion.div 
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200/50 hover:shadow-md transition-all duration-200"
                >
                    <div
                        className="w-4 h-4 rounded-full shadow-sm"
                        style={{ backgroundColor: chartColors.neutral.slate }}
                    ></div>
                    <span className="font-medium text-gray-700">差 (60分以下)</span>
                </motion.div>
            </motion.div>
        </motion.div>
    );
};

/**
 * 服务质量深度分析页面
 */
export const FinalServiceQualityDeepAnalysisPage: React.FC = () => {
    const [timeRange, setTimeRange] = useState('本月');
    const [selectedScheme, setSelectedScheme] = useState('全部方案');
    const [selectedTeam, setSelectedTeam] = useState('全部班组');
    const [selectedAgent, setSelectedAgent] = useState('全部坐席');
    const [expandedRule, setExpandedRule] = useState<string | null>(null);

    // 服务质量深度分析页面设计说明 (业务角度)
    const designGuideContent = (
        <div className="space-y-4 text-gray-700 text-sm leading-relaxed">
            <h2 className="text-xl font-bold text-gray-800">页面：服务质量深度分析 (FinalServiceQualityDeepAnalysisPage.tsx) - 业务设计说明</h2>

            <h3 className="text-lg font-semibold text-gray-700 mt-4">1. 核心定位与目标</h3>
            <p>
                服务质量深度分析页面是一个<b>专题分析报表</b>，其核心目标是让管理者能够
                <b>围绕任意一个具体的"质检规则"</b>进行深度下钻分析。它旨在回答"某个特定的服务质量问题（例如'客户不满未道歉'）
                在我们的组织中表现如何？根源在哪里？"这类具体问题，为精准的培训和辅导提供直接依据。
            </p>

            <h3 className="text-lg font-semibold text-gray-700 mt-4">2. 主要功能模块与内容</h3>

            <h4 className="text-md font-semibold text-gray-700 mt-3">a. 页面顶部区域</h4>
            <p>此区域提供页面的核心概览信息和全局操作入口：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>标题</b>: "服务质量深度分析"，明确页面的主题。</li>
                <li><b>副标题</b>: "下钻到具体的质检规则维度，分析服务质量的短板，并定位到具体的团队和个人，为精准培训和辅导提供依据"，阐述页面提供的核心业务价值。</li>
                <li><b>角色标识</b>: 使用靶心图标 (<code className="bg-gray-100 px-1 py-0.5 rounded text-red-500">Target</code>)，象征着精准定位和深度分析的功能属性。</li>
                <li><b>核心操作</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>刷新数据</b>: 允许管理者获取最新的分析数据，确保信息的实时性。</li>
                        <li><b>导出报表</b>: 提供将当前报表数据导出为可离线分析的格式，方便后续深入分析或汇报。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">b. 统一搜索筛选器</h4>
            <p>此模块提供灵活的数据筛选功能，允许管理者聚焦特定业务维度进行分析：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>目的</b>: 与质检运营总览页面类似，用于定义深度分析的数据范围。</li>
                <li><b>筛选字段及业务含义</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>时间范围</b>: 定义报表的统计周期，便于观察问题在不同时间段的表现。</li>
                        <li><b>质检方案</b>: 筛选特定质检方案下的数据，评估该方案对服务质量的影响。</li>
                        <li><b>班组/团队</b>: 允许下钻到特定团队，分析其在该规则下的表现。</li>
                        <li><b>坐席</b>: 更进一步，直接定位到特定坐席在该规则下的表现。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">c. 规则表现总览表格</h4>
            <p>这是页面的<b>起点和入口</b>，以表格形式汇总了在筛选范围内所有被触发过的质检规则，并展示了每条规则的综合表现。表格列及其业务含义和计算方式如下：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>规则名称</b>: 具体的质检规则，附带其"严重程度"标识（如轻度、中度、严重违规），用于快速识别风险等级。</li>
                <li><b>规则类型</b>: 质检规则的业务分类（如服务规范、合规风险），便于归类分析。</li>
                <li><b>平均得分率</b>:
                    <p>业务含义：<b>核心指标</b>，指所有与该规则相关的通话，在该规则项上的平均得分率。例如，如果某规则满分为-5分，实际扣了-1分，则得分率为80%。此指标比简单的触发次数更能反映问题的严重程度和实际影响。</p>
                    <p>计算/实现：<code>(该规则项的实际得分 / 该规则项的满分) × 100%</code>，对所有相关通话进行平均。</p>
                </li>
                <li><b>触发次数</b>:
                    <p>业务含义：该规则被命中的总次数，反映问题的发生频率。</p>
                    <p>计算/实现：系统根据质检结果统计该规则被判定的总次数。</p>
                </li>
                <li><b>申诉次数</b>:
                    <p>业务含义：针对该规则的申诉数量，反映该规则的争议性或AI判断的准确性。</p>
                    <p>计算/实现：统计用户针对该规则提交的申诉请求总数。</p>
                </li>
                <li><b>申诉成功率</b>:
                    <p>业务含义：针对该规则的申诉中，成功（被认可）的比例。高成功率可能意味着规则本身设置不合理，或AI自动质检的判断存在偏差，需要进一步复核或优化规则。</p>
                    <p>计算/实现：<code>(针对该规则的申诉成功次数 / 针对该规则的总申诉次数) × 100%</code>。</p>
                </li>
                <li><b>操作</b>: 提供一个<b>"下钻分析"</b>按钮。用户选择感兴趣的规则后点击此按钮，下方会动态展开该规则的详细分析区域。</li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">d. 规则下钻分析区域（动态展开）</h4>
            <p>这是页面的核心内容，仅在用户从"规则表现总览"表格中选择并点击某条规则后才会动态显示。此区域包含针对<b>所选定规则</b>的深入分析图表和列表：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>趋势分析（左右双图表布局）</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>规则得分趋势</b>: 展示在选定时间范围内，该规则的<b>每日/周平均得分率</b>的变化趋势。用于判断该特定服务质量问题是持续存在、正在改善还是正在恶化。数据来源于系统每日/每周对该规则得分的汇总。</li>
                        <li><b>触发次数趋势</b>: 展示该规则的<b>每日/周触发次数</b>的变化趋势。可以与得分率趋势结合分析，例如，触发次数下降但得分率没有相应提升，可能说明问题仍未根本解决，只是发生的频率降低了。数据来源于系统每日/每周对该规则触发次数的统计。</li>
                    </ul>
                </li>
                <li><b>表现排名（左右双列表布局）</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>团队表现排名</b>: 按照该规则的平均得分率对团队进行排名。这有助于快速定位哪个团队在该问题上表现最佳（可作为标杆进行经验推广），哪个团队表现最差（需要重点进行培训和辅导）。数据来源于各团队在该规则项下的平均得分聚合。</li>
                        <li><b>坐席表现排名</b>: 按照该规则的平均得分率对单个坐席进行排名。此排名直接定位到具体的个人，便于进行一对一的精准辅导。数据来源于各坐席在该规则项下的平均得分聚合。</li>
                    </ul>
                </li>
                <li><b>典型问题录音</b>:
                    <ul className="list-[circle] pl-6 space-y-1">
                        <li><b>功能</b>: 系统会自动筛选出几条因为该规则而导致严重扣分的<b>典型通话案例</b>。</li>
                        <li><b>内容与业务价值</b>: 每条案例会显示通话记录ID、坐席名称、所属团队、最终得分、通话时长以及最关键的<b>问题描述</b>（例如，"客户在3分15秒表达不满，坐席未道歉"）。管理者可以通过"播放录音"按钮，身临其境地回溯问题发生的具体场景，从而更直观地理解问题，为培训和辅导提供具象化的素材。数据来源于质检结果中被该规则严重扣分的通话记录。</li>
                    </ul>
                </li>
            </ul>

            <h4 className="text-md font-semibold text-gray-700 mt-3">e. 成员表现分布图</h4>
            <p>这是一个独立的、辅助性的分析图表，通常位于页面底部，旨在提供团队整体绩效的宏观视图：</p>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>功能</b>: 以柱状图形式，展示在筛选范围内所有成员（坐席）的最终质检得分分布情况。例如，可以清晰地看到有多少人得分在90-100分（优秀），有多少人得分在80-89分（良好），以此类推。</li>
                <li><b>业务价值与实现</b>: 帮助管理者了解团队整体表现是呈"纺锤形"（大部分人表现居中，符合正态分布）还是"哑铃型"（两极分化严重，即优秀和很差的人都多，中间较少）。这种分布图对于评估团队整体培训效果、识别潜在的普遍性问题或个体差异提供了数据支持。数据来源于所有坐席的最终质检得分汇总及按分数段的计数。</li>
            </ul>

            <h3 className="text-lg font-semibold text-gray-700 mt-4">3. 核心交互与操作</h3>
            <ul className="list-disc pl-6 space-y-1">
                <li><b>逐层下钻</b>: 页面的核心交互逻辑是<b>"总览 -&gt; 选择 -&gt; 下钻"</b>。用户首先从规则总览表格中发现潜在问题线索（如平均得分率低的规则），然后点击进入针对该问题的深度分析区域。在这个区域内，可以进一步从团队排名下钻到个人表现，并最终通过典型案例功能触达问题的原始通话场景，实现全链路的深入分析。</li>
                <li><b>问题精准定位</b>: 整个页面的设计都是为了帮助管理者<b>精准定位服务质量问题</b>。它不仅回答了"是什么问题"（通过规则名称和类型），还揭示了"问题有多严重"（通过平均得分率和趋势图），以及"问题出在谁身上"（通过团队和个人排名）。</li>
                <li><b>场景还原与培训赋能</b>: "典型问题录音"功能是本页面的点睛之笔，它将抽象的质检数据与具体的客户服务场景紧密联系起来。管理者可以通过聆听真实的通话录音，身临其境地理解问题细节，这极大地增强了分析的深度和培训辅导的针对性与说服力。</li>
            </ul>
        </div>
    );

    // 模拟规则表现数据
    const rulePerformanceData: RulePerformance[] = useMemo(() => [
        {
            ruleName: '客户不满未道歉',
            ruleType: '服务规范',
            avgScoreRate: 78.5,
            triggerCount: 1234,
            appealCount: 45,
            appealSuccessRate: 24.4,
            severity: 'high'
        },
        {
            ruleName: '未主动核身',
            ruleType: '合规风险',
            avgScoreRate: 85.2,
            triggerCount: 892,
            appealCount: 23,
            appealSuccessRate: 17.4,
            severity: 'high'
        },
        {
            ruleName: '通话静音超时',
            ruleType: '服务质量',
            avgScoreRate: 92.1,
            triggerCount: 567,
            appealCount: 12,
            appealSuccessRate: 8.3,
            severity: 'medium'
        },
        {
            ruleName: '语速过快',
            ruleType: '服务规范',
            avgScoreRate: 88.7,
            triggerCount: 445,
            appealCount: 8,
            appealSuccessRate: 12.5,
            severity: 'low'
        },
        {
            ruleName: '未确认客户需求',
            ruleType: '服务质量',
            avgScoreRate: 81.3,
            triggerCount: 723,
            appealCount: 31,
            appealSuccessRate: 19.4,
            severity: 'medium'
        },
        {
            ruleName: '敏感词汇使用',
            ruleType: '合规风险',
            avgScoreRate: 94.8,
            triggerCount: 156,
            appealCount: 3,
            appealSuccessRate: 0,
            severity: 'high'
        }
    ], []);

    // 模拟规则趋势数据
    const ruleTrendData: RuleTrendData[] = useMemo(() => [
        { date: '12-28', scoreRate: 75.2, triggerCount: 45 },
        { date: '12-29', scoreRate: 76.8, triggerCount: 52 },
        { date: '12-30', scoreRate: 78.1, triggerCount: 48 },
        { date: '12-31', scoreRate: 77.5, triggerCount: 41 },
        { date: '01-01', scoreRate: 79.2, triggerCount: 38 },
        { date: '01-02', scoreRate: 78.9, triggerCount: 43 },
        { date: '01-03', scoreRate: 80.1, triggerCount: 39 }
    ], []);

    // 模拟团队排名数据
    const teamRankings: TeamRanking[] = useMemo(() => [
        { teamName: 'A班组', avgScore: 85.6, memberCount: 12, rank: 1 },
        { teamName: 'B班组', avgScore: 82.3, memberCount: 15, rank: 2 },
        { teamName: 'C班组', avgScore: 79.8, memberCount: 11, rank: 3 },
        { teamName: 'D班组', avgScore: 77.1, memberCount: 13, rank: 4 },
        { teamName: 'E班组', avgScore: 74.5, memberCount: 14, rank: 5 }
    ], []);

    // 模拟坐席排名数据
    const agentRankings: AgentRanking[] = useMemo(() => [
        { agentName: '张三', teamName: 'A班组', avgScore: 92.5, triggerCount: 2, rank: 1 },
        { agentName: '李四', teamName: 'B班组', avgScore: 89.7, triggerCount: 3, rank: 2 },
        { agentName: '王五', teamName: 'A班组', avgScore: 87.2, triggerCount: 4, rank: 3 },
        { agentName: '赵六', teamName: 'C班组', avgScore: 84.8, triggerCount: 6, rank: 4 },
        { agentName: '钱七', teamName: 'B班组', avgScore: 82.1, triggerCount: 8, rank: 5 }
    ], []);

    // 模拟典型问题案例数据
    const problemCases: ProblemCase[] = useMemo(() => [
        {
            recordId: 'QM2507020089',
            agentName: '孙八',
            teamName: 'D班组',
            score: 45,
            callTime: '2025-01-03 14:30',
            duration: '8分32秒',
            problemDescription: '客户多次表达不满，坐席未进行道歉'
        },
        {
            recordId: 'QM2507020078',
            agentName: '周九',
            teamName: 'E班组',
            score: 52,
            callTime: '2025-01-03 13:45',
            duration: '6分18秒',
            problemDescription: '客户情绪激动，坐席态度冷淡'
        },
        {
            recordId: 'QM2507020067',
            agentName: '吴十',
            teamName: 'D班组',
            score: 58,
            callTime: '2025-01-03 11:20',
            duration: '12分05秒',
            problemDescription: '多次出现客户不满情绪，未及时安抚'
        }
    ], []);

    // 模拟成员表现分布数据
    const performanceDistributionData: PerformanceDistribution[] = useMemo(() => [
        { scoreRange: '90-100', count: 23, percentage: 15.3 },
        { scoreRange: '80-89', count: 45, percentage: 30.0 },
        { scoreRange: '70-79', count: 52, percentage: 34.7 },
        { scoreRange: '60-69', count: 24, percentage: 16.0 },
        { scoreRange: '&lt;60', count: 6, percentage: 4.0 }
    ], []);

    const handleRuleClick = (rule: RulePerformance) => {
        setExpandedRule(expandedRule === rule.ruleName ? null : rule.ruleName);
    };

    // Filter fields configuration
    const filterFields = [
        {
            key: 'timeRange',
            label: '时间范围',
            type: 'select' as const,
            value: timeRange,
            options: [
                { value: '今日', label: '今日' },
                { value: '本周', label: '本周' },
                { value: '本月', label: '本月' },
                { value: '自定义', label: '自定义' }
            ],
            onChange: setTimeRange
        },
        {
            key: 'selectedScheme',
            label: '质检方案',
            type: 'select' as const,
            value: selectedScheme,
            options: [
                { value: '全部方案', label: '全部方案' },
                { value: '金融合规质检方案', label: '金融合规质检方案' },
                { value: '客服标准质检方案', label: '客服标准质检方案' },
                { value: '销售质检方案', label: '销售质检方案' }
            ],
            onChange: setSelectedScheme
        },
        {
            key: 'selectedTeam',
            label: '班组/团队',
            type: 'select' as const,
            value: selectedTeam,
            options: [
                { value: '全部班组', label: '全部班组' },
                { value: 'A班组', label: 'A班组' },
                { value: 'B班组', label: 'B班组' },
                { value: 'C班组', label: 'C班组' },
                { value: 'D班组', label: 'D班组' }
            ],
            onChange: setSelectedTeam
        }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
            <UnifiedPageHeader
                title="服务质量深度分析"
                subtitle="下钻到具体的质检规则维度，分析服务质量的短板，并定位到具体的团队和个人，为精准培训和辅导提供依据"
                icon={Target}
                badge={{ text: "深度分析", color: "purple" }}
                actions={[
                    {
                        label: "刷新数据",
                        icon: RefreshCw,
                        variant: "secondary",
                        onClick: () => console.log('刷新数据')
                    },
                    {
                        label: "导出报表",
                        icon: Download,
                        variant: "primary",
                        onClick: () => console.log('导出报表')
                    }
                ]}
                showDesignGuide={true}
                designGuideContent={designGuideContent}
            />

            <main className="p-6 space-y-8">
                {/* Enhanced Filter Section */}
                <EnhancedFilterSection
                    fields={filterFields}
                    onSearch={() => console.log('搜索')}
                    onReset={() => {
                        setTimeRange('本月');
                        setSelectedScheme('全部方案');
                        setSelectedTeam('全部班组');
                        setSelectedAgent('全部坐席');
                    }}
                />

                {/* 规则表现总览 */}
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="mb-8"
                >
                    <RulePerformanceTable 
                        data={rulePerformanceData}
                        onRuleClick={handleRuleClick}
                        expandedRule={expandedRule}
                    />
                    
                    {/* 规则下钻分析 */}
                    <AnimatePresence>
                        {expandedRule && (
                            <RuleDrillDownAnalysis
                                rule={rulePerformanceData.find(r => r.ruleName === expandedRule)!}
                                trendData={ruleTrendData}
                                teamRankings={teamRankings}
                                agentRankings={agentRankings}
                                problemCases={problemCases}
                            />
                        )}
                    </AnimatePresence>
                </motion.div>

                {/* 成员表现分布图 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                >
                    <PerformanceDistributionChart data={performanceDistributionData} />
                </motion.div>
            </main>
        </div>
    );
};

export default FinalServiceQualityDeepAnalysisPage;