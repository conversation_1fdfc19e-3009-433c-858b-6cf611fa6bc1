import React from 'react';

/**
 * 上下文重复检查使用提示组件
 * 提供使用技巧和最佳实践指导
 */
const ContextRepeatTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">相似度设置</span>
                    <span className="text-xs text-gray-500 block">0.8以上为高相似度，0.5-0.8为中等，0.5以下为低相似度</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">检测窗口</span>
                    <span className="text-xs text-gray-500 block">建议设置2-3句，过大会影响性能，过小可能遗漏</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">短句过滤</span>
                    <span className="text-xs text-gray-500 block">建议设置2-3个字，避免"嗯"、"好的"等无意义重复</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 高级功能提示 */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">高级功能</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">例外句子设置</span>
                    <span className="text-xs text-gray-500 block">为常见的标准回复设置例外，如"您好"、"谢谢"等</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">违规次数控制</span>
                    <span className="text-xs text-gray-500 block">第2次重复算违规较为合理，第1次过于严格</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">角色分别检测</span>
                    <span className="text-xs text-gray-500 block">客服和客户分别设置，针对性检测重复问题</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">客服重复检测</span>
                    <span className="text-xs text-gray-500 block">检测客服是否过度使用相同回复，影响服务质量</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">客户投诉监控</span>
                    <span className="text-xs text-gray-500 block">检测客户是否重复表达不满，及时关注情绪</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">效率分析</span>
                    <span className="text-xs text-gray-500 block">分析对话效率，减少无效的重复沟通</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 参数调优建议 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">参数调优建议</h4>
              <div className="space-y-3">
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">相似度阈值</h5>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div className="flex justify-between">
                      <span>• 严格模式：0.9以上</span>
                      <span className="text-orange-600">适合精确重复检测</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• 标准模式：0.7-0.8</span>
                      <span className="text-orange-600">推荐日常使用</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• 宽松模式：0.5-0.6</span>
                      <span className="text-orange-600">检测语义相似</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">检测窗口大小</h5>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div className="flex justify-between">
                      <span>• 2句内：快速重复</span>
                      <span className="text-orange-600">连续重复检测</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• 3-5句内：中等范围</span>
                      <span className="text-orange-600">平衡效果和性能</span>
                    </div>
                    <div className="flex justify-between">
                      <span>• 5句以上：长范围</span>
                      <span className="text-orange-600">全面但可能过度</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-200">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <span className="text-gray-600 text-sm">❓</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">常见问题</h4>
              <div className="space-y-3">
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Q: 为什么"您好"总是被检测为重复？</h5>
                  <p className="text-xs text-gray-600">A: 将常见的礼貌用语添加到例外句子中，或者提高最小字数阈值。</p>
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Q: 相似度计算不准确怎么办？</h5>
                  <p className="text-xs text-gray-600">A: 调整相似度阈值，或者使用文本相似度算子进行更精确的检测。</p>
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Q: 检测结果太多或太少？</h5>
                  <p className="text-xs text-gray-600">A: 调整检测窗口大小和相似度阈值，找到适合业务场景的平衡点。</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速开始 */}
        <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-lg p-4 border border-emerald-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
              <span className="text-emerald-600 text-sm">🚀</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <span className="bg-emerald-200 text-emerald-800 px-2 py-1 rounded">步骤1</span>
                <span>选择检测角色</span>
                <span>→</span>
                <span className="bg-emerald-200 text-emerald-800 px-2 py-1 rounded">步骤2</span>
                <span>设置检测参数</span>
                <span>→</span>
                <span className="bg-emerald-200 text-emerald-800 px-2 py-1 rounded">步骤3</span>
                <span>执行测试验证</span>
              </div>
            </div>
          </div>
        </div>

        {/* 算法说明 */}
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
              <span className="text-indigo-600 text-sm">🧮</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">算法说明</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <p>• <strong>编辑距离算法</strong>：计算两个字符串的最小编辑操作数</p>
                <p>• <strong>相似度归一化</strong>：相似度 = 1 - (编辑距离 / 最大长度)</p>
                <p>• <strong>文本预处理</strong>：去除标点符号，只保留核心文字内容</p>
                <p>• <strong>智能过滤</strong>：自动过滤过短的句子，避免误判</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextRepeatTips; 