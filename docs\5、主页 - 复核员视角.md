
---

### 页面三：复核员视角 (FinalReviewerHomePage.tsx)

#### 1. 核心定位与目标
该页面是**质检复核员**登录后的个人工作台（Dashboard）。其核心目标是帮助复核员清晰地了解自己的**工作负荷、效率和质量**，并提供快速进入待办任务列表的入口。它强调的是对个人工作状态的监控，而非对他人的绩效评估。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "复核员视角"
*   **副标题**: 显示复核员的个人信息，例如：“欢迎回来，李复核 | 质检部-复核组”。
*   **图标**: `UserCheck` (带对勾的用户图标)，象征着审核与确认。
*   **徽章**: 显示复核员的关键状态，如“在线”。

**b. 核心工作指标 (`MetricCard` 网格布局)**
这部分展示的是衡量复核员工作量和核心价值的KPI：
*   **复核总量**: 展示复核员在周期内完成的复核任务总数，反映总体工作量。
*   **待复核量**: 显示当前分配给该复核员但尚未完成的任务数，这是最重要的待办事项指标。
*   **日均复核量**: 反映复核员的平均工作效率。
*   **AI结果纠错数**: 这是一个非常关键的指标，展示了复核员修正AI初检结果的次数。它不仅体现了人工复核的价值，也侧面反映了AI在某些场景下的不足，这些数据是优化AI模型的重要输入。

> 卡片同样包含趋势指示和指标说明。

**c. 工作量趋势分析 (折线图)**
*   **图表标题**: "工作量趋势"
*   **内容**: 使用一个双折线图（Dual-Line Chart），在一个图表中同时展示两条趋势线：
    1.  **完成复核数**: 蓝色线条，展示每日完成的复核任务数量，反映工作量的波动。
    2.  **纠错数量**: 红色线条，展示每日修正的AI结果数量，可以与工作量进行对比，分析复核的难度和价值。
*   **交互**: 鼠标悬浮时，Tooltip会同时显示当天的“完成复核数”和“纠错数量”，便于分析两者关系。

**d. 通知公告 (卡片布局)**
*   **标题**: "通知公告"
*   **内容**: 聚合了与复核员工作直接相关的信息，例如：
    *   **紧急任务提醒**: 如有高优先级的复核任务（例如涉及严重客诉或合规风险），会在此处突出显示。
    *   **任务分配通知**: 系统分配新任务或周/月度任务指标的通知。
    *   **系统公告与标准更新**: 如系统维护、质检标准或规则库变更等，确保复核员的标准与系统保持一致。
*   **特点**: 同样采用优先级视觉提示，帮助复核员快速识别重要信息。

**e. 待复核任务列表 (卡片布局)**
*   **标题**: "待复核任务"
*   **内容**: 这是复核员的**快速待办列表 (Quick To-Do List)**，展示了最新或最高优先级的几条待复核任务。每条任务通常包含：
    *   **记录ID**: 唯一标识。
    *   **被检坐席**: 姓名和头像。
    *   **通话信息**: 如通话时间、时长。
    *   **AI初检分数**: 让复核员在进入前有一个初步判断。
    *   **质检类型/触发原因**: 告知该任务为何需要复核（如：低分触发、随机抽检），帮助复核员带着侧重点进入。
*   **交互**: 每条任务的末尾都有一个醒目的 **“开始复核”** 按钮，点击后直接跳转到 **[多模式会话详情页]** (`FinalMultiModeSessionDetailPage`，复核模式) 开始工作。

#### 3. 核心交互与操作
*   **任务驱动**: 页面的设计完全以“任务”为中心，所有模块最终都指向引导复核员去处理待办任务。
*   **效率与价值可视化**: 通过工作量趋势图和“AI结果纠错数”指标，让复核员能直观地看到自己的工作效率和为系统带来的价值。
*   **快速访问**: “待复核任务”列表提供了最直接的工作入口，减少了页面跳转和寻找任务的时间。

---